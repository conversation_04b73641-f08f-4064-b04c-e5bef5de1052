<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="shortcut icon" href="{{ asset('assets/images/logo-small.png') }}">
    <title>PWB LOGIN</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    @vite(['resources/css/login.css', 'resources/js/login/login.js', 'resources/js/login/superadmin-login.js'])
    <style>
        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            background: url("{{ asset('assets/images/438463.png') }}");
            background-size: cover;
            background-position: center;
            font-family: 'Segoe UI', sans-serif;
            position: relative;
            overflow: hidden;
        }

        #view-document-btn:hover {
            background-color: rgb(4, 58, 47);
            border-radius: 10px;
            padding: 2px 5px 2px 5px;
        }

        /* Modal Styling */
       #document-modal {
    display: none; /* Awalnya sembunyi */
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}


        #document-modal .modal-content {
            position: relative;
            width: 80%;
            height: 90%;
            background-color: white;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
            border-radius: 8px;
            overflow: hidden;
        }

        #close-modal-btn {
            position: absolute;
            max-width: 80px;
            top: 10px;
            right: 10px;
            background-color: #f8fbb7;
            color: black;
            border: none;
            padding: 5px 10px;
            cursor: pointer;
            z-index: 10;
        }
    </style>
</head>

<body>
    <div class="background-effects">
        <div class="particle" style="left: 10%; width: 80px; height: 80px; animation-delay: 0s"></div>
        <div class="particle" style="left: 30%; width: 120px; height: 120px; animation-delay: -5s"></div>
        <div class="particle" style="left: 70%; width: 100px; height: 100px; animation-delay: -10s"></div>
    </div>

    <div class="container">
        <div class="login-section">
            <div class="login-box">
                <h2>LOGIN</h2>
                @if ($errors->any())
                <div class="alert alert-danger" style="color: red; margin-top: 10px; background-color: #f8fbb7; margin-bottom: 10px; padding: 5px;">
                    {{ $errors->first() }}
                </div>
                @endif
                <form id="login-form" action="{{ route('login') }}" method="POST">
                    @csrf
                    <div class="input-group">
                        <i class="fas fa-user"></i>
                        <input type="text" name="username" id="username-input" placeholder="Username" required>
                    </div>
                    <div class="input-group" id="password-input-group">
                        <i class="fas fa-lock"></i>
                        <input type="password" name="password" id="password-input" placeholder="Password atau Kode" required>
                    </div>
                    <button type="submit">Masuk</button>
                </form>
                <div class="footer" style="margin-top: 15px; text-align: center; display: flex; justify-content: space-between;">
                    <a href="{{ route('token.login.form') }}" style="color:rgb(255, 255, 255); text-decoration: none; font-size: 14px;">
                        <i class="fas fa-key"></i> Login dengan Token
                    </a>
                    <a id="view-document-btn" style="color:rgb(228, 241, 164); text-decoration: none; font-size: 14px; cursor: pointer;">
                        Panduan Admin Site <i class="fas fa-book"></i>
                    </a>
                </div>
            </div>
        </div>
        <div class="chart-section">
            <img src="{{ asset('assets/images/6333220.png') }}" alt="Background Graphic">
        </div>
    </div>

    <!-- Modal -->
    <div id="document-modal">
        <div class="modal-content">
            <button class="btn btn-primary" id="close-modal-btn">Close</button>
            <iframe src="{{ asset('assets/InventoryGuidline.pdf') }}" style="width: 100%; height: 100%; border: none;"></iframe>
        </div>
    </div>

    <script>
        const modal = document.getElementById('document-modal');
        const openBtn = document.getElementById('view-document-btn');
        const closeBtn = document.getElementById('close-modal-btn');

        openBtn.addEventListener('click', () => {
            modal.style.display = 'flex';
        });

        closeBtn.addEventListener('click', () => {
            modal.style.display = 'none';
        });

        // Optional: Close modal when clicking outside the content
        window.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.style.display = 'none';
            }
        });
    </script>
</body>

</html>
