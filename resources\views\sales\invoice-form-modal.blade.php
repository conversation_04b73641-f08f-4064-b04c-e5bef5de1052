<!-- Invoice Form Modal -->
<div style="min-width: 100%" class="modal fade" id="invoice-form-modal" tabindex="-1" role="dialog"
    aria-labelledby="invoice-form-modal-label" aria-hidden="true">
    <div class="modal-dialog" role="document" style="margin: auto !important; min-width: 80%;">
        <div class="modal-content p-4">
            <div class="modal-header">
                <h5 class="modal-title" id="invoice-form-modal-label">Form Invoice</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="invoice-form">
                    <div class="row">
                        <div class="col">
                            <div id="selected-transactions-container" class="mb-3 d-none">
                                <label class="form-label">Selected Transactions</label>
                                <div id="selected-transactions-list" class="border p-2 rounded"
                                    style="max-height: 150px; overflow-y: auto;">
                                    <!-- Selected transactions will be displayed here -->
                                </div>
                            </div>
                            <input type="hidden" id="invoice-transaction-ids" name="transaction_ids">

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="invoice-customer" class="form-label">Customer</label>
                                    <input type="text" class="form-control" id="invoice-customer" name="customer"
                                        placeholder="Nama Customer">
                                </div>
                                <div class="col-md-6">
                                    <label for="invoice-location" class="form-label">Location</label>
                                    <input type="text" class="form-control" id="invoice-location" name="location"
                                        placeholder="Lokasi Perusahaan">
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="invoice-no" class="form-label">No. Invoice</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="invoice-no" name="no_invoice"
                                            placeholder="Nomor Invoice">
                                        <button class="btn btn-outline-secondary" type="button"
                                            id="generate-next-invoice-number">Auto</button>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="invoice-sn" class="form-label">Serial Number (SN)</label>
                                    <input type="text" class="form-control" id="invoice-sn" name="sn"
                                        placeholder="Serial Number Unit">
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="invoice-do-number" class="form-label">Nomor DO</label>
                                    <input type="text" class="form-control" id="invoice-do-number" name="trouble"
                                        placeholder="Nomor Delivery Order">
                                </div>
                                <div class="col-md-6">
                                    <label for="invoice-due-date" class="form-label">Tanggal Jatuh Tempo</label>
                                    <input type="date" class="form-control" id="invoice-due-date" name="due_date">
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <label for="invoice-lokasi" class="form-label">Lokasi Site</label>
                                    <input type="text" class="form-control" id="invoice-lokasi" name="lokasi"
                                        placeholder="Lokasi Spesifik Unit">
                                </div>
                                <div class="col-md-4">
                                    <label for="invoice-tanggal" class="form-label">Tanggal Invoice</label>
                                    <input type="date" class="form-control" id="invoice-tanggal" name="tanggal_invoice">
                                </div>
                                <div class="col-md-4">
                                    <label for="invoice-ppn" class="form-label">PPN (11%)</label>
                                    <input type="text" class="form-control" id="invoice-ppn" name="ppn" value="0.11">
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <label for="invoice-notes" class="form-label">Catatan</label>
                                    <textarea class="form-control" id="invoice-notes" name="notes" rows="3"
                                        placeholder="Tambahkan catatan untuk invoice ini (opsional)"></textarea>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <label for="signed-document" class="form-label">Dokumen Invoice Tertandatangani
                                        (PDF, max 10MB)</label>
                                    <input type="file" class="form-control" id="signed-document" name="signed_document"
                                        accept=".pdf">
                                    <div id="signed-document-preview" class="mt-2 d-none">
                                        <p>Dokumen saat ini: <span id="current-document-name"></span></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col">
                            <h5 class="h5 font-bold">DAFTAR PART</h5>
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <td>No</td>
                                        <td>Code Part</td>
                                        <td>Nama Part</td>
                                        <td>Jumlah</td>
                                        <td>Harga</td>
                                    </tr>
                                </thead>
                                <tbody id="daftarpartinvoice">

                                </tbody>
                            </table>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                <button type="button" class="btn btn-primary" id="save-invoice-btn">Review</button>
            </div>
        </div>
    </div>
</div>

<!-- Script for automatic due date calculation -->
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Add event listener for invoice date change
        const invoiceDateField = document.getElementById('invoice-tanggal');
        const dueDateField = document.getElementById('invoice-due-date');

        if (invoiceDateField) {
            invoiceDateField.addEventListener('change', function () {
                if (this.value && dueDateField) {
                    // Use timezone-safe date calculation
                    const formattedDueDate = window.DateUtils ?
                        window.DateUtils.addDaysToDate(this.value, 60) :
                        (() => {
                            const invoiceDate = new Date(this.value);
                            if (!isNaN(invoiceDate.getTime())) {
                                const dueDate = new Date(invoiceDate);
                                dueDate.setDate(dueDate.getDate() + 60);
                                return dueDate.toISOString().split('T')[0];
                            }
                            return '';
                        })();

                    if (formattedDueDate) {
                        dueDateField.value = formattedDueDate;
                    }
                }
            });
        }

        // Handle modal events to set default values only for new invoices
        const modal = document.getElementById('invoice-form-modal');
        if (modal) {
            modal.addEventListener('show.bs.modal', function (event) {
                // Check if this is for a new invoice (no invoice-id hidden field or it's empty)
                const invoiceIdField = document.getElementById('invoice-id');
                const isNewInvoice = !invoiceIdField || !invoiceIdField.value;

                if (isNewInvoice) {
                    // Set default values only for new invoices using timezone-safe methods
                    const formattedToday = window.DateUtils ?
                        window.DateUtils.getTodayFormatted() :
                        new Date().toISOString().split('T')[0];

                    // Set today's date as default for new invoice only if field is empty
                    if (invoiceDateField && !invoiceDateField.value) {
                        invoiceDateField.value = formattedToday;
                    }

                    // Set initial due date (60 days from today) only if field is empty
                    if (dueDateField && !dueDateField.value) {
                        const formattedDueDate = window.DateUtils ?
                            window.DateUtils.getDateFromToday(60) :
                            (() => {
                                const dueDate = new Date();
                                dueDate.setDate(dueDate.getDate() + 60);
                                return dueDate.toISOString().split('T')[0];
                            })();
                        dueDateField.value = formattedDueDate;
                    }
                }
            });
        }
    });
</script>