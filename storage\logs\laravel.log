[2025-07-03 08:26:50] local.INFO: User <PERSON><PERSON> logged in successfully. employee_id: 000012, site_id: WHO  
[2025-07-03 09:13:26] local.INFO: User pwb20212025 logged in successfully using token. employee_id: 666, site_id:   
[2025-07-03 09:13:27] local.INFO: Sites Data Request {"start_date":"2025-07-01","end_date":"2025-07-31","month":null,"division":null,"site":null} 
[2025-07-03 09:13:28] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-03 09:13:28] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-03 09:13:28] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-03 09:13:28] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-03 09:13:28] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-03 09:13:28] local.INFO: Sites Data Response {"count":5} 
[2025-07-03 09:13:28] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:13:34] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:13:34] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:13:40] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:13:40] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:15:45] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:15:45] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:16:22] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:16:22] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:16:29] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:16:29] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:16:32] local.INFO: Sites Data Request {"start_date":"2025-07-01","end_date":"2025-07-31","month":null,"division":null,"site":null} 
[2025-07-03 09:16:32] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-03 09:16:32] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-03 09:16:32] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-03 09:16:32] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-03 09:16:32] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-03 09:16:32] local.INFO: Sites Data Response {"count":5} 
[2025-07-03 09:16:33] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:17:20] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:17:20] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:18:44] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:18:45] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:19:30] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:19:30] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:20:44] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:20:45] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:20:49] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:20:49] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:22:58] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:22:58] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:24:54] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:24:54] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:25:12] local.INFO: Sites Data Request {"start_date":"2025-07-01","end_date":"2025-07-31","month":null,"division":null,"site":null} 
[2025-07-03 09:25:12] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-03 09:25:12] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-03 09:25:12] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-03 09:25:12] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-03 09:25:12] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-03 09:25:12] local.INFO: Sites Data Response {"count":5} 
[2025-07-03 09:25:13] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:25:15] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:25:15] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:25:30] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:25:31] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:27:01] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:27:01] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:27:09] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:27:10] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:30:41] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:30:42] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:30:45] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:30:45] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:31:12] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:31:13] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:32:00] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:32:00] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:32:09] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:32:10] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:32:23] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:32:23] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:33:10] local.INFO: Sites Data Request {"start_date":"2025-07-01","end_date":"2025-07-31","month":null,"division":null,"site":null} 
[2025-07-03 09:33:10] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-03 09:33:10] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-03 09:33:10] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-03 09:33:10] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-03 09:33:10] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-03 09:33:10] local.INFO: Sites Data Response {"count":5} 
[2025-07-03 09:33:10] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:33:14] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:33:14] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:33:16] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:33:16] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:35:37] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:35:38] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:35:54] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:35:54] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:36:59] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:37:00] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:37:04] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:37:05] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:37:27] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:37:28] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:37:29] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:37:45] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:37:46] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:37:52] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:37:53] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:38:01] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:38:03] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:38:03] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:38:20] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:38:20] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:38:34] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:38:35] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:38:49] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:38:50] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:38:51] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:38:52] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:39:50] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:39:52] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:40:00] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:40:02] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:40:03] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:40:16] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:40:17] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:40:25] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:40:25] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:40:26] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:40:28] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:40:30] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:40:31] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:40:31] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:40:32] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:41:26] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:41:27] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:41:33] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:41:33] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:41:48] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:41:49] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:43:37] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:43:37] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:43:49] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:43:50] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:44:52] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:44:52] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:45:00] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:45:00] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:45:11] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:45:13] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:45:15] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:45:17] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:45:39] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:45:40] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:45:41] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:45:43] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:45:44] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:45:50] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:45:50] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:45:57] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:45:58] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:47:08] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:47:09] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:47:11] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:47:11] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:47:22] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-03","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-03 23:59:59"} 
[2025-07-03 09:47:23] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-03 09:48:07] local.INFO: User tika logged in successfully. employee_id: 000012, site_id: WHO  
[2025-07-03 09:51:04] local.INFO: Unit Schedule getData - Session data: {"role":"adminsite","site_id":"PPA","request_site_id":null} 
[2025-07-03 09:51:04] local.INFO: Filtering by site for non-HO user: {"site_id":"PPA"} 
[2025-07-03 09:51:04] local.INFO: Units retrieved: {"count":108,"units":{"20":"HD7835","21":"HD78128","22":"HD78347","23":"H57105AMM","24":"HD7897","25":"HD78136","26":"HD78160","27":"HD78435","28":"H57154AMM","29":"HD78116","35":"HD78145","36":"HD78161","37":"HD78232","38":"H57089AMM","39":"HD78453","40":"H57159AMM","41":"H57018AMM","42":"HD78234","48":"C577026AMM","49":"HD78263","50":"H57146AMM","51":"HD78235","52":"HD78159","53":"HD78458","56":"H57008AMM","57":"HD7827","58":"H57024AMM","61":"HD78170","62":"HD78112","63":"H57015AMM","64":"HD78262","65":"H57023AMM","72":"HD78102","73":"HD78348","74":"H57145AMM","75":"H57094AMM","76":"H57025AMM","77":"HD7874","78":"H57084AMM","79":"HD7823","80":"HD78125","82":"HD78142","83":"HD78131","84":"HD78152","89":"HD78165","90":"HD7826","91":"HD78233","93":"HD78168","97":"H57088AMM","98":"HD78144","99":"H57021AMM","100":"HD78105","101":"HD78143","102":"HD78231","108":"HD78135","109":"HD78459","110":"H57148AMM","111":"HD78264","112":"H57117AMM","121":"HD7879","122":"H57155AMM","123":"H57019AMM","124":"HD78127","125":"HD7805","126":"H57129AMM","127":"HD78440","128":"H57029AMM","129":"HD78419","130":"HD7892","131":"HD78423","132":"H57026AMM","133":"HD78451","134":"HD78106","135":"HD7878","136":"H57134AMM","137":"H57017AMM","138":"HD78460","139":"H57004AMM","140":"HD7884","141":"H57075AMM","142":"HD78420","143":"H57153AMM","144":"HD78428","145":"HD78206","146":"H57030AMM","173":"HD78104","174":"HD78236","175":"HD78265","176":"H57001AMM","182":"H57137AMM","183":"H57125AMM","184":"H57009AMM","185":"H57007AMM","189":"HD78132","190":"HD78257","193":"HD78167","194":"HD78157","198":"HD78241","199":"HD78261","202":"HD78158","203":"HD78240","205":"HD78163","206":"HD78269","207":"HD78276","215":"H57013AMM","222":"HD78424","231":"H57126AMM","232":"HD78239"}} 
[2025-07-03 09:53:00] local.INFO: Unit Schedule getData - Session data: {"role":"adminsite","site_id":"PPA","request_site_id":null} 
[2025-07-03 09:53:00] local.INFO: Filtering by site for non-HO user: {"site_id":"PPA"} 
[2025-07-03 09:53:00] local.INFO: Units retrieved: {"count":108,"units":{"20":"HD7835","21":"HD78128","22":"HD78347","23":"H57105AMM","24":"HD7897","25":"HD78136","26":"HD78160","27":"HD78435","28":"H57154AMM","29":"HD78116","35":"HD78145","36":"HD78161","37":"HD78232","38":"H57089AMM","39":"HD78453","40":"H57159AMM","41":"H57018AMM","42":"HD78234","48":"C577026AMM","49":"HD78263","50":"H57146AMM","51":"HD78235","52":"HD78159","53":"HD78458","56":"H57008AMM","57":"HD7827","58":"H57024AMM","61":"HD78170","62":"HD78112","63":"H57015AMM","64":"HD78262","65":"H57023AMM","72":"HD78102","73":"HD78348","74":"H57145AMM","75":"H57094AMM","76":"H57025AMM","77":"HD7874","78":"H57084AMM","79":"HD7823","80":"HD78125","82":"HD78142","83":"HD78131","84":"HD78152","89":"HD78165","90":"HD7826","91":"HD78233","93":"HD78168","97":"H57088AMM","98":"HD78144","99":"H57021AMM","100":"HD78105","101":"HD78143","102":"HD78231","108":"HD78135","109":"HD78459","110":"H57148AMM","111":"HD78264","112":"H57117AMM","121":"HD7879","122":"H57155AMM","123":"H57019AMM","124":"HD78127","125":"HD7805","126":"H57129AMM","127":"HD78440","128":"H57029AMM","129":"HD78419","130":"HD7892","131":"HD78423","132":"H57026AMM","133":"HD78451","134":"HD78106","135":"HD7878","136":"H57134AMM","137":"H57017AMM","138":"HD78460","139":"H57004AMM","140":"HD7884","141":"H57075AMM","142":"HD78420","143":"H57153AMM","144":"HD78428","145":"HD78206","146":"H57030AMM","173":"HD78104","174":"HD78236","175":"HD78265","176":"H57001AMM","182":"H57137AMM","183":"H57125AMM","184":"H57009AMM","185":"H57007AMM","189":"HD78132","190":"HD78257","193":"HD78167","194":"HD78157","198":"HD78241","199":"HD78261","202":"HD78158","203":"HD78240","205":"HD78163","206":"HD78269","207":"HD78276","215":"H57013AMM","222":"HD78424","231":"H57126AMM","232":"HD78239"}} 
[2025-07-03 09:54:30] local.INFO: Unit Schedule getData - Session data: {"role":"adminsite","site_id":"PPA","request_site_id":null} 
[2025-07-03 09:54:30] local.INFO: Filtering by site for non-HO user: {"site_id":"PPA"} 
[2025-07-03 09:54:30] local.INFO: Units retrieved: {"count":108,"units":{"20":"HD7835","21":"HD78128","22":"HD78347","23":"H57105AMM","24":"HD7897","25":"HD78136","26":"HD78160","27":"HD78435","28":"H57154AMM","29":"HD78116","35":"HD78145","36":"HD78161","37":"HD78232","38":"H57089AMM","39":"HD78453","40":"H57159AMM","41":"H57018AMM","42":"HD78234","48":"C577026AMM","49":"HD78263","50":"H57146AMM","51":"HD78235","52":"HD78159","53":"HD78458","56":"H57008AMM","57":"HD7827","58":"H57024AMM","61":"HD78170","62":"HD78112","63":"H57015AMM","64":"HD78262","65":"H57023AMM","72":"HD78102","73":"HD78348","74":"H57145AMM","75":"H57094AMM","76":"H57025AMM","77":"HD7874","78":"H57084AMM","79":"HD7823","80":"HD78125","82":"HD78142","83":"HD78131","84":"HD78152","89":"HD78165","90":"HD7826","91":"HD78233","93":"HD78168","97":"H57088AMM","98":"HD78144","99":"H57021AMM","100":"HD78105","101":"HD78143","102":"HD78231","108":"HD78135","109":"HD78459","110":"H57148AMM","111":"HD78264","112":"H57117AMM","121":"HD7879","122":"H57155AMM","123":"H57019AMM","124":"HD78127","125":"HD7805","126":"H57129AMM","127":"HD78440","128":"H57029AMM","129":"HD78419","130":"HD7892","131":"HD78423","132":"H57026AMM","133":"HD78451","134":"HD78106","135":"HD7878","136":"H57134AMM","137":"H57017AMM","138":"HD78460","139":"H57004AMM","140":"HD7884","141":"H57075AMM","142":"HD78420","143":"H57153AMM","144":"HD78428","145":"HD78206","146":"H57030AMM","173":"HD78104","174":"HD78236","175":"HD78265","176":"H57001AMM","182":"H57137AMM","183":"H57125AMM","184":"H57009AMM","185":"H57007AMM","189":"HD78132","190":"HD78257","193":"HD78167","194":"HD78157","198":"HD78241","199":"HD78261","202":"HD78158","203":"HD78240","205":"HD78163","206":"HD78269","207":"HD78276","215":"H57013AMM","222":"HD78424","231":"H57126AMM","232":"HD78239"}} 
[2025-07-03 11:07:04] local.INFO: Unit Schedule getData - Session data: {"role":"adminsite","site_id":"PPA","request_site_id":null} 
[2025-07-03 11:07:04] local.INFO: Filtering by site for non-HO user: {"site_id":"PPA"} 
[2025-07-03 11:07:04] local.INFO: Units retrieved: {"count":108,"units":{"20":"HD7835","21":"HD78128","22":"HD78347","23":"H57105AMM","24":"HD7897","25":"HD78136","26":"HD78160","27":"HD78435","28":"H57154AMM","29":"HD78116","35":"HD78145","36":"HD78161","37":"HD78232","38":"H57089AMM","39":"HD78453","40":"H57159AMM","41":"H57018AMM","42":"HD78234","48":"C577026AMM","49":"HD78263","50":"H57146AMM","51":"HD78235","52":"HD78159","53":"HD78458","56":"H57008AMM","57":"HD7827","58":"H57024AMM","61":"HD78170","62":"HD78112","63":"H57015AMM","64":"HD78262","65":"H57023AMM","72":"HD78102","73":"HD78348","74":"H57145AMM","75":"H57094AMM","76":"H57025AMM","77":"HD7874","78":"H57084AMM","79":"HD7823","80":"HD78125","82":"HD78142","83":"HD78131","84":"HD78152","89":"HD78165","90":"HD7826","91":"HD78233","93":"HD78168","97":"H57088AMM","98":"HD78144","99":"H57021AMM","100":"HD78105","101":"HD78143","102":"HD78231","108":"HD78135","109":"HD78459","110":"H57148AMM","111":"HD78264","112":"H57117AMM","121":"HD7879","122":"H57155AMM","123":"H57019AMM","124":"HD78127","125":"HD7805","126":"H57129AMM","127":"HD78440","128":"H57029AMM","129":"HD78419","130":"HD7892","131":"HD78423","132":"H57026AMM","133":"HD78451","134":"HD78106","135":"HD7878","136":"H57134AMM","137":"H57017AMM","138":"HD78460","139":"H57004AMM","140":"HD7884","141":"H57075AMM","142":"HD78420","143":"H57153AMM","144":"HD78428","145":"HD78206","146":"H57030AMM","173":"HD78104","174":"HD78236","175":"HD78265","176":"H57001AMM","182":"H57137AMM","183":"H57125AMM","184":"H57009AMM","185":"H57007AMM","189":"HD78132","190":"HD78257","193":"HD78167","194":"HD78157","198":"HD78241","199":"HD78261","202":"HD78158","203":"HD78240","205":"HD78163","206":"HD78269","207":"HD78276","215":"H57013AMM","222":"HD78424","231":"H57126AMM","232":"HD78239"}} 
[2025-07-03 11:09:01] local.INFO: Unit Schedule getData - Session data: {"role":"adminsite","site_id":"PPA","request_site_id":null} 
[2025-07-03 11:09:01] local.INFO: Filtering by site for non-HO user: {"site_id":"PPA"} 
[2025-07-03 11:09:01] local.INFO: Units retrieved: {"count":108,"units":{"20":"HD7835","21":"HD78128","22":"HD78347","23":"H57105AMM","24":"HD7897","25":"HD78136","26":"HD78160","27":"HD78435","28":"H57154AMM","29":"HD78116","35":"HD78145","36":"HD78161","37":"HD78232","38":"H57089AMM","39":"HD78453","40":"H57159AMM","41":"H57018AMM","42":"HD78234","48":"C577026AMM","49":"HD78263","50":"H57146AMM","51":"HD78235","52":"HD78159","53":"HD78458","56":"H57008AMM","57":"HD7827","58":"H57024AMM","61":"HD78170","62":"HD78112","63":"H57015AMM","64":"HD78262","65":"H57023AMM","72":"HD78102","73":"HD78348","74":"H57145AMM","75":"H57094AMM","76":"H57025AMM","77":"HD7874","78":"H57084AMM","79":"HD7823","80":"HD78125","82":"HD78142","83":"HD78131","84":"HD78152","89":"HD78165","90":"HD7826","91":"HD78233","93":"HD78168","97":"H57088AMM","98":"HD78144","99":"H57021AMM","100":"HD78105","101":"HD78143","102":"HD78231","108":"HD78135","109":"HD78459","110":"H57148AMM","111":"HD78264","112":"H57117AMM","121":"HD7879","122":"H57155AMM","123":"H57019AMM","124":"HD78127","125":"HD7805","126":"H57129AMM","127":"HD78440","128":"H57029AMM","129":"HD78419","130":"HD7892","131":"HD78423","132":"H57026AMM","133":"HD78451","134":"HD78106","135":"HD7878","136":"H57134AMM","137":"H57017AMM","138":"HD78460","139":"H57004AMM","140":"HD7884","141":"H57075AMM","142":"HD78420","143":"H57153AMM","144":"HD78428","145":"HD78206","146":"H57030AMM","173":"HD78104","174":"HD78236","175":"HD78265","176":"H57001AMM","182":"H57137AMM","183":"H57125AMM","184":"H57009AMM","185":"H57007AMM","189":"HD78132","190":"HD78257","193":"HD78167","194":"HD78157","198":"HD78241","199":"HD78261","202":"HD78158","203":"HD78240","205":"HD78163","206":"HD78269","207":"HD78276","215":"H57013AMM","222":"HD78424","231":"H57126AMM","232":"HD78239"}} 
[2025-07-03 11:09:22] local.INFO: Unit Schedule getData - Session data: {"role":"adminsite","site_id":"PPA","request_site_id":null} 
[2025-07-03 11:09:22] local.INFO: Filtering by site for non-HO user: {"site_id":"PPA"} 
[2025-07-03 11:09:22] local.INFO: Units retrieved: {"count":108,"units":{"20":"HD7835","21":"HD78128","22":"HD78347","23":"H57105AMM","24":"HD7897","25":"HD78136","26":"HD78160","27":"HD78435","28":"H57154AMM","29":"HD78116","35":"HD78145","36":"HD78161","37":"HD78232","38":"H57089AMM","39":"HD78453","40":"H57159AMM","41":"H57018AMM","42":"HD78234","48":"C577026AMM","49":"HD78263","50":"H57146AMM","51":"HD78235","52":"HD78159","53":"HD78458","56":"H57008AMM","57":"HD7827","58":"H57024AMM","61":"HD78170","62":"HD78112","63":"H57015AMM","64":"HD78262","65":"H57023AMM","72":"HD78102","73":"HD78348","74":"H57145AMM","75":"H57094AMM","76":"H57025AMM","77":"HD7874","78":"H57084AMM","79":"HD7823","80":"HD78125","82":"HD78142","83":"HD78131","84":"HD78152","89":"HD78165","90":"HD7826","91":"HD78233","93":"HD78168","97":"H57088AMM","98":"HD78144","99":"H57021AMM","100":"HD78105","101":"HD78143","102":"HD78231","108":"HD78135","109":"HD78459","110":"H57148AMM","111":"HD78264","112":"H57117AMM","121":"HD7879","122":"H57155AMM","123":"H57019AMM","124":"HD78127","125":"HD7805","126":"H57129AMM","127":"HD78440","128":"H57029AMM","129":"HD78419","130":"HD7892","131":"HD78423","132":"H57026AMM","133":"HD78451","134":"HD78106","135":"HD7878","136":"H57134AMM","137":"H57017AMM","138":"HD78460","139":"H57004AMM","140":"HD7884","141":"H57075AMM","142":"HD78420","143":"H57153AMM","144":"HD78428","145":"HD78206","146":"H57030AMM","173":"HD78104","174":"HD78236","175":"HD78265","176":"H57001AMM","182":"H57137AMM","183":"H57125AMM","184":"H57009AMM","185":"H57007AMM","189":"HD78132","190":"HD78257","193":"HD78167","194":"HD78157","198":"HD78241","199":"HD78261","202":"HD78158","203":"HD78240","205":"HD78163","206":"HD78269","207":"HD78276","215":"H57013AMM","222":"HD78424","231":"H57126AMM","232":"HD78239"}} 
[2025-07-03 11:09:53] local.INFO: Unit Schedule getData - Session data: {"role":"adminsite","site_id":"PPA","request_site_id":null} 
[2025-07-03 11:09:53] local.INFO: Filtering by site for non-HO user: {"site_id":"PPA"} 
[2025-07-03 11:09:53] local.INFO: Units retrieved: {"count":108,"units":{"20":"HD7835","21":"HD78128","22":"HD78347","23":"H57105AMM","24":"HD7897","25":"HD78136","26":"HD78160","27":"HD78435","28":"H57154AMM","29":"HD78116","35":"HD78145","36":"HD78161","37":"HD78232","38":"H57089AMM","39":"HD78453","40":"H57159AMM","41":"H57018AMM","42":"HD78234","48":"C577026AMM","49":"HD78263","50":"H57146AMM","51":"HD78235","52":"HD78159","53":"HD78458","56":"H57008AMM","57":"HD7827","58":"H57024AMM","61":"HD78170","62":"HD78112","63":"H57015AMM","64":"HD78262","65":"H57023AMM","72":"HD78102","73":"HD78348","74":"H57145AMM","75":"H57094AMM","76":"H57025AMM","77":"HD7874","78":"H57084AMM","79":"HD7823","80":"HD78125","82":"HD78142","83":"HD78131","84":"HD78152","89":"HD78165","90":"HD7826","91":"HD78233","93":"HD78168","97":"H57088AMM","98":"HD78144","99":"H57021AMM","100":"HD78105","101":"HD78143","102":"HD78231","108":"HD78135","109":"HD78459","110":"H57148AMM","111":"HD78264","112":"H57117AMM","121":"HD7879","122":"H57155AMM","123":"H57019AMM","124":"HD78127","125":"HD7805","126":"H57129AMM","127":"HD78440","128":"H57029AMM","129":"HD78419","130":"HD7892","131":"HD78423","132":"H57026AMM","133":"HD78451","134":"HD78106","135":"HD7878","136":"H57134AMM","137":"H57017AMM","138":"HD78460","139":"H57004AMM","140":"HD7884","141":"H57075AMM","142":"HD78420","143":"H57153AMM","144":"HD78428","145":"HD78206","146":"H57030AMM","173":"HD78104","174":"HD78236","175":"HD78265","176":"H57001AMM","182":"H57137AMM","183":"H57125AMM","184":"H57009AMM","185":"H57007AMM","189":"HD78132","190":"HD78257","193":"HD78167","194":"HD78157","198":"HD78241","199":"HD78261","202":"HD78158","203":"HD78240","205":"HD78163","206":"HD78269","207":"HD78276","215":"H57013AMM","222":"HD78424","231":"H57126AMM","232":"HD78239"}} 
[2025-07-03 11:10:04] local.INFO: Unit Schedule getData - Session data: {"role":"adminsite","site_id":"PPA","request_site_id":null} 
[2025-07-03 11:10:04] local.INFO: Filtering by site for non-HO user: {"site_id":"PPA"} 
[2025-07-03 11:10:04] local.INFO: Units retrieved: {"count":108,"units":{"20":"HD7835","21":"HD78128","22":"HD78347","23":"H57105AMM","24":"HD7897","25":"HD78136","26":"HD78160","27":"HD78435","28":"H57154AMM","29":"HD78116","35":"HD78145","36":"HD78161","37":"HD78232","38":"H57089AMM","39":"HD78453","40":"H57159AMM","41":"H57018AMM","42":"HD78234","48":"C577026AMM","49":"HD78263","50":"H57146AMM","51":"HD78235","52":"HD78159","53":"HD78458","56":"H57008AMM","57":"HD7827","58":"H57024AMM","61":"HD78170","62":"HD78112","63":"H57015AMM","64":"HD78262","65":"H57023AMM","72":"HD78102","73":"HD78348","74":"H57145AMM","75":"H57094AMM","76":"H57025AMM","77":"HD7874","78":"H57084AMM","79":"HD7823","80":"HD78125","82":"HD78142","83":"HD78131","84":"HD78152","89":"HD78165","90":"HD7826","91":"HD78233","93":"HD78168","97":"H57088AMM","98":"HD78144","99":"H57021AMM","100":"HD78105","101":"HD78143","102":"HD78231","108":"HD78135","109":"HD78459","110":"H57148AMM","111":"HD78264","112":"H57117AMM","121":"HD7879","122":"H57155AMM","123":"H57019AMM","124":"HD78127","125":"HD7805","126":"H57129AMM","127":"HD78440","128":"H57029AMM","129":"HD78419","130":"HD7892","131":"HD78423","132":"H57026AMM","133":"HD78451","134":"HD78106","135":"HD7878","136":"H57134AMM","137":"H57017AMM","138":"HD78460","139":"H57004AMM","140":"HD7884","141":"H57075AMM","142":"HD78420","143":"H57153AMM","144":"HD78428","145":"HD78206","146":"H57030AMM","173":"HD78104","174":"HD78236","175":"HD78265","176":"H57001AMM","182":"H57137AMM","183":"H57125AMM","184":"H57009AMM","185":"H57007AMM","189":"HD78132","190":"HD78257","193":"HD78167","194":"HD78157","198":"HD78241","199":"HD78261","202":"HD78158","203":"HD78240","205":"HD78163","206":"HD78269","207":"HD78276","215":"H57013AMM","222":"HD78424","231":"H57126AMM","232":"HD78239"}} 
[2025-07-03 11:10:38] local.INFO: Unit Schedule getData - Session data: {"role":"adminsite","site_id":"PPA","request_site_id":null} 
[2025-07-03 11:10:38] local.INFO: Filtering by site for non-HO user: {"site_id":"PPA"} 
[2025-07-03 11:10:38] local.INFO: Units retrieved: {"count":108,"units":{"20":"HD7835","21":"HD78128","22":"HD78347","23":"H57105AMM","24":"HD7897","25":"HD78136","26":"HD78160","27":"HD78435","28":"H57154AMM","29":"HD78116","35":"HD78145","36":"HD78161","37":"HD78232","38":"H57089AMM","39":"HD78453","40":"H57159AMM","41":"H57018AMM","42":"HD78234","48":"C577026AMM","49":"HD78263","50":"H57146AMM","51":"HD78235","52":"HD78159","53":"HD78458","56":"H57008AMM","57":"HD7827","58":"H57024AMM","61":"HD78170","62":"HD78112","63":"H57015AMM","64":"HD78262","65":"H57023AMM","72":"HD78102","73":"HD78348","74":"H57145AMM","75":"H57094AMM","76":"H57025AMM","77":"HD7874","78":"H57084AMM","79":"HD7823","80":"HD78125","82":"HD78142","83":"HD78131","84":"HD78152","89":"HD78165","90":"HD7826","91":"HD78233","93":"HD78168","97":"H57088AMM","98":"HD78144","99":"H57021AMM","100":"HD78105","101":"HD78143","102":"HD78231","108":"HD78135","109":"HD78459","110":"H57148AMM","111":"HD78264","112":"H57117AMM","121":"HD7879","122":"H57155AMM","123":"H57019AMM","124":"HD78127","125":"HD7805","126":"H57129AMM","127":"HD78440","128":"H57029AMM","129":"HD78419","130":"HD7892","131":"HD78423","132":"H57026AMM","133":"HD78451","134":"HD78106","135":"HD7878","136":"H57134AMM","137":"H57017AMM","138":"HD78460","139":"H57004AMM","140":"HD7884","141":"H57075AMM","142":"HD78420","143":"H57153AMM","144":"HD78428","145":"HD78206","146":"H57030AMM","173":"HD78104","174":"HD78236","175":"HD78265","176":"H57001AMM","182":"H57137AMM","183":"H57125AMM","184":"H57009AMM","185":"H57007AMM","189":"HD78132","190":"HD78257","193":"HD78167","194":"HD78157","198":"HD78241","199":"HD78261","202":"HD78158","203":"HD78240","205":"HD78163","206":"HD78269","207":"HD78276","215":"H57013AMM","222":"HD78424","231":"H57126AMM","232":"HD78239"}} 
[2025-07-03 11:10:45] local.INFO: Unit Schedule getData - Session data: {"role":"adminsite","site_id":"PPA","request_site_id":null} 
[2025-07-03 11:10:45] local.INFO: Filtering by site for non-HO user: {"site_id":"PPA"} 
[2025-07-03 11:10:45] local.INFO: Units retrieved: {"count":108,"units":{"20":"HD7835","21":"HD78128","22":"HD78347","23":"H57105AMM","24":"HD7897","25":"HD78136","26":"HD78160","27":"HD78435","28":"H57154AMM","29":"HD78116","35":"HD78145","36":"HD78161","37":"HD78232","38":"H57089AMM","39":"HD78453","40":"H57159AMM","41":"H57018AMM","42":"HD78234","48":"C577026AMM","49":"HD78263","50":"H57146AMM","51":"HD78235","52":"HD78159","53":"HD78458","56":"H57008AMM","57":"HD7827","58":"H57024AMM","61":"HD78170","62":"HD78112","63":"H57015AMM","64":"HD78262","65":"H57023AMM","72":"HD78102","73":"HD78348","74":"H57145AMM","75":"H57094AMM","76":"H57025AMM","77":"HD7874","78":"H57084AMM","79":"HD7823","80":"HD78125","82":"HD78142","83":"HD78131","84":"HD78152","89":"HD78165","90":"HD7826","91":"HD78233","93":"HD78168","97":"H57088AMM","98":"HD78144","99":"H57021AMM","100":"HD78105","101":"HD78143","102":"HD78231","108":"HD78135","109":"HD78459","110":"H57148AMM","111":"HD78264","112":"H57117AMM","121":"HD7879","122":"H57155AMM","123":"H57019AMM","124":"HD78127","125":"HD7805","126":"H57129AMM","127":"HD78440","128":"H57029AMM","129":"HD78419","130":"HD7892","131":"HD78423","132":"H57026AMM","133":"HD78451","134":"HD78106","135":"HD7878","136":"H57134AMM","137":"H57017AMM","138":"HD78460","139":"H57004AMM","140":"HD7884","141":"H57075AMM","142":"HD78420","143":"H57153AMM","144":"HD78428","145":"HD78206","146":"H57030AMM","173":"HD78104","174":"HD78236","175":"HD78265","176":"H57001AMM","182":"H57137AMM","183":"H57125AMM","184":"H57009AMM","185":"H57007AMM","189":"HD78132","190":"HD78257","193":"HD78167","194":"HD78157","198":"HD78241","199":"HD78261","202":"HD78158","203":"HD78240","205":"HD78163","206":"HD78269","207":"HD78276","215":"H57013AMM","222":"HD78424","231":"H57126AMM","232":"HD78239"}} 
[2025-07-03 11:10:52] local.INFO: Unit Schedule getData - Session data: {"role":"adminsite","site_id":"PPA","request_site_id":null} 
[2025-07-03 11:10:53] local.INFO: Filtering by site for non-HO user: {"site_id":"PPA"} 
[2025-07-03 11:10:53] local.INFO: Units retrieved: {"count":108,"units":{"20":"HD7835","21":"HD78128","22":"HD78347","23":"H57105AMM","24":"HD7897","25":"HD78136","26":"HD78160","27":"HD78435","28":"H57154AMM","29":"HD78116","35":"HD78145","36":"HD78161","37":"HD78232","38":"H57089AMM","39":"HD78453","40":"H57159AMM","41":"H57018AMM","42":"HD78234","48":"C577026AMM","49":"HD78263","50":"H57146AMM","51":"HD78235","52":"HD78159","53":"HD78458","56":"H57008AMM","57":"HD7827","58":"H57024AMM","61":"HD78170","62":"HD78112","63":"H57015AMM","64":"HD78262","65":"H57023AMM","72":"HD78102","73":"HD78348","74":"H57145AMM","75":"H57094AMM","76":"H57025AMM","77":"HD7874","78":"H57084AMM","79":"HD7823","80":"HD78125","82":"HD78142","83":"HD78131","84":"HD78152","89":"HD78165","90":"HD7826","91":"HD78233","93":"HD78168","97":"H57088AMM","98":"HD78144","99":"H57021AMM","100":"HD78105","101":"HD78143","102":"HD78231","108":"HD78135","109":"HD78459","110":"H57148AMM","111":"HD78264","112":"H57117AMM","121":"HD7879","122":"H57155AMM","123":"H57019AMM","124":"HD78127","125":"HD7805","126":"H57129AMM","127":"HD78440","128":"H57029AMM","129":"HD78419","130":"HD7892","131":"HD78423","132":"H57026AMM","133":"HD78451","134":"HD78106","135":"HD7878","136":"H57134AMM","137":"H57017AMM","138":"HD78460","139":"H57004AMM","140":"HD7884","141":"H57075AMM","142":"HD78420","143":"H57153AMM","144":"HD78428","145":"HD78206","146":"H57030AMM","173":"HD78104","174":"HD78236","175":"HD78265","176":"H57001AMM","182":"H57137AMM","183":"H57125AMM","184":"H57009AMM","185":"H57007AMM","189":"HD78132","190":"HD78257","193":"HD78167","194":"HD78157","198":"HD78241","199":"HD78261","202":"HD78158","203":"HD78240","205":"HD78163","206":"HD78269","207":"HD78276","215":"H57013AMM","222":"HD78424","231":"H57126AMM","232":"HD78239"}} 
[2025-07-03 11:11:08] local.INFO: Unit Schedule getData - Session data: {"role":"adminsite","site_id":"PPA","request_site_id":null} 
[2025-07-03 11:11:08] local.INFO: Filtering by site for non-HO user: {"site_id":"PPA"} 
[2025-07-03 11:11:08] local.INFO: Units retrieved: {"count":108,"units":{"20":"HD7835","21":"HD78128","22":"HD78347","23":"H57105AMM","24":"HD7897","25":"HD78136","26":"HD78160","27":"HD78435","28":"H57154AMM","29":"HD78116","35":"HD78145","36":"HD78161","37":"HD78232","38":"H57089AMM","39":"HD78453","40":"H57159AMM","41":"H57018AMM","42":"HD78234","48":"C577026AMM","49":"HD78263","50":"H57146AMM","51":"HD78235","52":"HD78159","53":"HD78458","56":"H57008AMM","57":"HD7827","58":"H57024AMM","61":"HD78170","62":"HD78112","63":"H57015AMM","64":"HD78262","65":"H57023AMM","72":"HD78102","73":"HD78348","74":"H57145AMM","75":"H57094AMM","76":"H57025AMM","77":"HD7874","78":"H57084AMM","79":"HD7823","80":"HD78125","82":"HD78142","83":"HD78131","84":"HD78152","89":"HD78165","90":"HD7826","91":"HD78233","93":"HD78168","97":"H57088AMM","98":"HD78144","99":"H57021AMM","100":"HD78105","101":"HD78143","102":"HD78231","108":"HD78135","109":"HD78459","110":"H57148AMM","111":"HD78264","112":"H57117AMM","121":"HD7879","122":"H57155AMM","123":"H57019AMM","124":"HD78127","125":"HD7805","126":"H57129AMM","127":"HD78440","128":"H57029AMM","129":"HD78419","130":"HD7892","131":"HD78423","132":"H57026AMM","133":"HD78451","134":"HD78106","135":"HD7878","136":"H57134AMM","137":"H57017AMM","138":"HD78460","139":"H57004AMM","140":"HD7884","141":"H57075AMM","142":"HD78420","143":"H57153AMM","144":"HD78428","145":"HD78206","146":"H57030AMM","173":"HD78104","174":"HD78236","175":"HD78265","176":"H57001AMM","182":"H57137AMM","183":"H57125AMM","184":"H57009AMM","185":"H57007AMM","189":"HD78132","190":"HD78257","193":"HD78167","194":"HD78157","198":"HD78241","199":"HD78261","202":"HD78158","203":"HD78240","205":"HD78163","206":"HD78269","207":"HD78276","215":"H57013AMM","222":"HD78424","231":"H57126AMM","232":"HD78239"}} 
[2025-07-03 11:11:11] local.INFO: Unit Schedule getData - Session data: {"role":"adminsite","site_id":"PPA","request_site_id":null} 
[2025-07-03 11:11:11] local.INFO: Filtering by site for non-HO user: {"site_id":"PPA"} 
[2025-07-03 11:11:11] local.INFO: Units retrieved: {"count":108,"units":{"20":"HD7835","21":"HD78128","22":"HD78347","23":"H57105AMM","24":"HD7897","25":"HD78136","26":"HD78160","27":"HD78435","28":"H57154AMM","29":"HD78116","35":"HD78145","36":"HD78161","37":"HD78232","38":"H57089AMM","39":"HD78453","40":"H57159AMM","41":"H57018AMM","42":"HD78234","48":"C577026AMM","49":"HD78263","50":"H57146AMM","51":"HD78235","52":"HD78159","53":"HD78458","56":"H57008AMM","57":"HD7827","58":"H57024AMM","61":"HD78170","62":"HD78112","63":"H57015AMM","64":"HD78262","65":"H57023AMM","72":"HD78102","73":"HD78348","74":"H57145AMM","75":"H57094AMM","76":"H57025AMM","77":"HD7874","78":"H57084AMM","79":"HD7823","80":"HD78125","82":"HD78142","83":"HD78131","84":"HD78152","89":"HD78165","90":"HD7826","91":"HD78233","93":"HD78168","97":"H57088AMM","98":"HD78144","99":"H57021AMM","100":"HD78105","101":"HD78143","102":"HD78231","108":"HD78135","109":"HD78459","110":"H57148AMM","111":"HD78264","112":"H57117AMM","121":"HD7879","122":"H57155AMM","123":"H57019AMM","124":"HD78127","125":"HD7805","126":"H57129AMM","127":"HD78440","128":"H57029AMM","129":"HD78419","130":"HD7892","131":"HD78423","132":"H57026AMM","133":"HD78451","134":"HD78106","135":"HD7878","136":"H57134AMM","137":"H57017AMM","138":"HD78460","139":"H57004AMM","140":"HD7884","141":"H57075AMM","142":"HD78420","143":"H57153AMM","144":"HD78428","145":"HD78206","146":"H57030AMM","173":"HD78104","174":"HD78236","175":"HD78265","176":"H57001AMM","182":"H57137AMM","183":"H57125AMM","184":"H57009AMM","185":"H57007AMM","189":"HD78132","190":"HD78257","193":"HD78167","194":"HD78157","198":"HD78241","199":"HD78261","202":"HD78158","203":"HD78240","205":"HD78163","206":"HD78269","207":"HD78276","215":"H57013AMM","222":"HD78424","231":"H57126AMM","232":"HD78239"}} 
[2025-07-03 11:11:14] local.INFO: Unit Schedule getData - Session data: {"role":"adminsite","site_id":"PPA","request_site_id":null} 
[2025-07-03 11:11:14] local.INFO: Filtering by site for non-HO user: {"site_id":"PPA"} 
[2025-07-03 11:11:14] local.INFO: Units retrieved: {"count":108,"units":{"20":"HD7835","21":"HD78128","22":"HD78347","23":"H57105AMM","24":"HD7897","25":"HD78136","26":"HD78160","27":"HD78435","28":"H57154AMM","29":"HD78116","35":"HD78145","36":"HD78161","37":"HD78232","38":"H57089AMM","39":"HD78453","40":"H57159AMM","41":"H57018AMM","42":"HD78234","48":"C577026AMM","49":"HD78263","50":"H57146AMM","51":"HD78235","52":"HD78159","53":"HD78458","56":"H57008AMM","57":"HD7827","58":"H57024AMM","61":"HD78170","62":"HD78112","63":"H57015AMM","64":"HD78262","65":"H57023AMM","72":"HD78102","73":"HD78348","74":"H57145AMM","75":"H57094AMM","76":"H57025AMM","77":"HD7874","78":"H57084AMM","79":"HD7823","80":"HD78125","82":"HD78142","83":"HD78131","84":"HD78152","89":"HD78165","90":"HD7826","91":"HD78233","93":"HD78168","97":"H57088AMM","98":"HD78144","99":"H57021AMM","100":"HD78105","101":"HD78143","102":"HD78231","108":"HD78135","109":"HD78459","110":"H57148AMM","111":"HD78264","112":"H57117AMM","121":"HD7879","122":"H57155AMM","123":"H57019AMM","124":"HD78127","125":"HD7805","126":"H57129AMM","127":"HD78440","128":"H57029AMM","129":"HD78419","130":"HD7892","131":"HD78423","132":"H57026AMM","133":"HD78451","134":"HD78106","135":"HD7878","136":"H57134AMM","137":"H57017AMM","138":"HD78460","139":"H57004AMM","140":"HD7884","141":"H57075AMM","142":"HD78420","143":"H57153AMM","144":"HD78428","145":"HD78206","146":"H57030AMM","173":"HD78104","174":"HD78236","175":"HD78265","176":"H57001AMM","182":"H57137AMM","183":"H57125AMM","184":"H57009AMM","185":"H57007AMM","189":"HD78132","190":"HD78257","193":"HD78167","194":"HD78157","198":"HD78241","199":"HD78261","202":"HD78158","203":"HD78240","205":"HD78163","206":"HD78269","207":"HD78276","215":"H57013AMM","222":"HD78424","231":"H57126AMM","232":"HD78239"}} 
[2025-07-03 11:11:19] local.INFO: Unit Schedule getData - Session data: {"role":"adminsite","site_id":"PPA","request_site_id":null} 
[2025-07-03 11:11:19] local.INFO: Filtering by site for non-HO user: {"site_id":"PPA"} 
[2025-07-03 11:11:19] local.INFO: Units retrieved: {"count":108,"units":{"20":"HD7835","21":"HD78128","22":"HD78347","23":"H57105AMM","24":"HD7897","25":"HD78136","26":"HD78160","27":"HD78435","28":"H57154AMM","29":"HD78116","35":"HD78145","36":"HD78161","37":"HD78232","38":"H57089AMM","39":"HD78453","40":"H57159AMM","41":"H57018AMM","42":"HD78234","48":"C577026AMM","49":"HD78263","50":"H57146AMM","51":"HD78235","52":"HD78159","53":"HD78458","56":"H57008AMM","57":"HD7827","58":"H57024AMM","61":"HD78170","62":"HD78112","63":"H57015AMM","64":"HD78262","65":"H57023AMM","72":"HD78102","73":"HD78348","74":"H57145AMM","75":"H57094AMM","76":"H57025AMM","77":"HD7874","78":"H57084AMM","79":"HD7823","80":"HD78125","82":"HD78142","83":"HD78131","84":"HD78152","89":"HD78165","90":"HD7826","91":"HD78233","93":"HD78168","97":"H57088AMM","98":"HD78144","99":"H57021AMM","100":"HD78105","101":"HD78143","102":"HD78231","108":"HD78135","109":"HD78459","110":"H57148AMM","111":"HD78264","112":"H57117AMM","121":"HD7879","122":"H57155AMM","123":"H57019AMM","124":"HD78127","125":"HD7805","126":"H57129AMM","127":"HD78440","128":"H57029AMM","129":"HD78419","130":"HD7892","131":"HD78423","132":"H57026AMM","133":"HD78451","134":"HD78106","135":"HD7878","136":"H57134AMM","137":"H57017AMM","138":"HD78460","139":"H57004AMM","140":"HD7884","141":"H57075AMM","142":"HD78420","143":"H57153AMM","144":"HD78428","145":"HD78206","146":"H57030AMM","173":"HD78104","174":"HD78236","175":"HD78265","176":"H57001AMM","182":"H57137AMM","183":"H57125AMM","184":"H57009AMM","185":"H57007AMM","189":"HD78132","190":"HD78257","193":"HD78167","194":"HD78157","198":"HD78241","199":"HD78261","202":"HD78158","203":"HD78240","205":"HD78163","206":"HD78269","207":"HD78276","215":"H57013AMM","222":"HD78424","231":"H57126AMM","232":"HD78239"}} 
[2025-07-03 11:11:40] local.INFO: Unit Schedule getData - Session data: {"role":"adminsite","site_id":"PPA","request_site_id":null} 
[2025-07-03 11:11:40] local.INFO: Filtering by site for non-HO user: {"site_id":"PPA"} 
[2025-07-03 11:11:40] local.INFO: Units retrieved: {"count":108,"units":{"20":"HD7835","21":"HD78128","22":"HD78347","23":"H57105AMM","24":"HD7897","25":"HD78136","26":"HD78160","27":"HD78435","28":"H57154AMM","29":"HD78116","35":"HD78145","36":"HD78161","37":"HD78232","38":"H57089AMM","39":"HD78453","40":"H57159AMM","41":"H57018AMM","42":"HD78234","48":"C577026AMM","49":"HD78263","50":"H57146AMM","51":"HD78235","52":"HD78159","53":"HD78458","56":"H57008AMM","57":"HD7827","58":"H57024AMM","61":"HD78170","62":"HD78112","63":"H57015AMM","64":"HD78262","65":"H57023AMM","72":"HD78102","73":"HD78348","74":"H57145AMM","75":"H57094AMM","76":"H57025AMM","77":"HD7874","78":"H57084AMM","79":"HD7823","80":"HD78125","82":"HD78142","83":"HD78131","84":"HD78152","89":"HD78165","90":"HD7826","91":"HD78233","93":"HD78168","97":"H57088AMM","98":"HD78144","99":"H57021AMM","100":"HD78105","101":"HD78143","102":"HD78231","108":"HD78135","109":"HD78459","110":"H57148AMM","111":"HD78264","112":"H57117AMM","121":"HD7879","122":"H57155AMM","123":"H57019AMM","124":"HD78127","125":"HD7805","126":"H57129AMM","127":"HD78440","128":"H57029AMM","129":"HD78419","130":"HD7892","131":"HD78423","132":"H57026AMM","133":"HD78451","134":"HD78106","135":"HD7878","136":"H57134AMM","137":"H57017AMM","138":"HD78460","139":"H57004AMM","140":"HD7884","141":"H57075AMM","142":"HD78420","143":"H57153AMM","144":"HD78428","145":"HD78206","146":"H57030AMM","173":"HD78104","174":"HD78236","175":"HD78265","176":"H57001AMM","182":"H57137AMM","183":"H57125AMM","184":"H57009AMM","185":"H57007AMM","189":"HD78132","190":"HD78257","193":"HD78167","194":"HD78157","198":"HD78241","199":"HD78261","202":"HD78158","203":"HD78240","205":"HD78163","206":"HD78269","207":"HD78276","215":"H57013AMM","222":"HD78424","231":"H57126AMM","232":"HD78239"}} 
[2025-07-03 11:12:27] local.ERROR: Unit search error: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'unit_model' in 'field list' (Connection: mysql, SQL: select `id`, `unit_code`, `unit_model`, `unit_type`, `site_id` from `units` where `site_id` = PPA and (`unit_code` LIKE %h% or `unit_model` LIKE %h% or `unit_type` LIKE %h%) and exists (select * from `daily_reports` where `units`.`id` = `daily_reports`.`unit_id`) order by `unit_code` asc limit 15)  
[2025-07-03 11:12:29] local.ERROR: Unit search error: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'unit_model' in 'field list' (Connection: mysql, SQL: select `id`, `unit_code`, `unit_model`, `unit_type`, `site_id` from `units` where `site_id` = PPA and (`unit_code` LIKE %hd% or `unit_model` LIKE %hd% or `unit_type` LIKE %hd%) and exists (select * from `daily_reports` where `units`.`id` = `daily_reports`.`unit_id`) order by `unit_code` asc limit 15)  
[2025-07-03 11:12:34] local.ERROR: Unit search error: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'unit_model' in 'field list' (Connection: mysql, SQL: select `id`, `unit_code`, `unit_model`, `unit_type`, `site_id` from `units` where `site_id` = PPA and (`unit_code` LIKE %hd8% or `unit_model` LIKE %hd8% or `unit_type` LIKE %hd8%) and exists (select * from `daily_reports` where `units`.`id` = `daily_reports`.`unit_id`) order by `unit_code` asc limit 15)  
[2025-07-03 11:12:36] local.ERROR: Unit search error: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'unit_model' in 'field list' (Connection: mysql, SQL: select `id`, `unit_code`, `unit_model`, `unit_type`, `site_id` from `units` where `site_id` = PPA and (`unit_code` LIKE %h% or `unit_model` LIKE %h% or `unit_type` LIKE %h%) and exists (select * from `daily_reports` where `units`.`id` = `daily_reports`.`unit_id`) order by `unit_code` asc limit 15)  
[2025-07-03 11:12:37] local.ERROR: Unit search error: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'unit_model' in 'field list' (Connection: mysql, SQL: select `id`, `unit_code`, `unit_model`, `unit_type`, `site_id` from `units` where `site_id` = PPA and (`unit_code` LIKE %hd% or `unit_model` LIKE %hd% or `unit_type` LIKE %hd%) and exists (select * from `daily_reports` where `units`.`id` = `daily_reports`.`unit_id`) order by `unit_code` asc limit 15)  
