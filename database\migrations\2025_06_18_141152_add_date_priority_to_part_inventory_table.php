<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::table('part_inventories', function (Blueprint $table) {
            $table->date('date_priority')->nullable()->after('priority'); 
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        Schema::table('part_inventories', function (Blueprint $table) {
            $table->dropColumn('date_priority');
        });
    }
};
