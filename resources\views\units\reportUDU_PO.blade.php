<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>Berita Acara Pemakaian Part</title>
    <style>
        body {
            font-family: <PERSON><PERSON>, 'Times New Roman', Times, serif;
            font-size: 11px;
            line-height: 1.4;
            margin: 20px;
        }

        /* Header styles */
        .header-container {
            width: 100%;
            margin-bottom: 5px;
        }

        .logo-left {
            float: left;
            text-align: center;
            width: 25%;
        }

        .title-center {
            width: 100%;
            text-align: center;
            margin: 0px 0;
        }

        .right {
            width: 90%;
            float: right;
            text-align: right;
        }

        .title-right {
            margin: 0px auto;
            width: 80%;
            text-align: center;
            display: block;
        }

        .header-title {
            background-color:rgb(90, 58, 156);
            color: white;
            padding: 8px;
            font-size: 14px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 15px;
        }

        .bapp-number {
            position: absolute;
            top: 20px;
            right: 20px;
            border: 1px solid #000;
            padding: 5px 10px;
            font-weight: bold;
        }

        /* Customer info table */
        .customer-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }

        .customer-table td {
            border: 1px solid #000;
            padding: 1px;
            padding-left: 4px;
        }

        .customer-table .label {
            width: 10%;
            font-weight: normal;
        }

        /* Main table styles */
        .main-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .main-table th,
        .main-table td {
            border: 1px solid #000;
            padding: 1px;
            text-align: center;
            font-size: 10px;
        }

        .main-table th {
            background-color: #4682b4;
            color: white;
            font-weight: bold;
        }

        .main-table tr:nth-child(even) {
            background-color: #f2f2f2;
        }

        /* Total section */
        .total-section {
            width: 30%;
            float: right;
            margin-bottom: 20px;
        }

        .total-table {
            width: 100%;
            border-collapse: collapse;
        }

        .total-table td {
            border: 1px solid #000;
            padding: 1px;
            text-align: left;
        }

        .total-table .total-label {
            font-weight: bold;
        }

        /* Signature section */
        .signature-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }

        .signature-table td {
            width: 50%;
            text-align: center;
            vertical-align: bottom;
            font-weight: bold;
            font-size: 10px;
        }

        .signature-stamp {
            width: 100px;
            height: 100px;
            margin: 0 auto;
            position: relative;
        }

        .clearfix::after {
            content: "";
            clear: both;
            display: table;
        }

        /* Adjust for landscape orientation */
        @page {
            size: landscape;
        }
    </style>
</head>

<body>
    <!-- Header with logo and title -->

    <div class="header-container clearfix">
        <div class="logo-left" style="text-align: center;">
            <img src="{{ public_path('assets/images/logo-small.png') }}" alt="PWB LOGO" style="width: 50px;">
            <p><strong>PT PUTERA WIBOWO BORNEO</strong></p>
        </div>
        <div class="right">
            <div class="title-right">
                <div class="header-title">BERITA ACARA PEMAKAIAN PART</div>
            </div>
        </div>
    </div>

    <!-- Customer Information -->
    <table class="customer-table">
        <tr>
            <td colspan="2" style="border: 0;"></td>
             <td class="label">No. QTN</td>
            <td style="width: 20%;">{{ $transactions[0]->do_number ?? ' ' }}</td>
        </tr>
        <tr>
            <td class="label">CUSTOMER</td>
            <td>PT. UNGGUL DINAMIKA UTAMA</td>
            <td class="label">CONTACT</td>
            <td style="width: 20%;">{{ $transactions[0]->contact ?? '' }}</td>
        </tr>
        <tr>
            <td class="label">CODE AREA</td>
            <td>{{ $transactions[0]->sitework ?? '' }}</td>
            <td class="label">PHONE</td>
            <td>{{ $transactions[0]->phone ?? '' }}</td>
        </tr>
    </table>

    <!-- Main Content Table -->
    <table class="main-table">
        <thead>
            <tr>
                <th style="width:3%;">NO</th>
                <th style="width:8%;">DATE</th>
                <th style="width:8%;">NO PR</th>
                <!--<th style="width:8%;">NO QTN</th>-->
                <th style="width:8%;">UNIT</th>
                <th style="width:12%;">PART NUMBER</th>
                <th style="width:20%;">DESCRIPTION</th>
                <th style="width:3%;">QTY</th>
                <th style="width:3%;">UOM</th>
                <th style="width:9%;">UNIT PRICE</th>
                <th style="width:9%;">UNIT PRICE (DISC 10%)</th>
                <th style="width:9%;">TOTAL PRICE</th>
            </tr>
        </thead>
        <tbody>
            @if(count($transactions) > 0)
            @php
            $no = 1;
            $totalAmount = 0;
            @endphp

            @foreach($transactions as $transaction)
            @php
            $transactionDate = \Carbon\Carbon::parse($transaction->created_at)->format('d-m-Y');
            // Sort parts by ID
            $sortedParts = $transaction->parts->sortBy('id');
            @endphp

            @foreach($sortedParts as $part)
            @php
            $unitPrice = $part->price;
            $discountedPrice = $unitPrice * 0.9; // 10% discount
            $subtotal = $discountedPrice * $part->quantity;
            $totalAmount += $subtotal;
            @endphp
            <tr>
                <td>{{ $no++ }}</td>
                <td>{{ $transactionDate }}</td>
                <td>{{ $transaction->wo_number }}</td>
                <!--<td>{{ explode('/', $transaction->do_number)[0] }}</td>-->
                <td>{{ $transaction->unit->unit_code }}</td>
                <td>{{ $part->partInventory->part->part_code }}</td>
                <td style="text-align: left;">{{ $part->partInventory->part->part_name }}</td>
                <td>{{ $part->quantity }}</td>
                <td>{{ $part->partInventory->oum ?? $part->eum }}</td>
                <td style="text-align: right;">Rp {{ number_format($unitPrice, 0, ',', '.') }}</td>
                <td style="text-align: right;">Rp {{ number_format($discountedPrice, 0, ',', '.') }}</td>
                <td style="text-align: right;">Rp {{ number_format($subtotal, 0, ',', '.') }}</td>
            </tr>
            @endforeach
            @endforeach

            <!-- Add empty rows to make the table look complete -->
                @else
                @for($i = 1; $i <= 10; $i++)
                    <tr>
                    <td>{{ $i }}</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    </tr>
                    @endfor
                    @endif
                    <tr>
                        <td colspan="9" style="border: none; background-color: #ffff;"></td>
                        <td colspan="1" style="text-align: right; font-weight: bold;">TOTAL</td>
                        <td style="text-align: right;">Rp {{ number_format($totalAmount ?? 0, 0, ',', '.') }}</td>
                    </tr>
                    <tr>
                        <td colspan="9" style="border: none; background-color: #ffff;"></td>
                        <td colspan="1" style="text-align: right; font-weight: bold;">PPN 11%</td>
                        <td style="text-align: right;">Rp {{ number_format(($totalAmount ?? 0) * 0.11, 0, ',', '.') }}</td>
                    </tr>
                    <tr>
                        <td colspan="9" style="border: none; background-color: #ffff;"></td>
                        <td colspan="1" style="text-align: right; font-weight: bold;">GRAND TOTAL</td>
                        <td style="text-align: right;">Rp {{ number_format(($totalAmount ?? 0) * 1.11, 0, ',', '.') }}</td>
                    </tr>
        </tbody>
    </table>

    <!-- Signature Section -->
    <div class="clearfix">
        <table class="signature-table">
            <tr>
                <td>
                    <div>Dibuat oleh,</div>
                </td>
                <td>
                    <div>Disetujui oleh,</div>
                </td>
            </tr>
            <tr>
                <td style="padding-top: 60px;">
                    <div>{{ $transactions[0]->contact ?? '' }}</div>
                    <div>Organinator site Putera Wibowo Borneo</div>
                </td>
                <td style="padding-top: 60px;">
                    <div>Parulian H.L. Tobing</div>
                    <div>HOD Plans PT. Unggul Dinamika Utama</div>
                </td>
            </tr>
        </table>
    </div>
</body>

</html>

