# Backlog Auto-Population Test Plan

## Test Cases

### 1. Basic Auto-Population Test
**Steps:**
1. Open daily reports page
2. Click "Add New Report" button
3. Select a unit from the dropdown
4. Enter HM value (e.g., 1500.5)
5. Click "Tambah Backlog" toggle button

**Expected Results:**
- Backlog section should open
- Unit Code field should be auto-populated with selected unit's code
- Unit Code field should be read-only (grayed out)
- HM Found field should be auto-populated with the HM value from daily report
- HM Found field should be read-only (grayed out)
- Helper text should show "Otomatis diisi dari unit daily report" and "Otomatis diisi dari HM daily report"

### 2. Validation Test - Missing Unit
**Steps:**
1. Open daily reports page
2. Click "Add New Report" button
3. Do NOT select a unit
4. Click "Tambah Backlog" toggle button

**Expected Results:**
- <PERSON><PERSON> should appear: "Silakan pilih unit terlebih dahulu sebelum membuat backlog"
- Backlog section should NOT open

### 3. Validation Test - Missing HM
**Steps:**
1. Open daily reports page
2. Click "Add New Report" button
3. Select a unit from the dropdown
4. Leave HM field empty
5. Click "Tambah Backlog" toggle button

**Expected Results:**
- <PERSON><PERSON> should appear: "Silakan isi HM terlebih dahulu sebelum membuat backlog"
- Backlog section should NOT open

### 4. Dynamic Update Test
**Steps:**
1. Open daily reports page
2. Click "Add New Report" button
3. Select a unit from the dropdown
4. Enter HM value
5. Click "Tambah Backlog" toggle button
6. Change the unit selection in the daily report
7. Change the HM value in the daily report

**Expected Results:**
- When unit changes, backlog unit code should update automatically
- When HM changes, backlog HM found should update automatically
- Fields should remain read-only

### 5. Form Reset Test
**Steps:**
1. Open daily reports page
2. Click "Add New Report" button
3. Select a unit and enter HM
4. Open backlog section
5. Close backlog section
6. Open backlog section again

**Expected Results:**
- When closing backlog section, fields should reset
- When reopening, fields should be auto-populated again from current daily report values
- Read-only state should be properly applied

### 6. Complete Form Submission Test
**Steps:**
1. Fill out complete daily report form with unit and HM
2. Open backlog section
3. Fill required backlog fields:
   - Problem Description
   - Backlog Job
   - Status (default OPEN)
   - Add at least one part
4. Submit the form

**Expected Results:**
- Form should submit successfully
- Both daily report and backlog should be created
- Backlog should have unit_code from daily report's unit
- Backlog should have hm_found from daily report's HM value

## Implementation Notes

### JavaScript Changes Made:
1. `toggleBacklogSection()` - Added validation and auto-population
2. `populateBacklogFromDailyReport()` - New function to populate fields
3. `updateBacklogFromDailyReport()` - New function to update when daily report changes
4. `resetBacklogForm()` - Updated to handle read-only state
5. Added event listeners for unit selection and HM changes

### Controller Changes Made:
1. Updated validation rules to make unit_code optional for backlog
2. Modified backlog creation to use unit from daily report
3. Auto-populate HM from daily report if not provided in backlog

### HTML Changes Made:
1. Added helper text to indicate auto-population
2. Fields will be made read-only via JavaScript

## Testing Environment
- Test on Chrome, Firefox, and Edge browsers
- Test with different unit types and HM values
- Test form validation scenarios
- Test with network delays/errors
