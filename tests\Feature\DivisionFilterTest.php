<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Http\Controllers\SuperadminController;
use App\Models\Invoice;
use App\Models\Part;
use App\Models\PartInventory;
use App\Models\UnitTransaction;
use App\Models\UnitTransactionPart;
use App\Models\ManualInvoicePart;
use App\Models\Penawaran;
use App\Models\PenawaranItem;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Carbon\Carbon;

class DivisionFilterTest extends TestCase
{
    use RefreshDatabase;

    protected $controller;

    protected function setUp(): void
    {
        parent::setUp();
        $this->controller = new SuperadminController();
    }

    /** @test */
    public function test_calculate_invoice_total_without_division_filter()
    {
        // Create test data
        $part = Part::factory()->create([
            'part_code' => 'TEST001',
            'part_name' => 'Test Part',
            'part_type' => 'AC',
            'price' => 100000
        ]);

        $partInventory = PartInventory::factory()->create([
            'part_code' => 'TEST001',
            'price' => 100000
        ]);

        $unitTransaction = UnitTransaction::factory()->create();
        
        $unitTransactionPart = UnitTransactionPart::factory()->create([
            'unit_transaction_id' => $unitTransaction->id,
            'part_inventory_id' => $partInventory->part_inventory_id,
            'quantity' => 2,
            'price' => 100000
        ]);

        $invoice = Invoice::factory()->create([
            'ppn' => 0.11
        ]);

        $invoice->unitTransactions()->attach($unitTransaction->id);

        // Test without division filter
        $total = $this->controller->calculateInvoiceTotal($invoice);
        
        // Expected: (100000 * 2) + (200000 * 0.11) = 200000 + 22000 = 222000
        $this->assertEquals(222000, $total);
    }

    /** @test */
    public function test_calculate_invoice_total_with_matching_division_filter()
    {
        // Create test data with AC part type
        $part = Part::factory()->create([
            'part_code' => 'TEST002',
            'part_name' => 'AC Test Part',
            'part_type' => 'AC',
            'price' => 150000
        ]);

        $partInventory = PartInventory::factory()->create([
            'part_code' => 'TEST002',
            'price' => 150000
        ]);

        $unitTransaction = UnitTransaction::factory()->create();
        
        $unitTransactionPart = UnitTransactionPart::factory()->create([
            'unit_transaction_id' => $unitTransaction->id,
            'part_inventory_id' => $partInventory->part_inventory_id,
            'quantity' => 1,
            'price' => 150000
        ]);

        $invoice = Invoice::factory()->create([
            'ppn' => 0.11
        ]);

        $invoice->unitTransactions()->attach($unitTransaction->id);

        // Test with matching division filter
        $total = $this->controller->calculateInvoiceTotal($invoice, 'AC');
        
        // Expected: (150000 * 1) + (150000 * 0.11) = 150000 + 16500 = 166500
        $this->assertEquals(166500, $total);
    }

    /** @test */
    public function test_calculate_invoice_total_with_non_matching_division_filter()
    {
        // Create test data with AC part type
        $part = Part::factory()->create([
            'part_code' => 'TEST003',
            'part_name' => 'AC Test Part',
            'part_type' => 'AC',
            'price' => 150000
        ]);

        $partInventory = PartInventory::factory()->create([
            'part_code' => 'TEST003',
            'price' => 150000
        ]);

        $unitTransaction = UnitTransaction::factory()->create();
        
        $unitTransactionPart = UnitTransactionPart::factory()->create([
            'unit_transaction_id' => $unitTransaction->id,
            'part_inventory_id' => $partInventory->part_inventory_id,
            'quantity' => 1,
            'price' => 150000
        ]);

        $invoice = Invoice::factory()->create([
            'ppn' => 0.11
        ]);

        $invoice->unitTransactions()->attach($unitTransaction->id);

        // Test with non-matching division filter (looking for TYRE but part is AC)
        $total = $this->controller->calculateInvoiceTotal($invoice, 'TYRE');
        
        // Expected: 0 (no parts match the filter)
        $this->assertEquals(0, $total);
    }

    /** @test */
    public function test_calculate_manual_invoice_total_with_division_filter()
    {
        // Create test data for manual invoice
        $part = Part::factory()->create([
            'part_code' => 'MANUAL001',
            'part_name' => 'Manual Test Part',
            'part_type' => 'FABRIKASI',
            'price' => 200000
        ]);

        $invoice = Invoice::factory()->create([
            'ppn' => 0.11
        ]);

        ManualInvoicePart::factory()->create([
            'invoice_id' => $invoice->id,
            'part_code' => 'MANUAL001',
            'quantity' => 3,
            'price' => 200000
        ]);

        // Test with matching division filter
        $total = $this->controller->calculateInvoiceTotal($invoice, 'FABRIKASI');
        
        // Expected: (200000 * 3) + (600000 * 0.11) = 600000 + 66000 = 666000
        $this->assertEquals(666000, $total);

        // Test with non-matching division filter
        $totalNonMatch = $this->controller->calculateInvoiceTotal($invoice, 'AC');
        
        // Expected: 0 (no parts match the filter)
        $this->assertEquals(0, $totalNonMatch);
    }
}
