<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('jasa_karyawan', function (Blueprint $table) {
            $table->id('jasa_karyawan_id');
            $table->string('site_id');
            $table->string('employee_name');
            $table->decimal('amount', 15, 2);
            $table->date('date');
            $table->enum('status', ['submitted', 'approved', 'rejected', 'done'])->default('submitted');
            $table->text('description')->nullable();
            $table->timestamps();

            // Foreign key constraint
            $table->foreign('site_id')->references('site_id')->on('sites')->onDelete('cascade');
            
            // Indexes
            $table->index(['site_id', 'date']);
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('jasa_karyawan');
    }
};
