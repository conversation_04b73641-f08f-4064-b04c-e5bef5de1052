<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BacklogPart extends Model
{
    use HasFactory;

    protected $fillable = [
        'backlog_id',
        'part_code',
        'quantity',
    ];

    protected $casts = [
        'quantity' => 'integer',
    ];

    /**
     * Get the backlog that owns the backlog part.
     */
    public function backlog()
    {
        return $this->belongsTo(Backlog::class);
    }

    /**
     * Get the part that belongs to the backlog part.
     */
    public function part()
    {
        return $this->belongsTo(Part::class, 'part_code', 'part_code');
    }
}
