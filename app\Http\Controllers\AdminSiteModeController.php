<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class AdminSiteModeController extends Controller
{
    public function showModeSelection()
    {
        if (session('role') !== 'adminsite') {
            return redirect('/login')->withErrors(['username' => 'Aks<PERSON> !!']);
        }
        return view('auth.mode-select');
    }
    public function selectMode(Request $request)
    {
        $request->validate([
            'mode' => 'required|in:inventory,daily_report',
        ]);
        session(['access_mode' => $request->mode]);
        if ($request->mode == 'inventory') {
            return redirect()->route('sites.dashboard');
        } else {
            return redirect()->route('daily-reports.index');
        }
    }
}
