<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\DailyReport;

class MidlifeController extends Controller
{
    // MIDLIFE FUNCTION
    /**
     * throw index
     */
    public function mindlife()
    {
        return view('daily-reports.midlife');
    }

    /**
     * Get daily using AJAX request.
     */
    public function getadatamidlife(Request $request)
    {
        $query = DailyReport::with(['unit', 'partProblems']);

    // Add index to the part_name column
    if ($request->filled('part')) {
        $query->whereHas('partProblems', function ($q) use ($request) {
            $q->where('part_name', 'like', '%' . $request->input('part') . '%');
            //   ->useIndex('part_name_index'); // Add this index in migration
        });
    }

    // Add index to the unit_code column
    if ($request->filled('unit')) {
        $query->whereHas('unit', function ($q) use ($request) {
            $q->where('unit_code', 'like', '%' . $request->input('unit') . '%');
            //   ->useIndex('unit_code_index'); // Add this index in migration
        });
    }

        $reports = $query->get();

        // Group by code_part
        $grouped = [];

        foreach ($reports as $report) {
            // ambil data awal dari daily report
            $hm = $report->hm;
            $unit = $report->unit->unit_code ?? null;
            $problem = $report->problem ?? 'tidak diinput';
            $tglin = $report->date_in ?? null;

            // looping semua data part dari daily report
            foreach ($report->partProblems as $part) {
                $codePart = $part->code_part;
                $partName = $part->part_name;

                // periksa jika sudah ada group berdasarkan code part
                // jika tidak section ini membuat group baru
                if (!isset($grouped[$codePart])) {
                    $grouped[$codePart] = [
                        'code_part' => $codePart,
                        'part_name' => $partName,
                        'items' => []
                    ];
                }

                // masukkan data kedalam array item
                $grouped[$codePart]['items'][] = [
                    'hm' => $hm,
                    'unit' => $unit,
                    'problem' => $problem,
                    'tanggalin' => $tglin,
                ];
            }
        }

        // melakukan return json kedalam js midlife.js
        return response()->json(array_values($grouped));
    }
}
