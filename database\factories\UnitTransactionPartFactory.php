<?php

namespace Database\Factories;

use App\Models\UnitTransactionPart;
use Illuminate\Database\Eloquent\Factories\Factory;

class UnitTransactionPartFactory extends Factory
{
    protected $model = UnitTransactionPart::class;

    public function definition()
    {
        return [
            'unit_transaction_id' => 1, // Will be overridden in tests
            'part_inventory_id' => 1, // Will be overridden in tests
            'quantity' => $this->faker->numberBetween(1, 10),
            'part_name' => $this->faker->words(3, true),
            'price' => $this->faker->numberBetween(50000, 500000),
            'eum' => 'EA',
        ];
    }
}
