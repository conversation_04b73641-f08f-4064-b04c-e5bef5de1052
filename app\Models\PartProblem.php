<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PartProblem extends Model
{
    use HasFactory;

    protected $fillable = [
        'daily_report_id',
        'code_part',
        'part_name',
        'quantity',
        'eum',
        'remarks',
    ];

    protected $casts = [
        'quantity' => 'float',
    ];

    /**
     * Get the daily report that owns the part problem.
     */
    public function dailyReport()
    {
        return $this->belongsTo(DailyReport::class, 'daily_report_id', 'daily_report_id');
    }
}
