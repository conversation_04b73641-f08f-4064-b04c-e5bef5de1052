<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add indexes for invoices table - most critical for dashboard performance
        Schema::table('invoices', function (Blueprint $table) {
            try {
                // Single column indexes for frequently queried fields
                $table->index('tanggal_invoice', 'idx_invoices_tanggal_invoice');
            } catch (\Exception $e) {
                // Index might already exist, continue
            }

            try {
                $table->index('payment_status', 'idx_invoices_payment_status');
            } catch (\Exception $e) {
                // Index might already exist, continue
            }

            try {
                $table->index('penawaran_id', 'idx_invoices_penawaran_id');
            } catch (\Exception $e) {
                // Index might already exist, continue
            }

            try {
                $table->index('site_id', 'idx_invoices_site_id');
            } catch (\Exception $e) {
                // Index might already exist, continue
            }

            try {
                // Composite indexes for common query patterns
                $table->index(['tanggal_invoice', 'payment_status'], 'idx_invoices_date_status');
            } catch (\Exception $e) {
                // Index might already exist, continue
            }

            try {
                $table->index(['tanggal_invoice', 'site_id'], 'idx_invoices_date_site');
            } catch (\Exception $e) {
                // Index might already exist, continue
            }
        });

        // Add indexes for parts table - critical for division filtering
        Schema::table('parts', function (Blueprint $table) {
            try {
                $table->index('part_type', 'idx_parts_part_type');
            } catch (\Exception $e) {
                // Index might already exist, continue
            }

            try {
                // Composite index for part_code and part_type (common lookup pattern)
                $table->index(['part_code', 'part_type'], 'idx_parts_code_type');
            } catch (\Exception $e) {
                // Index might already exist, continue
            }
        });

        // Add indexes for unit_transactions table
        Schema::table('unit_transactions', function (Blueprint $table) {
            try {
                $table->index('site_id', 'idx_unit_transactions_site_id');
            } catch (\Exception $e) {
                // Index might already exist, continue
            }

            try {
                // Composite index for common date-based queries
                $table->index(['po_date', 'site_id'], 'idx_unit_transactions_po_date_site');
            } catch (\Exception $e) {
                // Index might already exist, continue
            }
        });

        // Add indexes for part_inventories table
        Schema::table('part_inventories', function (Blueprint $table) {
            try {
                $table->index('part_code', 'idx_part_inventories_part_code');
            } catch (\Exception $e) {
                // Index might already exist, continue
            }

            try {
                $table->index('site_id', 'idx_part_inventories_site_id');
            } catch (\Exception $e) {
                // Index might already exist, continue
            }

            try {
                // Composite index for part_code and site_id (very common lookup)
                $table->index(['part_code', 'site_id'], 'idx_part_inventories_code_site');
            } catch (\Exception $e) {
                // Index might already exist, continue
            }
        });

        // Add indexes for unit_transaction_parts table
        Schema::table('unit_transaction_parts', function (Blueprint $table) {
            try {
                $table->index('unit_transaction_id', 'idx_unit_transaction_parts_transaction_id');
            } catch (\Exception $e) {
                // Index might already exist, continue
            }

            try {
                $table->index('part_inventory_id', 'idx_unit_transaction_parts_inventory_id');
            } catch (\Exception $e) {
                // Index might already exist, continue
            }
        });

        // Add indexes for penawaran_items table
        Schema::table('penawaran_items', function (Blueprint $table) {
            try {
                $table->index('penawaran_id', 'idx_penawaran_items_penawaran_id');
            } catch (\Exception $e) {
                // Index might already exist, continue
            }

            try {
                $table->index('part_inventory_id', 'idx_penawaran_items_inventory_id');
            } catch (\Exception $e) {
                // Index might already exist, continue
            }
        });

        // Add indexes for manual_invoice_parts table
        Schema::table('manual_invoice_parts', function (Blueprint $table) {
            try {
                $table->index('invoice_id', 'idx_manual_invoice_parts_invoice_id');
            } catch (\Exception $e) {
                // Index might already exist, continue
            }

            try {
                $table->index('part_code', 'idx_manual_invoice_parts_part_code');
            } catch (\Exception $e) {
                // Index might already exist, continue
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop indexes in reverse order - using try/catch to handle non-existent indexes
        Schema::table('manual_invoice_parts', function (Blueprint $table) {
            try { $table->dropIndex('idx_manual_invoice_parts_invoice_id'); } catch (\Exception $e) {}
            try { $table->dropIndex('idx_manual_invoice_parts_part_code'); } catch (\Exception $e) {}
        });

        Schema::table('penawaran_items', function (Blueprint $table) {
            try { $table->dropIndex('idx_penawaran_items_penawaran_id'); } catch (\Exception $e) {}
            try { $table->dropIndex('idx_penawaran_items_inventory_id'); } catch (\Exception $e) {}
        });

        Schema::table('unit_transaction_parts', function (Blueprint $table) {
            try { $table->dropIndex('idx_unit_transaction_parts_transaction_id'); } catch (\Exception $e) {}
            try { $table->dropIndex('idx_unit_transaction_parts_inventory_id'); } catch (\Exception $e) {}
        });

        Schema::table('part_inventories', function (Blueprint $table) {
            try { $table->dropIndex('idx_part_inventories_part_code'); } catch (\Exception $e) {}
            try { $table->dropIndex('idx_part_inventories_site_id'); } catch (\Exception $e) {}
            try { $table->dropIndex('idx_part_inventories_code_site'); } catch (\Exception $e) {}
        });

        Schema::table('unit_transactions', function (Blueprint $table) {
            try { $table->dropIndex('idx_unit_transactions_site_id'); } catch (\Exception $e) {}
            try { $table->dropIndex('idx_unit_transactions_po_date_site'); } catch (\Exception $e) {}
        });

        Schema::table('parts', function (Blueprint $table) {
            try { $table->dropIndex('idx_parts_part_type'); } catch (\Exception $e) {}
            try { $table->dropIndex('idx_parts_code_type'); } catch (\Exception $e) {}
        });

        Schema::table('invoices', function (Blueprint $table) {
            try { $table->dropIndex('idx_invoices_tanggal_invoice'); } catch (\Exception $e) {}
            try { $table->dropIndex('idx_invoices_payment_status'); } catch (\Exception $e) {}
            try { $table->dropIndex('idx_invoices_penawaran_id'); } catch (\Exception $e) {}
            try { $table->dropIndex('idx_invoices_site_id'); } catch (\Exception $e) {}
            try { $table->dropIndex('idx_invoices_date_status'); } catch (\Exception $e) {}
            try { $table->dropIndex('idx_invoices_date_site'); } catch (\Exception $e) {}
        });
    }
};
