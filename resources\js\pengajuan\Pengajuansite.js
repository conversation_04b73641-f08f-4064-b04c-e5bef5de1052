// Import required libraries
import Swal from "sweetalert2";
import bootstrap from '../bootstrap-init';

// Function to get the appropriate badge class based on status
function getStatusBadgeClass(status) {
    switch(status.toLowerCase()) {
        case 'diajukan':
            return 'bg-info'; // Blue badge for submitted
        case 'pending':
            return 'bg-warning'; // Yellow badge for pending
        case 'disetujui':
            return 'bg-success'; // Green badge for approved
        case 'selesai':
            return 'bg-primary'; // Primary badge for completed
        default:
            return 'bg-secondary'; // Grey badge for other statuses
    }
}

// Global variables for pagination
let currentPage = 1;
const itemsPerPage = 15; // 15 items per page for pengajuan table

document.addEventListener("DOMContentLoaded", function () {
    const pengajuanTable = document.getElementById("pengajuanTable");
    const addItemToListBtn = document.getElementById("addItemToListBtn");
    const listPengajuan = document.getElementById("listPengajuan");
    const pesanKosong = document.getElementById("pesanKosong");
    const simpanPengajuanBtn = document.getElementById("simpanPengajuanBtn");
    const tutupDetailsBtn = document.getElementById("tutupDetailsBtn");
    const partNameInput = document.getElementById("part_name"); // Get the input element
    const pengajuanDetailsForm = document.getElementById(
        "pengajuanDetailsForm"
    );

    let itemList = [];
    let currentPengajuanId = null;

    // Function to create pagination item
    function createPaginationItem(page, text, isActive = false) {
        const li = document.createElement('li');
        li.className = `page-item ${isActive ? 'active' : ''}`;

        const a = document.createElement('a');
        a.className = 'page-link';
        a.href = '#';
        a.textContent = text;
        a.dataset.page = page;

        li.appendChild(a);
        return li;
    }

    // Function to render pagination
    function renderPagination(data) {
        try {
            const paginationContainer = document.getElementById('pengajuan-pagination');
            if (!paginationContainer) {
                console.error('Pagination container not found');
                return;
            }

            paginationContainer.innerHTML = '';

            if (data.last_page > 1) {
                const pagination = document.createElement('ul');
                pagination.className = 'pagination pagination-rounded  justify-content-center';

                // Previous button
                if (data.current_page > 1) {
                    pagination.appendChild(createPaginationItem(data.current_page - 1, '\u00ab'));
                }

                // Page numbers
                for (let i = 1; i <= data.last_page; i++) {
                    pagination.appendChild(createPaginationItem(i, i, i === data.current_page));
                }

                // Next button
                if (data.current_page < data.last_page) {
                    pagination.appendChild(createPaginationItem(data.current_page + 1, '\u00bb'));
                }

                paginationContainer.appendChild(pagination);

                // Add event listeners to pagination links
                paginationContainer.querySelectorAll('.page-link').forEach(link => {
                    link.addEventListener('click', function(e) {
                        e.preventDefault();
                        const page = parseInt(this.dataset.page);
                        currentPage = page;
                        loadPengajuans(page);
                    });
                });
            }

            // Update global pagination data
            window.pengajuanPaginationData = data;
        } catch (error) {
            console.error('Error rendering pagination:', error);
        }
    }

    // Function to fetch parts autocomplete suggestions
    function fetchPartSuggestions(query) {
        const suggestionsDiv = document.getElementById("suggestions");
        suggestionsDiv.innerHTML = "";
        suggestionsDiv.style.display = "none";

        // Only fetch if query length is greater than 3 characters
        if (query.length > 3) {
            fetch(`/parts/autocomplete?query=${query}`, {
                method: "GET",
                headers: {
                    "Content-Type": "application/json",
                    "X-CSRF-TOKEN": document
                        .querySelector('meta[name="csrf-token"]')
                        .getAttribute("content"),
                },
            })
                .then((response) => {
                    if (!response.ok) {
                        throw new Error(
                            `HTTP error! status: ${response.status}`
                        );
                    }
                    return response.json();
                })
                .then((data) => {
                    if (data.length > 0) {
                        suggestionsDiv.style.display = "block";

                        // Limit the number of displayed results to 20 to prevent excessive scrolling
                        const maxResults = 20;
                        const displayData = data.slice(0, maxResults);

                        // Add a message if results were limited
                        if (data.length > maxResults) {
                            const limitMessage = document.createElement("div");
                            limitMessage.textContent = `Menampilkan ${maxResults} dari ${data.length} hasil. Silakan perjelas pencarian Anda.`;
                            limitMessage.classList.add("suggestion-limit-message");
                            suggestionsDiv.appendChild(limitMessage);
                        }

                        displayData.forEach((part) => {
                            const suggestion = document.createElement("div");
                            suggestion.textContent = part.part_name;
                            suggestion.classList.add("suggestion-item");

                            suggestion.onclick = () => {
                                document.getElementById("part_name").value =
                                    part.part_name;
                                document.getElementById(
                                    "selected_part_code"
                                ).value = part.part_code;
                                suggestionsDiv.style.display = "none";
                            };
                            suggestionsDiv.appendChild(suggestion);
                        });
                    }
                })
                .catch((error) => {
                    console.error(
                        "Error fetching autocomplete suggestions:",
                        error
                    );
                });
        }
    }

    // Event listener for part name input
    if (partNameInput) {
        partNameInput.addEventListener("input", function () {
            fetchPartSuggestions(this.value);
        });

        // Close suggestions when clicking outside
        document.addEventListener('click', function(e) {
            const suggestionsDiv = document.getElementById("suggestions");
            if (suggestionsDiv && !partNameInput.contains(e.target) && !suggestionsDiv.contains(e.target)) {
                suggestionsDiv.style.display = 'none';
            }
        });
    }
    function showDetails(id) {
        // Helper function untuk format tanggal
        function formatDate(isoString) {
            const date = new Date(isoString);
            const options = {
                day: "numeric",
                month: "long",
                year: "numeric",
                hour: "2-digit",
                minute: "2-digit",
                hour12: false,
            };

            // Jika waktu 00:00, tampilkan tanggal saja
            if (date.getUTCHours() === 0 && date.getUTCMinutes() === 0) {
                return date.toLocaleDateString("id-ID", {
                    day: "numeric",
                    month: "long",
                    year: "numeric",
                });
            }

            return date.toLocaleDateString("id-ID", options) + " WIB";
        }

        // Helper untuk deteksi field tanggal
        function isDateField(key) {
            return /(_date|_at)$/i.test(key);
        }

        fetch(`/pengajuan/${id}`)
            .then((response) => response.json())
            .then((data) => {
                let detailText = '<div class="table-responsive">';

                // Daftar field yang akan di-exclude
                const excludedFields = ["id", "site_id"];

                // Custom label untuk field tertentu
                const fieldLabels = {
                    requisition_type: "Jenis Pengajuan",
                    status: "Status Pengajuan",
                    modified_by: "Diubah Oleh",
                    requisition_date: "Tanggal diajukan",
                    confirmation_date: "Tanggal dikonfirmasi",
                    modified_by: "Di ajukan Oleh",
                    created_at: "Log ",
                };

                // Urutan field yang ingin ditampilkan
                const allowedFields = [
                    "requisition_type",
                    "status",
                    "requisition_date",
                    "confirmation_date",
                    "modified_by",
                    "created_at",
                ];

                // Tabel Informasi Umum
                detailText += `
                <h5 class="mb-3">Informasi Pengajuan</h5>
                <table class="table table-striped table-bordered mb-4">
                    <tbody>`;

                allowedFields.forEach((field) => {
                    if (
                        data.hasOwnProperty(field) &&
                        !excludedFields.includes(field)
                    ) {
                        const label =
                            fieldLabels[field] ||
                            field
                                .replace(/_/g, " ")
                                .replace(/(^\w|\s\w)/g, (m) => m.toUpperCase());

                        let value = data[field];

                        // Format tanggal
                        if (isDateField(field) && value) {
                            value = formatDate(value);
                        }

                        // Add badge for status field
                        if (field === 'status' && value) {
                            value = `<span class="badge ${getStatusBadgeClass(value)}">${value}</span>`;
                        }

                        detailText += `
                        <tr>
                            <th width="30%" class="bg-light">${label}</th>
                            <td>${value || "-"}</td>
                        </tr>`;
                    }
                });
                detailText += `</tbody></table>`;
                // Tabel Daftar Barang
                if (data.details && Array.isArray(data.details)) {
                    detailText += `
                        <h5 class="mb-3 font-bold">Daftar Barang</h5>
                        <table class="table table-bordered">
                            <thead class="thead-light">
                                <tr>
                                    <th>Nama Part</th>
                                    <th width="15%">Sedang Diajukan</th>
                                    <th width="15%">Diterima Site</th>
                                    <th width="15%">Dikirim HO</th>
                                    <th>Status</th>
                                    <th>Catatan HO</th>
                                    <th>Ket</th>
                                </tr>
                            </thead>
                            <tbody>`;

                    data.details.forEach((item) => {
                        let suratJalanLink = "";
                        if (item.surat_jalan_path) {
                            suratJalanLink = `<a href="#" data-file-url="/storage/${item.surat_jalan_path}">Lihat</a>`;
                        } else {
                            suratJalanLink =
                                '<em class="text-muted">Tidak ada</em>';
                        }
                        detailText += `
                                    <tr>
                                        <td>${item.part.part_name}</td>
                                        <td>${item.quantity}</td>
                                        <td>${item.quantity_confirm}</td>
                                        <td>${item.quantity_send}</td>
                                        <td><span class="badge ${getStatusBadgeClass(item.status_details)}">${item.status_details}</span></td>
                                        <td>${
                                            item.notes_ho ||
                                            '<em class="text-muted">Tidak ada catatan</em>'
                                        }</td>
                                        <td>${item.quantity_send > 0 ? 'Konfirmasi Penerimaan Pada Halaman In Stock' : ''}
                                        </td>
                                    </tr>`;
                    });
                    detailText += `</tbody></table>`;
                } else {
                    detailText += `<div class="alert alert-info">Tidak ada daftar barang</div>`;
                }

                detailText += "</div>"; // Tutup table-responsive
                document.getElementById("detailContent").innerHTML = detailText;
                document.getElementById("detailsDiv").style.display = "block";

                // Tambahkan event listener setelah detail di-render
                document
                    .querySelectorAll("#detailContent a[data-file-url]")
                    .forEach((link) => {
                        link.addEventListener("click", function (event) {
                            event.preventDefault(); // Prevent default link behavior
                            const fileUrl = this.dataset.fileUrl;
                            showFileInModal(fileUrl);
                        });
                    });
            });
    }
    tutupDetailsBtn.addEventListener("click", function () {
        document.getElementById("detailsDiv").style.display = "none";
    });

    // Function to attach event listeners to the dynamically created buttons.
    function attachEventListeners() {
        // Debug: Count how many delete buttons we found
        const deleteButtons = document.querySelectorAll(".deleteBtn");

        deleteButtons.forEach((button) => {
            button.addEventListener("click", function () {
                const id = this.dataset.id;
                deletePengajuan(id);
            });
        });

        document.querySelectorAll(".detailsBtn").forEach((button) => {
            button.addEventListener("click", function () {
                const id = this.dataset.id;
                showDetails(id);
            });
        });

        document.querySelectorAll(".editBtn").forEach((button) => {
            button.addEventListener("click", function () {
                const id = this.dataset.id;
                editPengajuan(id);
            });
        });
    }

    function editPengajuan(id) {

        fetch(`/pengajuan/${id}`)
            .then((response) => response.json())
            .then((data) => {

                document.getElementById("title").value = data.title;
                document.getElementById("site_id").value = data.site_id;
                document.getElementById("modified_by").value = data.modified_by;

                document.getElementById("notes").value = data.notes;
                itemList = data.details || [];
                updateItemList();
                currentPengajuanId = id;
                window.scrollTo({ top: 0, behavior: "smooth" });
            })
            .catch((error) => {
                console.error("Error fetching pengajuan:", error);
                Swal.fire({
                    icon: "error",
                    title: "Error!",
                    text: `Failed to fetch pengajuan: ${error.message}`,
                });
            });
    }

    function showItemDetails(id) {
        // Memperbaiki endpoint details
        const modalBody = document.getElementById("itemDetailsBody");
        modalBody.innerHTML = "<p>.</p>";

        fetch(`/pengajuan/${id}/details`)
            .then((response) => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                // Periksa apakah respons adalah JSON
                const contentType = response.headers.get("content-type");
                if (!contentType || !contentType.includes("application/json")) {
                    // Jika bukan JSON, mungkin HTML (misalnya, halaman login)
                    console.error("Received non-JSON response:", contentType);
                    throw new Error("Received non-JSON response"); // hentikan pemrosesan lebih lanjut
                }
                return response.json();
            })
            .then((details) => {
                let detailsHTML = "<ul>";
                if (details && details.length > 0) {
                    details.forEach((detail) => {
                        detailsHTML += `<li>${detail.item_name} - Quantity: ${
                            detail.quantity
                        } - Note: ${detail.note || "N/A"}</li>`;
                    });
                } else {
                    detailsHTML +=
                        "<li>Tidak ada detail item untuk pengajuan ini.</li>";
                }
                detailsHTML += "</ul>";
                modalBody.innerHTML = detailsHTML;
            })
            .catch((error) => {
                console.error("Error:", error);
                modalBody.innerHTML = `<p>Terjadi kesalahan: ${error.message}</p>`;
            });
    }

    addItemToListBtn.addEventListener("click", function () {
        const partName = document.getElementById("part_name").value.trim();
        const partCode = document.getElementById("selected_part_code").value.trim();
        const quantity = document.getElementById("quantity").value;
        const note = document.getElementById("note").value.trim();

        // Enhanced input validation
        if (!partName || !partCode) {
            Swal.fire({
                icon: "error",
                title: "Data Tidak Lengkap",
                text: "Silakan pilih part terlebih dahulu dari daftar yang tersedia",
            });
            return;
        }

        if (!quantity || quantity <= 0) {
            Swal.fire({
                icon: "error",
                title: "Jumlah Tidak Valid",
                text: "Jumlah harus diisi dan lebih besar dari 0",
            });
            return;
        }

        // Check for duplicates
        const isDuplicate = itemList.some(item => item.part_code === partCode);
        if (isDuplicate) {
            Swal.fire({
                icon: "warning",
                title: "Part Sudah Ada",
                text: "Part ini sudah ada dalam daftar pengajuan",
            });
            return;
        }

        // Add item with validation
        try {
            const item = {
                part_code: partCode,
                part_name: partName, // Changed from item_name to part_name for clarity
                quantity: parseInt(quantity),
                note: note
            };

            itemList.push(item);
            updateItemList();

            // Clear form
            clearForm();
        } catch (error) {
            Swal.fire({
                icon: "error",
                title: "Terjadi Kesalahan",
                text: "Gagal menambahkan item ke daftar",
            });
        }
    });

    function clearForm() {
        document.getElementById("part_name").value = "";
        document.getElementById("selected_part_code").value = "";
        document.getElementById("quantity").value = "";
        document.getElementById("note").value = "";
    }

    function updateItemList() {
        listPengajuan.innerHTML = "";

        if (itemList.length > 0) {
            pesanKosong.style.display = "none";

            itemList.forEach((item, index) => {
                const listItem = document.createElement("li");
                listItem.classList.add("list-group-item");
                listItem.innerHTML = `
                    ${item.part_name} - Jumlah: ${item.quantity} - Note: ${
                    item.note || "Tidak ada"
                }
                    <button type="button" class="btn btn-danger btn-sm float-right removeItemBtn" data-index="${index}">Hapus</button>
                `;
                listPengajuan.appendChild(listItem);
            });

            document.querySelectorAll(".removeItemBtn").forEach((button) => {
                button.addEventListener("click", function () {
                    const indexToRemove = this.dataset.index;
                    removeItemFromList(indexToRemove);
                });
            });
        } else {
            pesanKosong.style.display = "block";
        }
    }

    function removeItemFromList(index) {
        itemList.splice(index, 1);
        updateItemList();
    }

    simpanPengajuanBtn.addEventListener("click", function () {
        Swal.fire({
            title: "Yakin melanjutkan?",
            text: "Data tidak bisa diubah, namun anda bisa menghapusnya sebelum warehouse konfirmasi",
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: "Ya, Simpan!",
            cancelButtonText: "Batal",
        }).then((result) => {
            if (result.isConfirmed) {
                const title = document.getElementById("title").value;
                const requisitionType = "Pembelian Part";
                const notes = document.getElementById("notes").value;

                if (itemList.length === 0) {
                    Swal.fire({
                        icon: "warning",
                        title: "Peringatan!",
                        text: "List item pengajuan kosong!",
                    });
                    return;
                }

                const data = {
                    title: title,
                    notes: notes,
                    requisition_type: requisitionType,
                    details: itemList,
                };

                let url = "/pengajuan";
                let method = "POST";

                if (currentPengajuanId) {
                    url = `/pengajuan/${currentPengajuanId}`;
                    method = "PUT";
                }

                fetch(url, {
                    method: method,
                    headers: {
                        "Content-Type": "application/json",
                        "X-CSRF-TOKEN": document
                            .querySelector('meta[name="csrf-token"]')
                            .getAttribute("content"),
                    },
                    body: JSON.stringify(data),
                })
                    .then(async (response) => {
                        const contentType = response.headers.get("content-type");

                        if (!response.ok) {
                            // Try to get detailed error message from response if possible
                            if (contentType && contentType.includes("application/json")) {
                                const errorData = await response.json();
                                console.error('Server error details:', errorData);
                                if (errorData.errors) {
                                    // Format validation errors
                                    const errorMessages = Object.entries(errorData.errors)
                                        .map(([field, messages]) => `${field}: ${messages.join(', ')}`)
                                        .join('\n');
                                    throw new Error(`Validation failed:\n${errorMessages}`);
                                } else if (errorData.message) {
                                    throw new Error(errorData.message);
                                }
                            }
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }

                        if (!contentType || !contentType.includes("application/json")) {
                            throw new Error("Received non-JSON response");
                        }
                        return response.json();
                    })
                    .then((responseData) => {
                        loadPengajuans(1); // Reset to page 1 after saving
                        resetForm();
                        Swal.fire({
                            icon: "success",
                            title: "Berhasil!",
                            text: responseData.message || "Pengajuan berhasil disimpan!",
                        });
                    })
                    .catch((error) => {
                        Swal.fire({
                            icon: "error",
                            title: "Error!",
                            text: `Terjadi kesalahan: ${error.message}`,
                        });
                    });
            }
        });
    });

    function resetForm() {
        document.getElementById("title").value = "";
        document.getElementById("site_id").value = "";
        document.getElementById("modified_by").value = "";
        document.getElementById("notes").value = "";
        itemList = [];
        updateItemList();
        currentPengajuanId = null;
    }

    function loadPengajuans(page = 1) {
        currentPage = page;

        // Show loading indicator
        const tableBody = pengajuanTable.querySelector("tbody");
        tableBody.innerHTML = '<tr><td colspan="6" class="text-center">.</td></tr>';

        let url = `/getdata?page=${page}&per_page=${itemsPerPage}`;

        fetch(url, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
                "X-CSRF-TOKEN": document
                    .querySelector('meta[name="csrf-token"]')
                    .getAttribute("content"),
            },
        })
            .then((response) => response.json())
            .then((data) => {
                tableBody.innerHTML = ""; // Clear existing table rows

                if (data.data && data.data.length > 0) {
                    data.data.forEach((item) => {
                    const row = document.createElement("tr");
                    row.id = `row_${item.requisition_id}`;

                    // We're using badges now instead of row background colors

                    row.innerHTML = `
                    <td>${item.title}</td>
                    <td><span class="badge ${getStatusBadgeClass(item.status)}">${item.status}</span></td>
                    <td>${item.requisition_type}</td>
                    <td>${formatDate(item.requisition_date)}</td>
                    <td>${item.notes}</td>
                    <td>
                        <!-- Always show delete button for debugging -->
                        <button class="btn btn-sm btn-danger deleteBtn" data-id="${item.requisition_id}">Hapus</button>
                        <button class="btn btn-sm btn-info detailsBtn" data-id="${item.requisition_id}" data-bs-toggle="modal" data-bs-target="#itemDetailsModal">Details</button>
                    </td>
                `;

                    pengajuanTable.querySelector("tbody").appendChild(row);
                    });

                    // Render pagination
                    renderPagination({
                        current_page: data.current_page,
                        per_page: data.per_page,
                        last_page: data.last_page,
                        total: data.total
                    });
                } else {
                    tableBody.innerHTML = '<tr><td colspan="6" class="text-center">No data found</td></tr>';
                }

                attachEventListeners();
            })
            .catch((error) => {
                console.error("Error:", error);
                tableBody.innerHTML = '<tr><td colspan="6" class="text-center text-danger">Failed to load data. Please try again.</td></tr>';
            });
    }

    function formatDate(isoDate) {
        const date = new Date(isoDate);
        return date.toLocaleDateString("id-ID", {
            day: "2-digit",
            month: "long",
            year: "numeric",
        });
    }

    function toggleJumlah() {
        const jumlahGroup = document.getElementById("jumlahGroup");

        if (!jumlahGroup) {
            console.error("jumlahGroup element not found!");
            return; // Exit if the element doesn't exist
        }

        // if (jenisPengajuan === "Pembelian Part") {
        //     jumlahGroup.style.display = "block";
        // } else {
        //     jumlahGroup.style.display = "none";
        //     document.getElementById("jumlah").value = 0; // Ensure 'jumlah' element exists too
        // }
    }

    function deletePengajuan(id) {
        // Show loading indicator
        Swal.fire({
            title: "Memeriksa status pengajuan...",
            text: "Mohon tunggu sebentar",
            allowOutsideClick: false,
            allowEscapeKey: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        // First, fetch the requisition details to check its status
        fetch(`/pengajuan/${id}`)
            .then(response => response.json())
            .then(data => {
                Swal.close();

                // Check if the requisition can be deleted
                if (data.status !== 'diajukan') {
                    Swal.fire({
                        icon: "error",
                        title: "Tidak dapat menghapus",
                        text: "Hanya pengajuan dengan status 'diajukan' yang dapat dihapus."
                    });
                    return;
                }

                // Check if all details have status 'pending'
                const nonPendingDetails = data.details.filter(detail => detail.status_details !== 'pending');
                if (nonPendingDetails.length > 0) {
                    Swal.fire({
                        icon: "error",
                        title: "Tidak dapat menghapus",
                        text: "Beberapa item sudah diproses oleh warehouse."
                    });
                    return;
                }

                // If all checks pass, confirm deletion
                Swal.fire({
                    title: "Apakah Anda yakin?",
                    text: "Anda tidak akan dapat mengembalikan ini!",
                    icon: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#3085d6",
                    cancelButtonColor: "#d33",
                    confirmButtonText: "Ya, Hapus!",
                    cancelButtonText: "Batal",
                }).then((result) => {
                    if (result.isConfirmed) {
                fetch(`/pengajuan/${id}`, {
                    method: "DELETE",
                    headers: {
                        "Content-Type": "application/json",
                        Accept: "application/json",
                        "X-CSRF-TOKEN": document
                            .querySelector('meta[name="csrf-token"]')
                            .getAttribute("content"),
                    },
                })
                    .then(async (response) => {
                        const contentType = response.headers.get("content-type");

                        // Always try to parse the JSON response, even if status is not OK
                        if (contentType && contentType.includes("application/json")) {
                            const data = await response.json();

                            if (!response.ok) {
                                // If server returned an error with details
                                console.error('Delete error details:', data);
                                throw new Error(data.message || `HTTP error! status: ${response.status}`);
                            }

                            return data;
                        } else {
                            // Non-JSON response
                            console.error("Received non-JSON response:", contentType);
                            if (!response.ok) {
                                throw new Error(`HTTP error! status: ${response.status}`);
                            }
                            throw new Error("Received non-JSON response");
                        }
                    })
                    .then((data) => {
                        if (data.success === false) {
                            // Server returned success:false but with 200 status code
                            Swal.fire({
                                icon: "error",
                                title: "Gagal Menghapus",
                                text: data.message
                            });
                        } else {
                            // Success case
                            Swal.fire("Terhapus!", data.message, "success");
                            loadPengajuans(1); // Refresh tabel setelah menghapus dan reset ke halaman 1
                        }
                    })
                    .catch((error) => {
                        console.error("Error:", error);
                        Swal.fire({
                            icon: "error",
                            title: "Gagal Menghapus",
                            text: error.message || "Terjadi kesalahan saat menghapus."
                        });
                    });
                    }
                });
            });
    }

    // Initial load of pengajuans with page 1
    loadPengajuans(1);

    if (document.getElementById("jumlahGroup")) {
        // Check if element exists
        toggleJumlah(); // Call it here, but only if the element exists
    }

    // menampilkan file dalam modal
    function showFileInModal(fileUrl) {
        const modal = document.createElement("div");
        modal.classList.add("modal");
        modal.classList.add("fade");
        modal.setAttribute("id", "fileModal");
        modal.setAttribute("tabindex", "-1");
        modal.setAttribute("role", "dialog");
        modal.setAttribute("aria-labelledby", "fileModalLabel");
        modal.setAttribute("aria-hidden", "true");

        modal.innerHTML = `
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="fileModalLabel">Lampiran</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">×</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <iframe id="iframesk" src="${fileUrl}" style="width:100%; height:700px;"></iframe>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Tutup</button>
                        <a class="btn btn-primary" href="${fileUrl}" download>Unduh</a>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Inisialisasi Bootstrap Modal dengan Bootstrap 5
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();

        // Hapus modal dari DOM setelah ditutup
        modal.addEventListener('hidden.bs.modal', function () {
            document.body.removeChild(modal);
        });
    }
});

