/**
 * Dashboard Unit Site CSS
 * Custom styles for unit dashboard interface
 */

/* Empty State Animation */
.empty-state-animation {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

/* Unit Search Styles */
#unit-search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 1050;
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    background: white;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.unit-search-result {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #f8f9fa;
    transition: background-color 0.15s ease-in-out;
}

.unit-search-result:hover {
    background-color: #f8f9fa;
    text-decoration: none;
}

.unit-search-result:last-child {
    border-bottom: none;
}

/* Loading State */
#loading-state {
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Dashboard Cards */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.15s ease-in-out, transform 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

/* Daily Reports Table */
#daily-reports-table {
    margin-bottom: 0;
}

.daily-report-row {
    transition: background-color 0.15s ease-in-out;
}

.daily-report-row:hover {
    background-color: #f8f9fa;
}

/* Latest Report Card */
.latest-report-card {
    font-size: 0.9rem;
}

.latest-report-card .row {
    margin-bottom: 0.5rem;
}

.latest-report-card .row:last-child {
    margin-bottom: 0;
}

/* Backlog Items */
.backlog-item {
    background-color: #fff8e1;
    border-color: #ffc107 !important;
    transition: all 0.15s ease-in-out;
}

.backlog-item:hover {
    background-color: #fff3cd;
    transform: translateX(5px);
}

/* Part Usage Stats */
#part-usage-content .d-flex {
    padding: 0.5rem 0;
    border-bottom: 1px solid #f8f9fa;
    transition: background-color 0.15s ease-in-out;
}

#part-usage-content .d-flex:hover {
    background-color: #f8f9fa;
    border-radius: 0.25rem;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
}

#part-usage-content .d-flex:last-child {
    border-bottom: none;
}

/* Badge Styles */
.badge {
    font-size: 0.75em;
    font-weight: 500;
}

.badge.bg-success {
    background-color: #198754 !important;
}

.badge.bg-danger {
    background-color: #dc3545 !important;
}

.badge.bg-warning {
    background-color: #ffc107 !important;
    color: #000;
}

.badge.bg-secondary {
    background-color: #6c757d !important;
}

.badge.bg-info {
    background-color: #0dcaf0 !important;
    color: #000;
}

.badge.bg-primary {
    background-color: #0d6efd !important;
}

/* Chart Containers */
canvas {
    max-height: 300px !important;
}

/* Modal Styles */
.modal-lg {
    max-width: 900px;
}

.form-control-plaintext {
    padding: 0.375rem 0;
    margin-bottom: 0;
    font-size: 0.875rem;
    line-height: 1.5;
    color: #212529;
    background-color: transparent;
    border: solid transparent;
    border-width: 1px 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .card {
        margin-bottom: 1rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .latest-report-card {
        font-size: 0.8rem;
    }
    
    .backlog-item {
        padding: 1rem !important;
    }
    
    #unit-search-results {
        font-size: 0.875rem;
    }
    
    .unit-search-result {
        padding: 0.5rem 0.75rem;
    }
}

@media (max-width: 576px) {
    .page-title-box h4 {
        font-size: 1.25rem;
    }
    
    .card-title {
        font-size: 1rem;
    }
    
    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
    
    .table th,
    .table td {
        padding: 0.5rem 0.25rem;
        font-size: 0.8rem;
    }
    
    .badge {
        font-size: 0.65em;
    }
}

/* Animation for state transitions */
#empty-state,
#loading-state,
#dashboard-content {
    transition: opacity 0.3s ease-in-out;
}

/* Unit header card */
.card.bg-primary {
    background: linear-gradient(135deg, #0d6efd 0%, #0b5ed7 100%) !important;
}

.card.bg-primary .card-body {
    position: relative;
    overflow: hidden;
}

.card.bg-primary .card-body::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: rotate(45deg);
}

/* Hover effects for interactive elements */
.btn {
    transition: all 0.15s ease-in-out;
}

.btn:hover {
    transform: translateY(-1px);
}

/* Custom scrollbar for search results */
#unit-search-results::-webkit-scrollbar {
    width: 6px;
}

#unit-search-results::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

#unit-search-results::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

#unit-search-results::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Focus states */
.form-control:focus {
    border-color: #86b7fe;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Table hover effects */
.table-hover tbody tr:hover td {
    background-color: rgba(13, 110, 253, 0.075);
}

/* Loading spinner animation */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.spinner-border {
    animation: spin 0.75s linear infinite;
}

/* Success/Error message styles */
.alert {
    border: none;
    border-radius: 0.5rem;
    font-weight: 500;
}

.alert-success {
    background-color: #d1e7dd;
    color: #0f5132;
}

.alert-danger {
    background-color: #f8d7da;
    color: #842029;
}

/* Print styles */
@media print {
    .btn,
    .page-title-right,
    #unit-search-results,
    .modal {
        display: none !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #dee2e6 !important;
    }
    
    .page-break {
        page-break-before: always;
    }
}

/* Dark mode support (if needed) */
@media (prefers-color-scheme: dark) {
    .card {
        background-color: #2d3748;
        color: #e2e8f0;
    }
    
    .card-header {
        background-color: #4a5568;
        border-bottom-color: #718096;
    }
    
    .table {
        color: #e2e8f0;
    }
    
    .table-hover tbody tr:hover td {
        background-color: rgba(255, 255, 255, 0.075);
    }
    
    .form-control-plaintext {
        color: #e2e8f0;
    }
    
    .text-muted {
        color: #a0aec0 !important;
    }
}
