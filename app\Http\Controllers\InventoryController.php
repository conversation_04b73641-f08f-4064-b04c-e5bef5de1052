<?php

namespace App\Http\Controllers;

use App\Models\PartInventory;
use App\Models\Site;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use Barryvdh\DomPDF\Facade\Pdf;

class InventoryController extends Controller
{
    public function index()
    {
        // Ambil site pertama dari database
        $firstSite = Site::first();
        $startDate = Carbon::now()->startOfMonth()->toDateString();
        $endDate = Carbon::now()->toDateString();
        $sites = Site::all();

        return view('inventory.index', compact('firstSite', 'startDate', 'endDate', 'sites'));
    }

    public function show(string $id_site)
    {
        $firstSite = Site::find($id_site);
        if (!$firstSite) {
            abort(404, 'Site not found');
        }

        $startDate = Carbon::now()->startOfMonth()->toDateString();
        $endDate = Carbon::now()->toDateString();
        $sites = Site::all();

        return view('inventory.index', compact('firstSite', 'startDate', 'endDate', 'sites'));
    }
    public function getData(Request $request)
    {
        try {
            $siteId = $request->input('site_id');
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');
            $search = $request->input('search');

            // Validate required inputs
            if (!$siteId || !$startDate || !$endDate) {
                return response()->json(['error' => 'Missing required parameters'], 400);
            }

            $inventories = PartInventory::with('part', 'site')
                ->where('site_id', $siteId)
                ->when($search, function ($query) use ($search) {
                    $query->whereHas('part', function ($q) use ($search) {
                        $q->where('part_name', 'like', '%' . $search . '%')
                            ->orWhere('part_code', 'like', '%' . $search . '%');
                    });
                })
                ->get();

            $transformedData = $inventories->map(function ($inventory) use ($startDate, $endDate) {
                // Check if part relationship exists
                if (!$inventory->part) {
                    \Log::warning('Part not found for inventory ID: ' . $inventory->part_inventory_id . ' with part_code: ' . $inventory->part_code);
                    return null;
                }

                // Calculate total in stocks
                $totalIn = DB::table('site_in_stocks')
                    ->where('part_inventory_id', $inventory->part_inventory_id)
                    ->whereBetween('date_in', [$startDate, $endDate])
                    ->sum('quantity') ?? 0;

                // Calculate total out stocks from site_out_stocks
                $siteOutTotal = DB::table('site_out_stocks')
                    ->where('part_inventory_id', $inventory->part_inventory_id)
                    ->whereBetween('date_out', [$startDate, $endDate])
                    ->sum('quantity') ?? 0;

                // Calculate total out stocks from warehouse_out_stocks
                $warehouseOutTotal = DB::table('warehouse_out_stocks')
                    ->where('part_inventory_id', $inventory->part_inventory_id)
                    ->whereBetween('date_out', [$startDate, $endDate])
                    ->sum('quantity') ?? 0;

                // Total out is the sum of both
                $totalOut = $siteOutTotal + $warehouseOutTotal;

                // Ensure min_stock and max_stock are not null
                $minStock = $inventory->min_stock ?? 0;
                $maxStock = $inventory->max_stock ?? 0;
                $averageStock = ($minStock + $maxStock) / 2;
                $status = 'Ready';

                // Rule: Ready is stock part more or same than max stock, or stock 0 but min and max not set = 0
                if ($minStock == 0 && $maxStock == 0) {
                    $status = 'Ready';
                }
                // Rule: Not ready only if min or max not 0, and stock same or less than average min and max stock
                elseif (($minStock > 0 || $maxStock > 0) && $inventory->stock_quantity <= $averageStock) {
                    $status = 'Not Ready';
                }
                // Rule: Medium/yellow status, is for part that have stock more than average min and max but less than max stock
                elseif ($inventory->stock_quantity > $averageStock && $inventory->stock_quantity < $maxStock) {
                    $status = 'Medium';
                }
                // Rule: Ready is stock part more or same than max stock
                elseif ($inventory->stock_quantity >= $maxStock) {
                    $status = 'Ready';
                }

                return [
                    'part_code' => $inventory->part->part_code,
                    'part_name' => $inventory->part->part_name,
                    'stock_quantity' => $inventory->stock_quantity,
                    'min_stock' => $minStock,
                    'max_stock' => $maxStock,
                    'total_in' => $totalIn,
                    'date_priority' => $inventory->date_priority,
                    'priority' => $inventory->priority,
                    'total_out' => $totalOut,
                    'status' => $status,
                    'part_inventory_id' => $inventory->part_inventory_id,
                ];
            })->filter(); // Remove null values

            $sortedData = $transformedData->sortBy(function ($item) {
                $order = [
                    'Not Ready' => 1,
                    'Medium' => 2,
                    'Ready' => 3,
                ];

                return $order[$item['status']] ?? 4;
            })->values()->all();

            return response()->json($sortedData);

        } catch (\Exception $e) {
            \Log::error('Error in InventoryController::getData: ' . $e->getMessage());
            return response()->json(['error' => 'Internal server error'], 500);
        }
    }
    public function getDetail(Request $request)
    {
        $partInventoryId = $request->input('part_inventory_id');
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');

        $inStocks = DB::table('site_in_stocks')
            ->join('users', 'site_in_stocks.employee_id', '=', 'users.employee_id')
            ->leftJoin('suppliers', 'site_in_stocks.supplier_id', '=', 'suppliers.supplier_id')
            ->where('part_inventory_id', $partInventoryId)
            ->whereBetween('date_in', [$startDate, $endDate])
            ->select(
                'site_in_stocks.date_in',
                'site_in_stocks.quantity',
                'users.name as employee_name',
                'suppliers.supplier_name',
                'site_in_stocks.notes'
            )
            ->get();


        // Ambil data dari site_out_stocks
        $siteOutStocks = DB::table('site_out_stocks')
            ->join('users', 'site_out_stocks.employee_id', '=', 'users.employee_id')
            ->where('part_inventory_id', $partInventoryId)
            ->whereBetween('date_out', [$startDate, $endDate])
            ->select(
                'site_out_stocks.date_out AS date_out',
                'site_out_stocks.quantity',
                'site_out_stocks.status',
                'users.name as employee_name',
                'site_out_stocks.notes',
                DB::raw('"site" as source_type')
            )
            ->get();

        // Ambil data dari warehouse_out_stocks
        $warehouseOutStocks = DB::table('warehouse_out_stocks')
            ->join('users', 'warehouse_out_stocks.employee_id', '=', 'users.employee_id')
            ->where('part_inventory_id', $partInventoryId)
            ->whereBetween('date_out', [$startDate, $endDate])
            ->select(
                'warehouse_out_stocks.date_out AS date_out',
                'warehouse_out_stocks.quantity',
                DB::raw('"" as status'),
                'users.name as employee_name',
                'warehouse_out_stocks.notes',
                DB::raw('"warehouse" as source_type')
            )
            ->get();

        // Gabungkan data out stocks
        $outStocks = $siteOutStocks->concat($warehouseOutStocks);
        return response()->json(['in' => $inStocks, 'out' => $outStocks]);
    }

    // Ambil site mana saja yang ada not ready
    public function getNotReadySiteNames()
    {
        $notReadySites = PartInventory::join('parts', 'part_inventories.part_code', '=', 'parts.part_code')
            ->join('sites', 'part_inventories.site_id', '=', 'sites.site_id')
            ->where('part_inventories.site_id', '!=', session('site_id'))
            ->where(function ($query) {
                $query->where('part_inventories.min_stock', '>', 0)
                    ->where('part_inventories.max_stock', '>', 0)
                    ->whereColumn('part_inventories.stock_quantity', '<=', 'part_inventories.min_stock');
            })
            ->distinct('part_inventories.site_id')
            ->pluck('sites.site_name')
            ->toArray();
        return response()->json(['sites' => $notReadySites]);
    }

    /**
     * Export inventory data to Excel
     */
    public function exportExcel(Request $request)
    {
        try {
            $siteId = $request->input('site_id');
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');
            $search = $request->input('search');

            // Access control
            $userRole = session('role');
            $userSiteId = session('site_id');

            // Site users can only export their own data
            if (in_array($userRole, ['adminsite', 'karyawan'])) {
                $siteId = $userSiteId;
            }

            // Validate required inputs
            if (!$siteId || !$startDate || !$endDate) {
                return response()->json(['error' => 'Missing required parameters'], 400);
            }

            // Get inventory data using the same logic as getData method
            $inventoryData = $this->getInventoryDataForExport($siteId, $startDate, $endDate, $search);

            // Create Excel file
            $spreadsheet = new Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();

            // Set headers
            $headers = [
                'A1' => 'Kode Part',
                'B1' => 'Nama Part',
                'C1' => 'Stok Saat Ini',
                'D1' => 'Min Stock',
                'E1' => 'Max Stock',
                'F1' => 'Jumlah Masuk',
                'G1' => 'Jumlah Keluar',
                'H1' => 'Status'
            ];

            foreach ($headers as $cell => $header) {
                $sheet->setCellValue($cell, $header);
            }

            // Style headers
            $headerStyle = [
                'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
                'fill' => ['fillType' => Fill::FILL_SOLID, 'startColor' => ['rgb' => '4472C4']],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
                'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]]
            ];
            $sheet->getStyle('A1:H1')->applyFromArray($headerStyle);

            // Add data
            $row = 2;
            foreach ($inventoryData as $item) {
                $sheet->setCellValue('A' . $row, $item['part_code']);
                $sheet->setCellValue('B' . $row, $item['part_name']);
                $sheet->setCellValue('C' . $row, $item['stock_quantity']);
                $sheet->setCellValue('D' . $row, $item['min_stock']);
                $sheet->setCellValue('E' . $row, $item['max_stock']);
                $sheet->setCellValue('F' . $row, $item['total_in']);
                $sheet->setCellValue('G' . $row, $item['total_out']);
                $sheet->setCellValue('H' . $row, $item['status']);

                // Color code status
                $statusColor = '';
                switch ($item['status']) {
                    case 'Not Ready':
                        $statusColor = 'FF6B6B'; // Red
                        break;
                    case 'Medium':
                        $statusColor = 'FFD93D'; // Yellow
                        break;
                    case 'Ready':
                        $statusColor = '6BCF7F'; // Green
                        break;
                }

                if ($statusColor) {
                    $sheet->getStyle('H' . $row)->applyFromArray([
                        'fill' => ['fillType' => Fill::FILL_SOLID, 'startColor' => ['rgb' => $statusColor]]
                    ]);
                }

                $row++;
            }

            // Auto-size columns
            foreach (range('A', 'H') as $column) {
                $sheet->getColumnDimension($column)->setAutoSize(true);
            }

            // Add borders to all data
            $sheet->getStyle('A1:H' . ($row - 1))->applyFromArray([
                'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]]
            ]);

            // Get site name for filename
            $site = Site::find($siteId);
            $siteName = $site ? $site->site_name : 'Unknown';
            $filename = 'Inventory_Monitoring_' . $siteName . '_' . date('Y-m-d_H-i-s') . '.xlsx';

            // Set headers for download
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header("Content-Disposition: attachment;filename=\"{$filename}\"");
            header('Cache-Control: max-age=0');

            $writer = new Xlsx($spreadsheet);
            $writer->save('php://output');
            exit;

        } catch (\Exception $e) {
            Log::error('Error exporting inventory Excel: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat export Excel: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export inventory data to PDF
     */
    public function exportPdf(Request $request)
    {
        try {
            $siteId = $request->input('site_id');
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');
            $search = $request->input('search');

            // Access control
            $userRole = session('role');
            $userSiteId = session('site_id');

            // Site users can only export their own data
            if (in_array($userRole, ['adminsite', 'karyawan'])) {
                $siteId = $userSiteId;
            }

            // Validate required inputs
            if (!$siteId || !$startDate || !$endDate) {
                return response()->json(['error' => 'Missing required parameters'], 400);
            }

            // Get inventory data
            $inventoryData = $this->getInventoryDataForExport($siteId, $startDate, $endDate, $search);
            $site = Site::find($siteId);

            $data = [
                'inventoryData' => $inventoryData,
                'site' => $site,
                'startDate' => $startDate,
                'endDate' => $endDate,
                'search' => $search,
                'exportDate' => now()->format('d-m-Y H:i:s')
            ];

            $pdf = Pdf::loadView('inventory.export-pdf', $data);
            $pdf->setPaper('a4', 'landscape');

            $filename = 'Inventory_Monitoring_' . ($site ? $site->site_name : 'Unknown') . '_' . date('Y-m-d_H-i-s') . '.pdf';

            return $pdf->download($filename);

        } catch (\Exception $e) {
            Log::error('Error exporting inventory PDF: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat export PDF: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get inventory data for export (reusable method)
     */
    private function getInventoryDataForExport($siteId, $startDate, $endDate, $search = null)
    {
        $userRole = session('role');

        $inventories = PartInventory::with('part', 'site')
            ->where('site_id', $siteId)
            ->when($search, function ($query) use ($search) {
                $query->whereHas('part', function ($q) use ($search) {
                    $q->where('part_name', 'like', '%' . $search . '%')
                        ->orWhere('part_code', 'like', '%' . $search . '%');
                });
            })
            ->when($userRole === 'superadmin', function ($query) {
                // Exclude specific part types for superadmin
                $query->whereHas('part', function ($q) {
                    $q->whereNotIn('part_type', ['PERSEDIAAN LAINNYA', 'PERLENGKAPAN AC']);
                });
            })
            ->get();

        $transformedData = $inventories->map(function ($inventory) use ($startDate, $endDate) {
            // Check if part relationship exists
            if (!$inventory->part) {
                Log::warning('Part not found for inventory ID: ' . $inventory->part_inventory_id . ' with part_code: ' . $inventory->part_code);
                return null;
            }

            // Calculate total in stocks
            $totalIn = DB::table('site_in_stocks')
                ->where('part_inventory_id', $inventory->part_inventory_id)
                ->whereBetween('date_in', [$startDate, $endDate])
                ->sum('quantity') ?? 0;

            // Calculate total out stocks from site_out_stocks
            $siteOutTotal = DB::table('site_out_stocks')
                ->where('part_inventory_id', $inventory->part_inventory_id)
                ->whereBetween('date_out', [$startDate, $endDate])
                ->sum('quantity') ?? 0;

            // Calculate total out stocks from warehouse_out_stocks
            $warehouseOutTotal = DB::table('warehouse_out_stocks')
                ->where('part_inventory_id', $inventory->part_inventory_id)
                ->whereBetween('date_out', [$startDate, $endDate])
                ->sum('quantity') ?? 0;

            // Total out is the sum of both
            $totalOut = $siteOutTotal + $warehouseOutTotal;

            // Ensure min_stock and max_stock are not null
            $minStock = $inventory->min_stock ?? 0;
            $maxStock = $inventory->max_stock ?? 0;
            $averageStock = ($minStock + $maxStock) / 2;
            $status = 'Ready';

            // Apply stock status rules
            if ($minStock == 0 && $maxStock == 0) {
                $status = 'Ready';
            }
            elseif (($minStock > 0 || $maxStock > 0) && $inventory->stock_quantity <= $averageStock) {
                $status = 'Not Ready';
            }
            elseif ($inventory->stock_quantity > $averageStock && $inventory->stock_quantity < $maxStock) {
                $status = 'Medium';
            }
            elseif ($inventory->stock_quantity >= $maxStock) {
                $status = 'Ready';
            }

            return [
                'part_code' => $inventory->part->part_code,
                'part_name' => $inventory->site_part_name ?: $inventory->part->part_name,
                'stock_quantity' => $inventory->stock_quantity,
                'min_stock' => $minStock,
                'max_stock' => $maxStock,
                'total_in' => $totalIn,
                'total_out' => $totalOut,
                'status' => $status,
                'part_inventory_id' => $inventory->part_inventory_id,
            ];
        })->filter(); // Remove null values

        // Sort by status priority
        $sortedData = $transformedData->sortBy(function ($item) {
            $order = [
                'Not Ready' => 1,
                'Medium' => 2,
                'Ready' => 3,
            ];

            return $order[$item['status']] ?? 4;
        })->values()->all();

        return $sortedData;
    }
}
