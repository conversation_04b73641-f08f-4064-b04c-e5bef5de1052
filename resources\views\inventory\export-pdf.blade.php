<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Laporan Inventory Monitoring</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 10px;
            margin: 0;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #333;
            padding-bottom: 10px;
        }
        .header h1 {
            margin: 0;
            font-size: 18px;
            color: #333;
        }
        .header h2 {
            margin: 5px 0;
            font-size: 14px;
            color: #666;
        }
        .info-section {
            margin-bottom: 15px;
            display: flex;
            justify-content: space-between;
        }
        .info-left, .info-right {
            width: 48%;
        }
        .info-item {
            margin-bottom: 5px;
        }
        .info-label {
            font-weight: bold;
            display: inline-block;
            width: 120px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 6px;
            text-align: left;
            font-size: 9px;
        }
        th {
            background-color: #4472C4;
            color: white;
            font-weight: bold;
            text-align: center;
        }
        .status-ready {
            background-color: #d4edda;
            color: #155724;
            font-weight: bold;
        }
        .status-medium {
            background-color: #fff3cd;
            color: #856404;
            font-weight: bold;
        }
        .status-not-ready {
            background-color: #f8d7da;
            color: #721c24;
            font-weight: bold;
        }
        .text-center {
            text-align: center;
        }
        .text-right {
            text-align: right;
        }
        .footer {
            margin-top: 20px;
            text-align: center;
            font-size: 8px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }
        .summary {
            margin-bottom: 15px;
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
        }
        .summary-item {
            display: inline-block;
            margin-right: 20px;
            font-weight: bold;
        }
        .summary-ready { color: #28a745; }
        .summary-medium { color: #ffc107; }
        .summary-not-ready { color: #dc3545; }
    </style>
</head>
<body>
    <div class="header">
        <h1>LAPORAN INVENTORY MONITORING</h1>
        <h2>{{ $site->site_name ?? 'Unknown Site' }}</h2>
    </div>

    <div class="info-section">
        <div class="info-left">
            <div class="info-item">
                <span class="info-label">Site:</span>
                {{ $site->site_name ?? 'Unknown Site' }}
            </div>
            <div class="info-item">
                <span class="info-label">Periode:</span>
                {{ \Carbon\Carbon::parse($startDate)->format('d-m-Y') }} s/d {{ \Carbon\Carbon::parse($endDate)->format('d-m-Y') }}
            </div>
            @if($search)
            <div class="info-item">
                <span class="info-label">Filter Pencarian:</span>
                {{ $search }}
            </div>
            @endif
        </div>
        <div class="info-right">
            <div class="info-item">
                <span class="info-label">Tanggal Export:</span>
                {{ $exportDate }}
            </div>
            <div class="info-item">
                <span class="info-label">Total Data:</span>
                {{ count($inventoryData) }} item
            </div>
        </div>
    </div>

    @php
        $readyCount = collect($inventoryData)->where('status', 'Ready')->count();
        $mediumCount = collect($inventoryData)->where('status', 'Medium')->count();
        $notReadyCount = collect($inventoryData)->where('status', 'Not Ready')->count();
    @endphp

    <div class="summary">
        <span class="summary-item summary-ready">Ready: {{ $readyCount }}</span>
        <span class="summary-item summary-medium">Medium: {{ $mediumCount }}</span>
        <span class="summary-item summary-not-ready">Not Ready: {{ $notReadyCount }}</span>
    </div>

    <table>
        <thead>
            <tr>
                <th style="width: 8%;">No</th>
                <th style="width: 12%;">Kode Part</th>
                <th style="width: 25%;">Nama Part</th>
                <th style="width: 8%;">Stok Saat Ini</th>
                <th style="width: 8%;">Min Stock</th>
                <th style="width: 8%;">Max Stock</th>
                <th style="width: 8%;">Jumlah Masuk</th>
                <th style="width: 8%;">Jumlah Keluar</th>
                <th style="width: 15%;">Status</th>
            </tr>
        </thead>
        <tbody>
            @foreach($inventoryData as $index => $item)
            <tr>
                <td class="text-center">{{ $index + 1 }}</td>
                <td>{{ $item['part_code'] }}</td>
                <td>{{ $item['part_name'] }}</td>
                <td class="text-center">{{ number_format($item['stock_quantity'], 0, ',', '.') }}</td>
                <td class="text-center">{{ number_format($item['min_stock'], 0, ',', '.') }}</td>
                <td class="text-center">{{ number_format($item['max_stock'], 0, ',', '.') }}</td>
                <td class="text-center">{{ number_format($item['total_in'], 0, ',', '.') }}</td>
                <td class="text-center">{{ number_format($item['total_out'], 0, ',', '.') }}</td>
                <td class="text-center 
                    @if($item['status'] === 'Ready') status-ready
                    @elseif($item['status'] === 'Medium') status-medium
                    @elseif($item['status'] === 'Not Ready') status-not-ready
                    @endif">
                    {{ $item['status'] }}
                </td>
            </tr>
            @endforeach
        </tbody>
    </table>

    <div class="footer">
        <p>Laporan ini dibuat secara otomatis oleh sistem Portal PWB pada {{ $exportDate }}</p>
        <p>Data inventory berdasarkan transaksi masuk dan keluar dalam periode yang dipilih</p>
    </div>
</body>
</html>
