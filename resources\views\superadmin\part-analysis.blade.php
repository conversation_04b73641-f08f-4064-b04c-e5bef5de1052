<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <title>Portal PWB - Part Analysis</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta content="Portal PWB" name="description" />
    <meta content="PWB" name="author" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <link rel="shortcut icon" href="{{ asset('assets/images/logo-small.png') }}">
    <!-- Styles -->
    @vite([
    'resources/assets/css/bootstrap.min.css',
    'resources/assets/css/icons.min.css',
    'resources/assets/css/app.min.css',
    'resources/css/app.css',
    'resources/css/superadmin-dashboard.css',
    'resources/css/superadmin-scaling.css'
    ])
    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- SweetAlert2 CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.min.css">
    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.all.min.js"></script>

    <style>
        /* CSS Variables for consistent theming */
        :root {
            --primary-color: #225297;
            --secondary-color: #58c0f6;
            --danger-color: #eb3124;
            --success-color: #97f784;
            --warning-color: #feff8c;
            --dark-color: #510968;
            --info-color: #58c0f6;
            --accent-color: #feff8c;
            --text-color: #333;
            --text-muted: #6c757d;
            --border-color: #e0e0e0;
            --card-bg-color: #ffffff;
        }

        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            background: url('{{ asset('assets/images/homewalpaper.jpg') }}');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            font-family: 'Segoe UI', sans-serif;
            position: relative;
            overflow-x: hidden;
        }

        .dashboard-container {
            width: 100%;
            max-width: 1600px;
            padding: 0 15px;
            margin: 0 auto;
        }

        .login-theme-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            background-color: #fff;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            position: sticky;
            top: 0;
            z-index: 100;
            margin-bottom: 20px;
            border-radius: 0 0 10px 10px;
        }

        .header-top {
            display: none;
        }

        .company-logo {
            display: flex;
            align-items: center;
        }

        .company-logo img {
            height: 40px;
            margin-right: 10px;
        }

        .company-name {
            font-size: 1.2rem;
            font-weight: 700;
            margin: 0;
            color: var(--primary-color);
        }

        .mobile-menu-toggle {
            display: none;
            background: none;
            border: none;
            color: var(--primary-color);
            font-size: 1.5rem;
            cursor: pointer;
            padding: 5px;
            border-radius: 5px;
            transition: all 0.3s ease;
        }

        .mobile-menu-toggle:hover {
            background-color: rgba(42, 105, 168, 0.1);
        }

        .mobile-menu-close {
            display: none;
            background: none;
            border: none;
            color: var(--danger-color);
            font-size: 1.5rem;
            cursor: pointer;
            padding: 5px;
            border-radius: 5px;
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 1001;
            transition: all 0.3s ease;
        }

        .mobile-menu-close:hover {
            background-color: rgba(42, 105, 168, 0.1);
        }

        .mobile-menu-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 998;
        }

        .header-right {
            display: flex;
            align-items: center;
            justify-content: flex-end;
        }

        .nav-links {
            display: flex;
            flex-wrap: wrap;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 8px 15px;
            margin: 0 5px 5px 0;
            border-radius: 8px;
            text-decoration: none;
            color: var(--primary-color);
            background-color: rgba(42, 105, 168, 0.1);
            transition: all 0.3s ease;
            border: 1px solid var(--primary-color);
        }

        .nav-link i {
            margin-right: 8px;
            font-size: 1.1rem;
        }

        .nav-link:hover {
            background-color: var(--primary-color);
            color: #fff;
        }

        .nav-link.active {
            background-color: var(--primary-color);
            color: #fff;
        }

        .nav-link-danger {
            color: var(--danger-color) !important;
            background-color: rgba(255, 93, 72, 0.1) !important;
            border: 1px solid var(--danger-color) !important;
        }

        .nav-link-danger:hover {
            background-color: var(--danger-color) !important;
            color: #fff !important;
        }

        .card {
            background-color: var(--card-bg-color) !important;
            border-radius: 10px !important;
            border: 1px solid var(--border-color) !important;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            margin-bottom: 1rem;
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .card-header {
            background-color: rgba(42, 105, 168, 0.05) !important;
            border-bottom: 1px solid var(--border-color) !important;
            padding: 0.75rem 1rem;
        }

        .card-body {
            padding: 1rem;
        }

        .status-ready {
            background-color: var(--success-color);
            color: #343a40;
        }

        .status-not-ready {
            background-color: var(--danger-color);
            color: white;
        }

        .classification-high-demand {
            background-color: var(--danger-color);
            color: white;
        }

        .classification-stable {
            background-color: var(--success-color);
            color: #343a40;
        }

        .classification-low-demand {
            background-color: var(--warning-color);
            color: #343a40;
        }

        .classification-seasonal {
            background-color: var(--secondary-color);
            color: white;
        }

        .classification-overstock {
            background-color: var(--dark-color);
            color: white;
        }

        .classification-uncategorized {
            background-color: #6c757d;
            color: white;
        }

        .classification-dormant {
            background-color: #f8f9fa;
            color: #343a40;
            border: 1px solid #dee2e6;
        }

        .table-responsive {
            max-height: 70vh;
            overflow-y: auto;
        }

        .sticky-header {
            position: sticky;
            top: 0;
            z-index: 10;
            background-color: #f8f9fa;
        }

        .form-control {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            color: var(--text-color);
            font-size: 1em;
            transition: all 0.3s ease;
            padding: 0.5rem 1rem;
        }

        .form-control:focus {
            outline: none;
            background: #ffffff;
            color: var(--text-color);
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(42, 105, 168, 0.25);
        }

        .content {
            padding: 10px 5px;
            max-width: 100%;
            margin: 0 auto;
        }

        /* Mobile responsive styles */
        @media (max-width: 992px) {
            .header-top {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 15px 20px;
                background-color: #fff;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
                position: sticky;
                top: 0;
                z-index: 100;
                margin-bottom: 20px;
                border-radius: 0 0 10px 10px;
            }

            .login-theme-header {
                display: none;
            }

            .mobile-menu-toggle {
                display: block;
            }

            .header-right {
                position: fixed;
                top: 0;
                right: -100%;
                width: 85%;
                max-width: 300px;
                height: auto;
                max-height: 80vh;
                background-color: #fff;
                box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
                padding: 15px;
                z-index: 999;
                overflow-y: auto;
                transition: right 0.3s ease;
                flex-direction: column;
                border-radius: 0 0 0 10px;
            }

            .header-right.active {
                right: 0;
            }

            .mobile-menu-overlay.active {
                display: block;
            }

            .mobile-menu-close {
                display: block;
            }

            .nav-links {
                flex-direction: column;
                width: 100%;
            }

            .nav-link {
                margin: 0 0 10px 0;
                width: 100%;
                justify-content: flex-start;
            }
        }

        @media (min-width: 993px) {
            .header-right {
                position: static;
                width: auto;
                max-width: none;
                height: auto;
                max-height: none;
                background-color: transparent;
                box-shadow: none;
                padding: 0;
                overflow-y: visible;
                transition: none;
                flex-direction: row;
                border-radius: 0;
            }

            .mobile-menu-close {
                display: none;
            }
        }
        .fade{
            display: none;
        }
    </style>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-refresh data when filters change
        const siteSelect = document.getElementById('site-select');
        const startDateInput = document.getElementById('start-date');
        const endDateInput = document.getElementById('end-date');
        const statusFilter = document.getElementById('status-filter');
        const classificationFilter = document.getElementById('classification-filter');
        
        function refreshData() {
            const siteId = siteSelect.value;
            const startDate = startDateInput.value;
            const endDate = endDateInput.value;
            
            // if (!siteId || !startDate || !endDate) {
            //     return;
            // }
            
            // Show loading
            const tableBody = document.getElementById('analysis-table-body');
            tableBody.innerHTML = '<tr><td colspan="8" class="text-center"><i class="mdi mdi-loading mdi-spin"></i> Memuat data...</td></tr>';
            
            // Fetch data
            fetch(`{{ route('superadmin.part-analysis') }}?site_id=${siteId}&start_date=${startDate}&end_date=${endDate}`, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                window.currentAnalysisData = data; // Store data globally for filtering
                updateTable(data);
                updateSummary(data);
            })
            .catch(error => {
                console.error('Error:', error);
                tableBody.innerHTML = '<tr><td colspan="8" class="text-center text-danger">Terjadi kesalahan saat memuat data</td></tr>';
            });
        }
        
        function updateTable(response) {
            const tableBody = document.getElementById('analysis-table-body');

            if (!response.data || response.data.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="8" class="text-center text-muted">Tidak ada data untuk ditampilkan</td></tr>';
                return;
            }

            // Apply filters
            let filteredData = response.data;

            const statusFilterValue = statusFilter.value;
            const classificationFilterValue = classificationFilter.value;

            if (statusFilterValue) {
                filteredData = filteredData.filter(item => item.status === statusFilterValue);
            }

            if (classificationFilterValue) {
                filteredData = filteredData.filter(item => item.analysis_description === classificationFilterValue);
            }

            if (filteredData.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="8" class="text-center text-muted">Tidak ada data yang sesuai dengan filter</td></tr>';
                return;
            }

            let html = '';
            filteredData.forEach((item, index) => {
                const statusClass = item.status === 'Ready' ? 'status-ready' : 'status-not-ready';
                const classificationClass = getClassificationClass(item.analysis_description);

                html += `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${item.part_name}</td>
                        <td>${item.part_code}</td>
                        <td class="text-end">${item.current_stock.toLocaleString('id-ID')}</td>
                        <td class="text-end">${item.in_stock.toLocaleString('id-ID')}</td>
                        <td class="text-end">${item.out_stock.toLocaleString('id-ID')}</td>
                        <td><span class="badge ${statusClass}">${item.status}</span></td>
                        <td><span class="badge ${classificationClass}">${item.analysis_description}</span></td>
                    </tr>
                `;
            });

            tableBody.innerHTML = html;

            // Update summary with filtered count
            updateSummary(response, filteredData.length);
        }
        
        function updateSummary(response, filteredCount = null) {
            const displayCount = filteredCount !== null ? filteredCount : response.total_parts;
            const filterText = filteredCount !== null && filteredCount !== response.total_parts ? ` (${filteredCount} dari ${response.total_parts} ditampilkan)` : '';
        }
        
        function getClassificationClass(classification) {
            const classMap = {
                'High Demand': 'classification-high-demand',
                'Stable': 'classification-stable',
                'Low Demand': 'classification-low-demand',
                'Seasonal': 'classification-seasonal',
                'Overstock': 'classification-overstock',
                'Uncategorized': 'classification-uncategorized',
                'Dormant': 'classification-dormant'
            };

            return classMap[classification] || 'classification-uncategorized';
        }
        
        // Add event listeners
        siteSelect.addEventListener('change', refreshData);
        startDateInput.addEventListener('change', refreshData);
        endDateInput.addEventListener('change', refreshData);
        statusFilter.addEventListener('change', refreshData);
        classificationFilter.addEventListener('change', refreshData);
    });
    </script>

    <!-- Vite JS Resources -->
    @vite([
    'resources/js/superadmin-scaling.js',
    'resources/js/superadmin-mobile-menu.js'
    ])
</head>

<body>
    <!-- Dashboard Container -->
    <div class="dashboard-container">
        <!-- Login Theme Header -->
        <header class="login-theme-header">
            <!-- Company Logo (Visible on all devices) -->
            <div class="company-logo">
                <img src="{{ asset('assets/images/logo-small.png') }}" alt="PWB Logo">
                <h1 class="company-name">PT. PUTERA WIBOWO BORNEO</h1>
            </div>

            <!-- Mobile Header Top (Only visible on mobile) -->
            <div class="header-top">
                <div class="company-logo">
                    <img src="{{ asset('assets/images/logo-small.png') }}" alt="PWB Logo">
                    <h1 class="company-name">PT. PUTERA WIBOWO BORNEO</h1>
                </div>

                <!-- Mobile Menu Toggle Button -->
                <button type="button" class="mobile-menu-toggle" id="mobileMenuToggle">
                    <i class="mdi mdi-menu"></i>
                </button>
            </div>

            <!-- Mobile Menu Overlay -->
            <div class="mobile-menu-overlay" id="mobileMenuOverlay"></div>

            <!-- Header Right (Navigation) -->
            <div class="header-right" id="mobileMenu">
                <!-- Mobile Menu Close Button (Only visible on mobile) -->
                <button type="button" class="mobile-menu-close" id="mobileMenuClose">
                    <i class="mdi mdi-close"></i>
                </button>

                <div class="nav-links">
                    <a href="{{ route('superadmin.dashboard') }}" class="nav-link">
                        <i class="mdi mdi-view-dashboard"></i> <span>Dashboard</span>
                    </a>
                    <a href="{{ route('superadmin.invoices') }}" class="nav-link">
                        <i class="mdi mdi-file-document-outline"></i> <span>Account Receivable</span>
                    </a>
                    <a href="{{ route('superadmin.parts') }}" class="nav-link">
                        <i class="mdi mdi-package-variant"></i> <span>Part</span>
                    </a>
                    <!-- <a href="{{ route('superadmin.part-analysis') }}" class="nav-link active">
                        <i class="mdi mdi-chart-line"></i> <span>Part Analysis</span>
                    </a> -->
                    <a href="{{ route('superadmin.price-list') }}" class="nav-link">
                        <i class="mdi mdi-tag-multiple"></i> <span>Price List</span>
                    </a>
                    <a href="{{ route('logout') }}" class="nav-link nav-link-danger">
                        <i class="mdi mdi-logout"></i> <span>Logout</span>
                    </a>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <div class="content">
            <div class="container-fluid mt-3">
                <div class="row">
                    <div class="col-md-12">
                        <div class="card">

                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0 font-bold text-uppercase" style="color: var(--primary-color);">Part Analysis</h5>
                                <div class="d-flex align-items-center">
                                    <button type="button" class="btn btn-outline-primary btn-sm" data-bs-toggle="modal" data-bs-target="#helpModal" title="Bantuan">
  <i class="mdi mdi-help-circle"></i>
</button>

                                </div>
                            </div>
                            
                            <!-- Filter Section -->
                            <div class="card-body border-bottom">
                                <div class="row g-3">
                                    <div class="col-md-3">
                                        <label for="site-select" class="form-label">Site</label>
                                        <select class="form-control" id="site-select" name="site_id">
                                            <option value="">Pilih Site</option>
                                            @foreach($sites as $site)
                                                <option value="{{ $site->site_id }}" {{ $selectedSite == $site->site_id ? 'selected' : '' }}>
                                                    {{ $site->site_name }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <label for="start-date" class="form-label">Tanggal Mulai</label>
                                        <input type="date" class="form-control" id="start-date" name="start_date" value="{{ $startDate }}">
                                    </div>
                                    <div class="col-md-2">
                                        <label for="end-date" class="form-label">Tanggal Akhir</label>
                                        <input type="date" class="form-control" id="end-date" name="end_date" value="{{ $endDate }}">
                                    </div>
                                    <div class="col-md-2">
                                        <label for="status-filter" class="form-label">Status</label>
                                        <select class="form-control" id="status-filter">
                                            <option value="">Semua Status</option>
                                            <option value="Ready">Ready</option>
                                            <option value="Not Ready">Not Ready</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label for="classification-filter" class="form-label">Klasifikasi</label>
                                        <select class="form-control" id="classification-filter">
                                            <option value="">Semua Klasifikasi</option>
                                            <option value="High Demand">High Demand</option>
                                            <option value="Stable">Stable</option>
                                            <option value="Low Demand">Low Demand</option>
                                            <option value="Seasonal">Seasonal</option>
                                            <option value="Overstock">Overstock</option>
                                            <option value="Dormant">Dormant</option>
                                            <option value="Uncategorized">Uncategorized</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Table Section -->
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-hover w-100">
                                        <thead class="sticky-header p-2" style="font-size: 11px;">
                                            <tr>
                                                <th>No</th>
                                                <th>Part Name</th>
                                                <th>Part Code</th>
                                                <th>Current Stock</th>
                                                <th>In Stock</th>
                                                <th>Out Stock</th>
                                                <th>Status</th>
                                                <th>Analysis Description</th>
                                            </tr>
                                        </thead>
                                        <tbody id="analysis-table-body" style="font-size: 11px;">
                                            @if(isset($analysisData) && !empty($analysisData['data']))
                                                @foreach($analysisData['data'] as $index => $item)
                                                <tr>
                                                    <td>{{ $index + 1 }}</td>
                                                    <td>{{ $item['part_name'] }}</td>
                                                    <td>{{ $item['part_code'] }}</td>
                                                    <td class="text-end">{{ number_format($item['current_stock'], 0, ',', '.') }}</td>
                                                    <td class="text-end">{{ number_format($item['in_stock'], 0, ',', '.') }}</td>
                                                    <td class="text-end">{{ number_format($item['out_stock'], 0, ',', '.') }}</td>
                                                    <td>
                                                        <span class="badge {{ $item['status'] === 'Ready' ? 'status-ready' : 'status-not-ready' }}">
                                                            {{ $item['status'] }}
                                                        </span>
                                                    </td>
                                                    <td>
                                                        @php
                                                            $classificationClass = '';
                                                            switch($item['analysis_description']) {
                                                                case 'High Demand':
                                                                    $classificationClass = 'classification-high-demand';
                                                                    break;
                                                                case 'Stable':
                                                                    $classificationClass = 'classification-stable';
                                                                    break;
                                                                case 'Low Demand':
                                                                    $classificationClass = 'classification-low-demand';
                                                                    break;
                                                                case 'Seasonal':
                                                                    $classificationClass = 'classification-seasonal';
                                                                    break;
                                                                case 'Overstock':
                                                                    $classificationClass = 'classification-overstock';
                                                                    break;
                                                                case 'Dormant':
                                                                    $classificationClass = 'classification-dormant';
                                                                    break;
                                                                default:
                                                                    $classificationClass = 'classification-uncategorized';
                                                            }
                                                        @endphp
                                                        <span class="badge {{ $classificationClass }}">
                                                            {{ $item['analysis_description'] }}
                                                        </span>
                                                    </td>
                                                </tr>
                                                @endforeach
                                            @else
                                                <tr>
                                                    <td colspan="8" class="text-center text-muted">Pilih site dan tanggal untuk melihat analisis part</td>
                                                </tr>
                                            @endif
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                </div>
                </div>
                <!-- Help Modal -->
<div class="modal fade" tabindex="-1" id="helpModal" aria-labelledby="helpModalLabel" aria-hidden="true">
    <div class="modal-dialog" style="min-width: 50%;">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="helpModalLabel">
                    <i class="mdi mdi-help-circle me-2"></i>Panduan Part Analysis
                </h5>
                 <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-12">
                        <div class="p-4 row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="d-flex align-items-center mb-1">
                                        <span class="badge classification-high-demand mr-2">High Demand</span>
                                        <strong>Permintaan Tinggi</strong>
                                    </div>
                                    <small class="text-muted">Part memiliki in dan out yang banyak</small><br>
                                    <small class="text-muted">Kesimpulan : Perhatikan stock, Set MIN dan MAX stock yang besar</small>
                                </div>
                                <div class="mb-3">
                                    <div class="d-flex align-items-center mb-1">
                                        <span class="badge classification-stable mr-2">Stable</span>
                                        <strong>Stabil</strong>
                                    </div>
                                    <small class="text-muted">- Part dengan pola masuk dan keluar yang seimbang. Manajemen stok normal.</small><br>
                                    <small class="text-muted">- In dan Out tidak terlalu berbeda</small><br>
                                    <small class="text-muted">kesimpulan : Perhatikan Stock, Set Min dan Max tidak terlalu kecil</small>
                                </div>

                                <div class="mb-3">
                                    <div class="d-flex align-items-center mb-1">
                                        <span class="badge classification-low-demand mr-2">Low Demand</span>
                                        <strong>Out Stock Renda</strong>
                                    </div>
                                    <small class="text-muted">Part dengan permintaan sedikit</small><br>
                                    <small class="text-muted">Kesimpulan : Set min dan max kecil, jangan terlalu banyak supplay</small>
                                </div>

                                <div class="mb-3">
                                    <div class="d-flex align-items-center mb-1">
                                        <span class="badge classification-overstock mr-2">Overstock</span>
                                        <strong>Kelebihan Stok</strong>
                                    </div>
                                    <small class="text-muted">Stok jauh melebihi pola penggunaan.</small><br>
                                    <small class="text-muted">Kesimpulan : Pertimbangkan untuk mengurangi stok. karena terlalu banyak IN</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="d-flex align-items-center mb-1">
                                        <span class="badge classification-dormant mr-2">Dormant</span>
                                        <strong>Tidak Aktif</strong>
                                    </div>
                                    <small class="text-muted">- Part memiliki stok tapi tidak keluar namun ada stock</small><br>
                                    <small class="text-muted">Kesimpulan : Pertimbangkan untuk mengurangi stok. atau menarik stock dari site</small>
                                </div>

                                <div class="mb-3">
                                    <div class="d-flex align-items-center mb-1">
                                        <span class="badge classification-uncategorized mr-2">Uncategorized</span>
                                        <strong>Tidak Terkategori</strong>
                                    </div>
                                    <small class="text-muted">Part tanpa stok dan tanpa aktivitas transaksi sama sekali.</small>
                                </div>
                                <div class="mb-3">
                                    <div class="d-flex align-items-center mb-1">
                                        <span class="badge classification-seasonal mr-2">Seasonal</span>
                                        <strong>Musiman</strong>
                                    </div>
                                    <small class="text-muted">Part dengan pola penggunaan tidak teratur, hanya digunakan pada waktu tertentu.</small><br>
                                    <small class="text-muted">kesimpulan : Kurangi min dan max stock, jangan terlalu menyimpan banyak Stock</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
            </div>
        </div>
    </div>
</body>
</html>
