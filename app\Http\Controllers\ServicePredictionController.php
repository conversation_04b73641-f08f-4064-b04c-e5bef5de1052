<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\DailyReport;
use App\Models\Unit;
use Carbon\Carbon;

class ServicePredictionController extends Controller
{
    /**
     * Get predicted next service date for a unit
     */
    public function getPredictedServiceDate(Request $request, $unitId)
    {
        try {
            // Get the latest HM entry for the unit
            $latestReport = DailyReport::where('unit_id', $unitId)
                ->whereNotNull('hm')
                ->orderBy('date_in', 'desc')
                ->orderBy('created_at', 'desc')
                ->first();

            if (!$latestReport) {
                return response()->json([
                    'success' => false,
                    'message' => 'No HM data found for this unit'
                ]);
            }

            $currentHM = $latestReport->hm;
            $lastServiceDate = Carbon::parse($latestReport->date_in);

            // Calculate next service HM (next multiple of 250)
            $nextServiceHM = ceil($currentHM / 250) * 250;
            
            // If current HM is already at a service interval, add 250
            if ($currentHM % 250 == 0) {
                $nextServiceHM = $currentHM + 250;
            }

            // Calculate remaining HM until next service
            $remainingHM = $nextServiceHM - $currentHM;

            // Calculate days until service (assuming 20 HM per day average usage)
            $averageHMPerDay = 20;
            $daysUntilService = ceil($remainingHM / $averageHMPerDay);

            // Calculate predicted service date
            $predictedServiceDate = $lastServiceDate->addDays($daysUntilService);

            return response()->json([   
                'success' => true,
                'data' => [
                    'current_hm' => $currentHM,
                    'last_service_date' => $lastServiceDate->format('d/m/Y'),
                    'next_service_hm' => $nextServiceHM,
                    'remaining_hm' => $remainingHM,
                    'days_until_service' => $daysUntilService,
                    'predicted_service_date' => $predictedServiceDate->format('d/m/Y'),
                    'predicted_service_date_formatted' => $predictedServiceDate->format('d-m-Y')
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error calculating service prediction: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get predicted service dates for multiple units
     */
    public function getBulkPredictedServiceDates(Request $request)
    {
        try {
            $unitIds = $request->input('unit_ids', []);
            
            if (empty($unitIds)) {
                return response()->json([
                    'success' => false,
                    'message' => 'No unit IDs provided'
                ]);
            }

            $predictions = [];

            foreach ($unitIds as $unitId) {
                $unit = Unit::find($unitId);
                if (!$unit) continue;

                // Get the latest HM entry for the unit
                $latestReport = DailyReport::where('unit_id', $unitId)
                    ->whereNotNull('hm')
                    ->orderBy('date_in', 'desc')
                    ->orderBy('created_at', 'desc')
                    ->first();

                if (!$latestReport) {
                    $predictions[] = [
                        'unit_id' => $unitId,
                        'unit_code' => $unit->unit_code,
                        'unit_type' => $unit->unit_type,
                        'has_data' => false,
                        'message' => 'No HM data available'
                    ];
                    continue;
                }

                $currentHM = $latestReport->hm;
                $lastServiceDate = Carbon::parse($latestReport->date_in);

                // Calculate next service HM (next multiple of 250)
                $nextServiceHM = ceil($currentHM / 250) * 250;
                
                // If current HM is already at a service interval, add 250
                if ($currentHM % 250 == 0) {
                    $nextServiceHM = $currentHM + 250;
                }

                // Calculate remaining HM until next service
                $remainingHM = $nextServiceHM - $currentHM;

                // Calculate days until service (assuming 20 HM per day average usage)
                $averageHMPerDay = 20;
                $daysUntilService = ceil($remainingHM / $averageHMPerDay);

                // Calculate predicted service date
                $predictedServiceDate = $lastServiceDate->copy()->addDays($daysUntilService);

                $predictions[] = [
                    'unit_id' => $unitId,
                    'unit_code' => $unit->unit_code,
                    'unit_type' => $unit->unit_type,
                    'has_data' => true,
                    'current_hm' => $currentHM,
                    'last_service_date' => $lastServiceDate->format('d/m/Y'),
                    'next_service_hm' => $nextServiceHM,
                    'remaining_hm' => $remainingHM,
                    'days_until_service' => $daysUntilService,
                    'predicted_service_date' => $predictedServiceDate->format('d/m/Y'),
                    'predicted_service_date_formatted' => $predictedServiceDate->format('d-m-Y')
                ];
            }

            return response()->json([
                'success' => true,
                'data' => $predictions
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error calculating bulk service predictions: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get service prediction for a unit by unit code
     */
    public function getPredictedServiceDateByCode(Request $request, $unitCode)
    {
        try {
            $unit = Unit::where('unit_code', $unitCode)->first();
            
            if (!$unit) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unit not found'
                ]);
            }

            return $this->getPredictedServiceDate($request, $unit->id);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error finding unit: ' . $e->getMessage()
            ], 500);
        }
    }
}
