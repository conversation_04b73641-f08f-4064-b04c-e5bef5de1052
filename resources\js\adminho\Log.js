const siteId = "{{ $siteId }}";
let timeoutId;
let currentMinWords = 4;

// Initial pagination data
window.logsPaginationData = {
    current_page: 1,
    per_page: 20,
    last_page: 1,
    total: 0
};
const logTableBody = document.querySelector('#logTable tbody');
let currentPage = 1;
const itemsPerPage = 20;
function createPaginationItem(page, text, isActive = false) {
    const li = document.createElement('li');
    li.className = `page-item ${isActive ? 'active' : ''}`;
    const a = document.createElement('a');
    a.className = 'page-link';
    a.href = '#';
    a.textContent = text;
    a.dataset.page = page;

    li.appendChild(a);
    return li;
}

function renderPagination(data) {
    try {
        const paginationContainer = document.getElementById('logs-pagination');
        if (!paginationContainer) {
            console.error('Pagination container not found');
            return;
        }

        paginationContainer.innerHTML = '';

        if (data.last_page > 1) {
            const pagination = document.createElement('ul');
            pagination.className = 'pagination pagination-rounded  justify-content-center';

            // Previous button
            if (data.current_page > 1) {
                pagination.appendChild(createPaginationItem(data.current_page - 1, '\u00ab'));
            }

            // Page numbers
            for (let i = 1; i <= data.last_page; i++) {
                pagination.appendChild(createPaginationItem(i, i, i === data.current_page));
            }

            // Next button
            if (data.current_page < data.last_page) {
                pagination.appendChild(createPaginationItem(data.current_page + 1, '\u00bb'));
            }

            paginationContainer.appendChild(pagination);

            // Add event listeners to pagination links
            paginationContainer.querySelectorAll('.page-link').forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const page = parseInt(this.dataset.page);
                    currentPage = page;
                    searchLogs(page);
                });
            });
        }

        // Update global pagination data
        window.logsPaginationData = data;
    } catch (error) {
        console.error('Error rendering pagination:', error);
    }
}

function searchLogs(page = 1) {
    clearTimeout(timeoutId);
    currentPage = page;

    // Show loading indicator
    logTableBody.innerHTML = '<tr><td colspan="6" class="text-center">.</td></tr>';

    timeoutId = setTimeout(() => {
        const searchInput = document.getElementById('searchInput');
        const searchTerm = searchInput.value.trim();
        const words = searchTerm.split(/\s+/);

        // Cek apakah jumlah kata memenuhi threshold (sementara dinonaktifkan untuk debugging)
        if (words.length < currentMinWords && searchTerm.length > 0) {
            return;
        }

        const dateElement = document.getElementById('dateFilter');
        const siteFilter = document.getElementById('siteFilter').value;

        // Get the date value, default to today if not set
        let date = dateElement.value;
        if (!date) {
            date = new Date().toISOString().split('T')[0]; // Today's date in YYYY-MM-DD format
            dateElement.value = date; // Update the input field
        }

        // Always include the date parameter
        let queryString = `?site_id=${siteFilter}&search=${encodeURIComponent(searchTerm)}&page=${page}&per_page=${itemsPerPage}&date=${date}`;

        fetch(`/log-aktivitas/search${queryString}`, {
                method: 'GET',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                logTableBody.innerHTML = '';

                if (data.data && data.data.length > 0) {
                    data.data.forEach((log, index) => {
                        const row = `
                        <tr>
                            <td>${((data.current_page - 1) * data.per_page) + index + 1}</td>
                            <td>${log.name}</td>
                            <td>${log.action}</td>
                            <td>${log.description || ''}</td>
                            <td>${log.table || ''}</td>
                            <td>${log.formatted_created_at}</td>
                        </tr>
                    `;
                        logTableBody.innerHTML += row;
                    });
                    renderPagination({
                        current_page: data.current_page,
                        per_page: data.per_page,
                        last_page: data.last_page,
                        total: data.total
                    });
                } else {
                    logTableBody.innerHTML = '<tr><td colspan="7">Tidak ada data.</td></tr>';
                }
                if (currentMinWords < 8) {
                    currentMinWords += 2;
                }
            })
            .catch(error => console.error('Error:', error));
    }, 300);
}

document.getElementById('searchInput').addEventListener('input', function() {
    searchLogs(1);
});
document.getElementById('dateFilter').addEventListener('change', function() {
    searchLogs(1);
});
document.getElementById('siteFilter').addEventListener('change', function() {
    searchLogs(1);
});

// The site filter is already set to the logged-in site by default in the blade template
// This is just to ensure it's properly selected in JavaScript as well
const siteFilterElement = document.getElementById('siteFilter');
if (siteFilterElement) {
}

// Make sure the date filter is set to today's date
const dateFilterElement = document.getElementById('dateFilter');
if (dateFilterElement && !dateFilterElement.value) {
    const today = new Date().toISOString().split('T')[0];
    dateFilterElement.value = today;
}

// Initial load with page 1, the logged-in site, and today's date
searchLogs(1);