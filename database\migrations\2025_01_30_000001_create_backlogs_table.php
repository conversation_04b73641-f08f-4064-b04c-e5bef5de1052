<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('backlogs', function (Blueprint $table) {
            $table->id();
            $table->string('unit_code', 50);
            $table->foreign('unit_code')
                ->references('unit_code')
                ->on('units')
                ->onUpdate('cascade')
                ->onDelete('cascade');
            $table->float('hm_found')->nullable();
            $table->string('problem_description');
            $table->string('backlog_job');
            $table->float('plan_hm')->nullable();
            $table->enum('status', ['OPEN', 'CLOSED'])->default('OPEN');
            $table->datetime('plan_pull_date')->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();

            // Add indexes for better performance
            $table->index(['unit_code', 'status']);
            $table->index('status');
            $table->index('plan_pull_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('backlogs');
    }
};
