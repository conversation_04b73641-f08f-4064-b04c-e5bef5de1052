<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add new columns to daily_reports table
        Schema::table('daily_reports', function (Blueprint $table) {
            $table->text('plan_fix')->nullable();
            $table->text('plan_rekomen')->nullable();
            $table->string('lifetime_component')->nullable();
        });

        // Create part_problems table
        Schema::create('part_problems', function (Blueprint $table) {
            $table->id();
            $table->foreignId('daily_report_id')->constrained('daily_reports', 'daily_report_id')->onDelete('cascade');
            $table->string('code_part');
            $table->string('part_name');
            $table->float('quantity');
            $table->string('eum');
            $table->text('remarks')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('part_problems');

        Schema::table('daily_reports', function (Blueprint $table) {
            $table->dropColumn(['plan_fix', 'plan_rekomen', 'lifetime_component']);
        });
    }
};
