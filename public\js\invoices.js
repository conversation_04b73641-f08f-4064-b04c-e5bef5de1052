/**
 * Invoices Table Management
 * Handles loading, filtering, and displaying invoice data
 */

document.addEventListener('DOMContentLoaded', function() {
    // Set default date range (current month)
    const today = new Date();
    const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);

    // Set default date range in the date inputs
    document.getElementById('date-from').valueAsDate = firstDay;
    document.getElementById('date-to').valueAsDate = today;

    // Variables for pagination
    let currentPage = 1;
    let totalPages = 1;
    let itemsPerPage = 10;
    let sortField = 'no_invoice';
    let sortDirection = 'asc';
    let searchTerm = '';

    // Store all loaded invoices for client-side filtering
    let allLoadedInvoices = [];
    let filteredInvoices = [];

    // Load invoices on page load
    loadInvoices();

    // Date input change events - automatically reload data when dates change
    document.getElementById('date-from').addEventListener('change', function() {
        // Reset page to 1
        currentPage = 1;

        // Show loading indicator
        Swal.fire({
            title: 'Memuat Data',
            text: 'Mengambil data sesuai tanggal...',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            },
            timer: 1000,
            timerProgressBar: true
        });

        // Reload data from server with new date range
        loadInvoices();
    });

    document.getElementById('date-to').addEventListener('change', function() {
        // Reset page to 1
        currentPage = 1;

        // Show loading indicator
        Swal.fire({
            title: 'Memuat Data',
            text: 'Mengambil data sesuai tanggal...',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            },
            timer: 1000,
            timerProgressBar: true
        });

        // Reload data from server with new date range
        loadInvoices();
    });

    // Search input event - client-side filtering
    document.getElementById('search-input').addEventListener('input', function() {
        searchTerm = this.value.toLowerCase().trim();
        currentPage = 1;
        filterAndDisplayInvoices();
    });

    // Add event listeners for sortable columns
    document.querySelectorAll('.sortable').forEach(column => {
        column.addEventListener('click', function() {
            const newSortField = this.getAttribute('data-sort');

            // If clicking the same column, toggle sort direction
            if (newSortField === sortField) {
                sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
            } else {
                // If clicking a different column, set as new sort field with ascending direction
                sortField = newSortField;
                sortDirection = 'asc';
            }

            // Update sort icons
            updateSortIcons();

            // Reset to first page
            currentPage = 1;

            // Reload data with new sort
            loadInvoices();
        });
    });

    // Function to update sort icons
    function updateSortIcons() {
        // Remove all sort classes
        document.querySelectorAll('.sortable .sort-icon').forEach(icon => {
            icon.className = 'sort-icon mdi mdi-sort';
        });

        // Add appropriate sort class to active column
        const activeColumn = document.querySelector(`.sortable[data-sort="${sortField}"] .sort-icon`);
        if (activeColumn) {
            activeColumn.className = `sort-icon mdi mdi-sort-${sortDirection === 'asc' ? 'ascending' : 'descending'}`;
        }
    }

    // Function to load invoices
    function loadInvoices() {
        // Get date range values
        let dateFrom = document.getElementById('date-from').value;
        let dateTo = document.getElementById('date-to').value;

        // If dates are not set, use default (first day of current month to today)
        if (!dateFrom || !dateTo) {
            // Use global date utilities if available, otherwise fallback
            if (window.DateUtils) {
                dateFrom = window.DateUtils.getFirstDayOfMonth();
                dateTo = window.DateUtils.getTodayFormatted();
            } else {
                const today = new Date();
                const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
                dateFrom = firstDay.toISOString().split('T')[0];
                dateTo = today.toISOString().split('T')[0];
            }

            // Update the date inputs
            document.getElementById('date-from').value = dateFrom;
            document.getElementById('date-to').value = dateTo;
        }

        // Show loading state
        const tableBody = document.getElementById('invoices-table-body');
        tableBody.innerHTML = `
            <tr>
                <td colspan="11" class="text-center">
                    <div class="d-flex justify-content-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="sr-only">.</span>
                        </div>
                    </div>
                </td>
            </tr>
        `;

        // Build query parameters
        const params = new URLSearchParams({
            date_from: dateFrom,
            date_to: dateTo,
            page: currentPage,
            per_page: itemsPerPage,
            sort_field: sortField,
            sort_direction: sortDirection,
            search: searchTerm
        });

        // Fetch data from API
        fetch(`/sales/all-invoices?${params.toString()}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                // Store all loaded invoices for client-side filtering
                allLoadedInvoices = data.data || [];
                filteredInvoices = [...allLoadedInvoices];

                // Display invoices
                displayInvoices(allLoadedInvoices);

                // Update pagination
                updatePagination(data);

                // Update showing text
                updateShowingText(data);

                // Update sort icons
                updateSortIcons();
            })
            .catch(error => {
                console.error('Error fetching invoices:', error);
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="11" class="text-center">
                            <div class="alert alert-danger">
                                Error loading data. Please try again.
                                <button id="error-details-btn" class="btn btn-sm btn-outline-danger mt-2">Show Details</button>
                            </div>
                        </td>
                    </tr>
                `;

                // Add event listener to error details button
                document.getElementById('error-details-btn')?.addEventListener('click', function() {
                    Swal.fire({
                        title: 'Detail Error',
                        html: `
                            <div class="text-left">
                                <p><strong>Error Message:</strong> ${error.message}</p>
                                <p><strong>Date Range:</strong> ${dateFrom} to ${dateTo}</p>
                            </div>
                        `,
                        icon: 'error',
                        confirmButtonText: 'Tutup'
                    });
                });
            });
    }

    // Function to display invoices
    function displayInvoices(invoices) {
        const tableBody = document.getElementById('invoices-table-body');

        // Clear table body
        tableBody.innerHTML = '';

        // Check if there are no invoices
        if (!invoices || invoices.length === 0) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="11" class="text-center">
                        <div class="alert alert-info">
                            Tidak ada data invoice untuk periode ini.
                        </div>
                    </td>
                </tr>
            `;
            return;
        }

        // Loop through invoices and add rows
        invoices.forEach((invoice, index) => {
            // Use formatted dates from the server if available, otherwise format them here
            const formattedInvoiceDate = invoice.formatted_invoice_date || (invoice.tanggal_invoice ? new Date(invoice.tanggal_invoice).toLocaleDateString('id-ID') : '-');
            const formattedDueDate = invoice.formatted_due_date || (invoice.due_date ? new Date(invoice.due_date).toLocaleDateString('id-ID') : '-');

            // Use due status from the server if available, otherwise calculate it here
            let dueStatus = invoice.due_status || 'Normal';
            let dueStatusClass = invoice.due_status_class || 'success';

            // Create table row
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${(currentPage - 1) * itemsPerPage + index + 1}</td>
                <td>
                    <a href="#" class="invoice-detail-link" data-id="${invoice.id}">${invoice.no_invoice || '-'}</a>
                </td>
                <td>${invoice.site_name || '-'}</td>
                <td>${invoice.unit_list || '-'}</td>
                <td>${invoice.po_number || '-'}</td>
                <td>${formattedInvoiceDate}</td>
                <td>${formattedDueDate}</td>
                <td>
                    <span class="badge bg-${invoice.payment_status === 'Lunas' ? 'success' : 'warning'}">
                        ${invoice.payment_status || 'Belum Lunas'}
                    </span>
                </td>
                <td>
                    <span class="badge bg-${dueStatusClass}">
                        ${dueStatus}
                    </span>
                </td>
                <td>
                    ${invoice.document_path ?
                        `<button class="btn btn-sm btn-primary btn-view-document" data-path="${invoice.document_path}">
                            <i class="mdi mdi-file-document"></i> Lihat
                        </button>` :
                        '<span class="badge bg-secondary">Tidak ada</span>'
                    }
                </td>
                <td>
                    <div class="btn-group">
                        <button class="btn btn-sm btn-success btn-print-invoice" data-id="${invoice.id}" title="Cetak Invoice">
                            <i class="mdi mdi-printer"></i>
                        </button>
                        ${invoice.payment_status !== 'Lunas' ?
                            `<button class="btn btn-sm btn-primary btn-edit-invoice" data-id="${invoice.id}" title="Edit Invoice">
                                <i class="mdi mdi-pencil"></i>
                            </button>` : ''
                        }
                        ${invoice.payment_status !== 'Lunas' ?
                            `<button class="btn btn-sm btn-danger btn-delete-invoice" data-id="${invoice.id}" title="Hapus Invoice">
                                <i class="mdi mdi-delete"></i>
                            </button>` : ''
                        }
                    </div>
                </td>
            `;

            tableBody.appendChild(row);
        });

        // Add event listeners to action buttons
        addActionButtonListeners();
    }

    // Function to add event listeners to action buttons
    function addActionButtonListeners() {
        // View document buttons
        document.querySelectorAll('.btn-view-document').forEach(button => {
            button.addEventListener('click', function() {
                const documentPath = this.getAttribute('data-path');
                viewDocument(documentPath);
            });
        });

        // Print invoice buttons
        document.querySelectorAll('.btn-print-invoice').forEach(button => {
            button.addEventListener('click', function() {
                const invoiceId = this.getAttribute('data-id');
                printInvoice(invoiceId);
            });
        });

        // Edit invoice buttons
        document.querySelectorAll('.btn-edit-invoice').forEach(button => {
            button.addEventListener('click', function() {
                const invoiceId = this.getAttribute('data-id');
                editInvoice(invoiceId);
            });
        });

        // Delete invoice buttons
        document.querySelectorAll('.btn-delete-invoice').forEach(button => {
            button.addEventListener('click', function() {
                const invoiceId = this.getAttribute('data-id');
                deleteInvoice(invoiceId);
            });
        });

        // Invoice detail links
        document.querySelectorAll('.invoice-detail-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const invoiceId = this.getAttribute('data-id');
                viewInvoiceDetails(invoiceId);
            });
        });
    }

    // Function to update pagination
    function updatePagination(data) {
        const pagination = document.getElementById('pagination');
        pagination.innerHTML = '';

        // Update total pages
        totalPages = data.last_page || 1;

        // Don't show pagination if there's only one page
        if (totalPages <= 1) {
            return;
        }

        // Previous button
        const prevLi = document.createElement('li');
        prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
        prevLi.innerHTML = `<a class="page-link" href="#" data-page="${currentPage - 1}">«</a>`;
        pagination.appendChild(prevLi);

        // Page numbers
        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, startPage + 4);

        for (let i = startPage; i <= endPage; i++) {
            const pageLi = document.createElement('li');
            pageLi.className = `page-item ${i === currentPage ? 'active' : ''}`;
            pageLi.innerHTML = `<a class="page-link" href="#" data-page="${i}">${i}</a>`;
            pagination.appendChild(pageLi);
        }

        // Next button
        const nextLi = document.createElement('li');
        nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
        nextLi.innerHTML = `<a class="page-link" href="#" data-page="${currentPage + 1}">»</a>`;
        pagination.appendChild(nextLi);

        // Add event listeners to pagination links
        document.querySelectorAll('.page-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const page = parseInt(this.getAttribute('data-page'));

                // Only proceed if it's a valid page
                if (page >= 1 && page <= totalPages && page !== currentPage) {
                    currentPage = page;
                    loadInvoices();
                }
            });
        });
    }

    // Function to update showing text
    function updateShowingText(data) {
        const showingText = document.getElementById('showing-text');
        const total = data.total || 0;
        const start = total === 0 ? 0 : (currentPage - 1) * itemsPerPage + 1;
        const end = Math.min(start + itemsPerPage - 1, total);

        showingText.textContent = `Menampilkan ${start} sampai ${end} dari ${total} invoice`;
    }

    // Function to filter and display invoices (client-side filtering)
    function filterAndDisplayInvoices() {
        if (!searchTerm) {
            // If no search term, show all loaded invoices
            filteredInvoices = [...allLoadedInvoices];
        } else {
            // Filter invoices based on search term
            filteredInvoices = allLoadedInvoices.filter(invoice => {
                return (
                    (invoice.no_invoice && invoice.no_invoice.toLowerCase().includes(searchTerm)) ||
                    (invoice.customer && invoice.customer.toLowerCase().includes(searchTerm)) ||
                    (invoice.site_name && invoice.site_name.toLowerCase().includes(searchTerm)) ||
                    (invoice.unit_list && invoice.unit_list.toLowerCase().includes(searchTerm)) ||
                    (invoice.po_number && invoice.po_number.toLowerCase().includes(searchTerm))
                );
            });
        }

        // Display filtered invoices
        displayInvoices(filteredInvoices);

        // Update showing text for filtered results
        const showingText = document.getElementById('showing-text');
        showingText.textContent = `Menampilkan ${filteredInvoices.length} dari ${allLoadedInvoices.length} invoice (filtered)`;
    }

    // Function to view document
    function viewDocument(documentPath) {
        // Implement document viewer logic
        Swal.fire({
            title: 'Dokumen Invoice',
            html: `<iframe src="/assets/invoice_documents/${documentPath}" width="100%" height="500px"></iframe>`,
            width: '80%',
            confirmButtonText: 'Tutup'
        });
    }

    // Function to print invoice
    function printInvoice(invoiceId) {
        // Open invoice preview in new window
        window.open(`/sales/invoices/${invoiceId}/preview`, '_blank');
    }

    // Function to edit invoice
    function editInvoice(invoiceId) {
        // Redirect to edit page or show edit modal
        window.location.href = `/sales/invoices/${invoiceId}/edit`;
    }

    // Function to delete invoice
    function deleteInvoice(invoiceId) {
        // Show confirmation dialog
        Swal.fire({
            title: 'Hapus Invoice?',
            text: 'Anda yakin ingin menghapus invoice ini? Tindakan ini tidak dapat dibatalkan.',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Ya, Hapus',
            cancelButtonText: 'Batal',
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6'
        }).then((result) => {
            if (result.isConfirmed) {
                // Send delete request
                fetch(`/sales/invoices/${invoiceId}`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        Swal.fire({
                            title: 'Berhasil!',
                            text: 'Invoice berhasil dihapus.',
                            icon: 'success',
                            timer: 2000,
                            showConfirmButton: false
                        });

                        // Reload invoices
                        loadInvoices();
                    } else {
                        throw new Error(data.message || 'Failed to delete invoice');
                    }
                })
                .catch(error => {
                    Swal.fire({
                        title: 'Error!',
                        text: error.message || 'Terjadi kesalahan saat menghapus invoice.',
                        icon: 'error'
                    });
                });
            }
        });
    }

    // Function to view invoice details
    function viewInvoiceDetails(invoiceId) {
        // Fetch invoice details
        fetch(`/sales/invoices/${invoiceId}`)
            .then(response => response.json())
            .then(data => {
                // Show invoice details in modal
                Swal.fire({
                    title: `Invoice: ${data.no_invoice}`,
                    html: `
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <tr>
                                    <th>Customer</th>
                                    <td>${data.customer || '-'}</td>
                                </tr>
                                <tr>
                                    <th>Tanggal Invoice</th>
                                    <td>${data.tanggal_invoice ? new Date(data.tanggal_invoice).toLocaleDateString('id-ID') : '-'}</td>
                                </tr>
                                <tr>
                                    <th>Tanggal Jatuh Tempo</th>
                                    <td>${data.due_date ? new Date(data.due_date).toLocaleDateString('id-ID') : '-'}</td>
                                </tr>
                                <tr>
                                    <th>Status Pembayaran</th>
                                    <td>${data.payment_status || '-'}</td>
                                </tr>
                                <tr>
                                    <th>Nomor PO</th>
                                    <td>${data.po_number || '-'}</td>
                                </tr>
                                <tr>
                                    <th>Catatan</th>
                                    <td>${data.notes || '-'}</td>
                                </tr>
                            </table>
                        </div>
                    `,
                    width: '600px',
                    confirmButtonText: 'Tutup'
                });
            })
            .catch(error => {
                Swal.fire({
                    title: 'Error!',
                    text: 'Gagal memuat detail invoice.',
                    icon: 'error'
                });
            });
    }
});
