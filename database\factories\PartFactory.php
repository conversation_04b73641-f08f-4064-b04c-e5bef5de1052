<?php

namespace Database\Factories;

use App\Models\Part;
use Illuminate\Database\Eloquent\Factories\Factory;

class PartFactory extends Factory
{
    protected $model = Part::class;

    public function definition()
    {
        return [
            'part_code' => $this->faker->unique()->regexify('[A-Z]{3}[0-9]{3}'),
            'part_name' => $this->faker->words(3, true),
            'bin_location' => $this->faker->regexify('[A-Z][0-9]{2}'),
            'part_type' => $this->faker->randomElement(['AC', 'TYRE', 'FABRIKASI']),
            'price' => $this->faker->numberBetween(50000, 500000),
            'purchase_price' => $this->faker->numberBetween(40000, 400000),
            'eum' => 'EA',
        ];
    }
}
