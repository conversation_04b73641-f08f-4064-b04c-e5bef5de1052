import Chart from "chart.js/auto";
import ChartDataLabels from "chartjs-plugin-datalabels";

Chart.register(ChartDataLabels); // Registrasi global, !! Penting !! jangan panggil difile lain

document.addEventListener("DOMContentLoaded", function () {
    window.inventoryData.forEach((data) => {
        const pieChartId = document
            .getElementById(`pieChart${slugify(data.site_name)}`)
            .getContext("2d");

        const pieChart = new Chart(pieChartId, {
            type: "doughnut",
            data: {
                labels: ["Not Ready","Ready"], // urutkan sesuai warna dan data
                datasets: [
                    {
                        label: "Status Inventory",
                        data: [
                            data.not_ready_percentage, // cocok dengan "Not Ready"
                            data.ready_percentage, // cocok dengan "Ready"
                        ],
                        backgroundColor: [
                            "rgba(235, 49, 36, 0.8)", // Red → "Not Ready"
                            "rgba(40, 167, 69, 0.8)", // <PERSON> → "Ready"
                        ],
                        borderWidth: 1,
                    },
                ],
            },
            options: {
                rotation: 180,
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: "bottom",
                    },
                    title: {
                        display: true,
                        text: "Persentase Status Inventory",
                    },
                    tooltip: {
                        callbacks: {
                            label: function (context) {
                                let label = context.label || "";
                                if (label) {
                                    label += ": ";
                                }
                                if (context.parsed !== null) {
                                    label += context.parsed.toFixed(2) + "%";
                                }
                                return label;
                            },
                        },
                    },
                    datalabels: {
                        formatter: (value, context) => {
                            return value.toFixed(2) + "%";
                        },
                        color: "#fff",
                        font: {
                            weight: "bold",
                            size: 12,
                        },
                    },
                },
            },
            plugins: [ChartDataLabels],
        });
    });

    function slugify(str) {
        return String(str)
            .normalize("NFKD")
            .replace(/[\u0300-\u036f]/g, "")
            .trim()
            .toLowerCase()
            .replace(/[^a-z0-9 -]/g, "")
            .replace(/\s+/g, "-")
            .replace(/-+/g, "-");
    }
});
