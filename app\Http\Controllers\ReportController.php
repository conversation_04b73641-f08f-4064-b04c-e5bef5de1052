<?php

namespace App\Http\Controllers;

use App\Models\SiteInStock;
use App\Models\SiteOutStock;
use App\Models\WarehouseOutStock;
use Illuminate\Http\Request;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class ReportController extends Controller
{
    public function index()
    {
        return view('reports.index');
    }

    public function exportInPdf(Request $request)
    {
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');
        $sortBy = $request->input('sort_by', 'date');
        $siteId = $request->input('site_id');  // Ambil site_id dari request

        $data = $this->getInStockData($startDate, $endDate, $sortBy, $siteId); // Teruskan siteId
        $title = 'Laporan Part In Stock';
        $type = 'IN';

        // Get site name if site_id is provided
        $siteName = null;
        if ($siteId) {
            $site = \App\Models\Site::find($siteId);
            $siteName = $site ? $site->site_name : null;
        }

        $pdf = Pdf::loadView('reports.pdf_template', compact('data', 'startDate', 'endDate', 'sortBy', 'title', 'type', 'siteName'));

        $filename = 'laporan_part_in';
        if ($siteName) {
            $filename .= '_' . str_replace(' ', '_', strtolower($siteName));
        }
        $filename .= '_' . now()->format('YmdHis') . '.pdf';

        return $pdf->download($filename);
    }

    public function exportOutPdf(Request $request)
    {
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');
        $sortBy = $request->input('sort_by', 'date');
        $siteId = $request->input('site_id'); // Ambil site_id dari request

        $data = $this->getOutStockData($startDate, $endDate, $sortBy, $siteId); // Teruskan siteId
        $title = 'Laporan Part Out Stock';
        $type = 'OUT';

        // Get site name if site_id is provided
        $siteName = null;
        if ($siteId) {
            $site = \App\Models\Site::find($siteId);
            $siteName = $site ? $site->site_name : null;
        }

        $pdf = Pdf::loadView('reports.pdf_template', compact('data', 'startDate', 'endDate', 'sortBy', 'title', 'type', 'siteName'));

        $filename = 'laporan_part_out';
        if ($siteName) {
            $filename .= '_' . str_replace(' ', '_', strtolower($siteName));
        }
        $filename .= '_' . now()->format('YmdHis') . '.pdf';

        return $pdf->download($filename);
    }

    private function getInStockData($startDate = null, $endDate = null, $sortBy = 'date', $siteId = null)
    {
        $query = SiteInStock::join('part_inventories', 'site_in_stocks.part_inventory_id', '=', 'part_inventories.part_inventory_id')
            ->join('parts', 'part_inventories.part_code', '=', 'parts.part_code')
            ->select(
                'site_in_stocks.date_in',
                'parts.part_code',
                'parts.part_name',
                'site_in_stocks.quantity',
                'part_inventories.site_id'  // Select site_id
            );

        if ($startDate && $endDate) {
            $query->whereBetween('site_in_stocks.date_in', [$startDate, $endDate]);
        }

        if ($siteId) {
            $query->where('part_inventories.site_id', $siteId); // Filter berdasarkan site_id
        }

        if ($sortBy == 'date') {
            $query->orderBy('site_in_stocks.date_in', 'asc');
        } elseif ($sortBy == 'part_name') {
            $query->orderBy('parts.part_name', 'asc')->orderBy('site_in_stocks.date_in', 'asc');
        }

        return $query->get();
    }

    private function getOutStockData($startDate = null, $endDate = null, $sortBy = 'date', $siteId = null)
    {
        if ($siteId == 'WHO') {
            $query = WarehouseOutStock::join('part_inventories', 'warehouse_out_stocks.part_inventory_id', '=', 'part_inventories.part_inventory_id')
                ->join('parts', 'part_inventories.part_code', '=', 'parts.part_code')
                ->select(
                    'warehouse_out_stocks.date_out',
                    'parts.part_code',
                    'parts.part_name',
                    'warehouse_out_stocks.destination_id',
                    'warehouse_out_stocks.quantity',
                    'part_inventories.site_id'  // Select site_id
                );
            if ($startDate && $endDate) {
                $query->whereBetween('warehouse_out_stocks.date_out', [$startDate, $endDate]);
            }
            if ($sortBy == 'date') {
                $query->orderBy('warehouse_out_stocks.date_out', 'asc');
            } elseif ($sortBy == 'part_name') {
                $query->orderBy('parts.part_name', 'asc')->orderBy('warehouse_out_stocks.date_out', 'asc');
            }
        } else {
            $query = SiteOutStock::join('part_inventories', 'site_out_stocks.part_inventory_id', '=', 'part_inventories.part_inventory_id')
                ->join('parts', 'part_inventories.part_code', '=', 'parts.part_code')
                ->select(
                    'site_out_stocks.date_out',
                    'parts.part_code',
                    'parts.part_name',
                    'site_out_stocks.quantity',
                    'part_inventories.site_id'  // Select site_id
                );
            if ($startDate && $endDate) {
                $query->whereBetween('site_out_stocks.date_out', [$startDate, $endDate]);
            }
            if ($sortBy == 'date') {
                $query->orderBy('site_out_stocks.date_out', 'asc');
            } elseif ($sortBy == 'part_name') {
                $query->orderBy('parts.part_name', 'asc')->orderBy('site_out_stocks.date_out', 'asc');
            }
        }

        if ($siteId) {
            $query->where('part_inventories.site_id', $siteId); // Filter berdasarkan site_id
        }

        return $query->get();
    }

    public function exportInExcel(Request $request)
    {
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');
        $sortBy = $request->input('sort_by', 'date');
        $siteId = $request->input('site_id');

        $data = $this->getInStockData($startDate, $endDate, $sortBy, $siteId);

        // Get site name if site_id is provided
        $siteName = 'Semua Site';
        if ($siteId) {
            $site = \App\Models\Site::find($siteId);
            $siteName = $site ? $site->site_name : 'Semua Site';
        }

        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set title and info
        $sheet->setCellValue('A1', 'PT. Putera Wibowo Borneo');
        $sheet->setCellValue('A2', 'Laporan Part In Stock');
        $sheet->setCellValue('A3', 'Site: ' . $siteName);
        $sheet->setCellValue('A4', 'Periode: ' .
            ($startDate ? date('d F Y', strtotime($startDate)) : 'Awal') . ' - ' .
            ($endDate ? date('d F Y', strtotime($endDate)) : 'Akhir'));

        // Set headers
        $sheet->setCellValue('A6', 'No');
        $sheet->setCellValue('B6', 'Tanggal');
        $sheet->setCellValue('C6', 'Part Number');
        $sheet->setCellValue('D6', 'Nama Part');
        $sheet->setCellValue('E6', 'Qty');

        // Populate data
        $row = 7;
        foreach ($data as $index => $item) {
            $sheet->setCellValue('A' . $row, $index + 1);
            $sheet->setCellValue('B' . $row, Carbon::parse($item->date_in)->format('d F Y'));
            $sheet->setCellValue('C' . $row, $item->part_code);
            $sheet->setCellValue('D' . $row, $item->part_name);
            $sheet->setCellValue('E' . $row, $item->quantity);
            $row++;
        }

        // Auto-size columns
        foreach (range('A', 'E') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }

        // Style the header
        $sheet->getStyle('A1:A4')->getFont()->setBold(true);
        $sheet->getStyle('A6:E6')->getFont()->setBold(true);
        $sheet->getStyle('A6:E6')->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setRGB('1a3c5a');
        $sheet->getStyle('A6:E6')->getFont()->getColor()->setRGB('FFFFFF');

        $writer = new Xlsx($spreadsheet);
        $filename = 'laporan_part_in';
        if ($siteName && $siteName !== 'Semua Site') {
            $filename .= '_' . str_replace(' ', '_', strtolower($siteName));
        }
        $filename .= '_' . now()->format('YmdHis') . '.xlsx';

        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $writer->save('php://output');
        exit;
    }

    public function exportOutExcel(Request $request)
    {
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');
        $sortBy = $request->input('sort_by', 'date');
        $siteId = $request->input('site_id');

        $data = $this->getOutStockData($startDate, $endDate, $sortBy, $siteId);

        // Get site name if site_id is provided
        $siteName = 'Semua Site';
        if ($siteId) {
            $site = \App\Models\Site::find($siteId);
            $siteName = $site ? $site->site_name : 'Semua Site';
        }

        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set title and info
        $sheet->setCellValue('A1', 'PT. Putera Wibowo Borneo');
        $sheet->setCellValue('A2', 'Laporan Part Out Stock');
        $sheet->setCellValue('A3', 'Site: ' . $siteName);
        $sheet->setCellValue('A4', 'Periode: ' .
            ($startDate ? date('d F Y', strtotime($startDate)) : 'Awal') . ' - ' .
            ($endDate ? date('d F Y', strtotime($endDate)) : 'Akhir'));

        // Set headers
        $sheet->setCellValue('A6', 'No');
        $sheet->setCellValue('B6', 'Tanggal');
        $sheet->setCellValue('C6', 'Part Number');
        $sheet->setCellValue('D6', 'Nama Part');
        $sheet->setCellValue('E6', 'Qty');
        $sheet->setCellValue('F6', 'Site Tujuan');


        // Populate data
        $row = 7;
        foreach ($data as $index => $item) {
            $sheet->setCellValue('A' . $row, $index + 1);
            $sheet->setCellValue('B' . $row, Carbon::parse($item->date_out)->format('d F Y'));
            $sheet->setCellValue('C' . $row, $item->part_code);
            $sheet->setCellValue('D' . $row, $item->part_name);
            $sheet->setCellValue('E' . $row, $item->quantity);
            $sheet->setCellValue('F' . $row, $item->destination_id == 1 ? '-' : $item->destination_id);

            $row++;
        }

        // Auto-size columns
        foreach (range('A', 'E') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }

        // Style the header
        $sheet->getStyle('A1:A4')->getFont()->setBold(true);
        $sheet->getStyle('A6:E6')->getFont()->setBold(true);
        $sheet->getStyle('A6:E6')->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setRGB('1a3c5a');
        $sheet->getStyle('A6:E6')->getFont()->getColor()->setRGB('FFFFFF');

        $writer = new Xlsx($spreadsheet);
        $filename = 'laporan_part_out';
        if ($siteName && $siteName !== 'Semua Site') {
            $filename .= '_' . str_replace(' ', '_', strtolower($siteName));
        }
        $filename .= '_' . now()->format('YmdHis') . '.xlsx';

        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $writer->save('php://output');
        exit;
    }
}
