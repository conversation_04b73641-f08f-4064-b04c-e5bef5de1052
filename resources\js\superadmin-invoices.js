/**
 * Superadmin Invoices JavaScript
 *
 * This file contains the JavaScript code for the superadmin invoices page.
 */

document.addEventListener("DOMContentLoaded", function () {
    // Initialize variables for invoices
    const lengthPageBtn = document.getElementById("length-page");
    let currentPage = 1;
    let totalPages = 1;
    let perPage = lengthPageBtn.value;
    let totalInvoices = 0;
    let currentSearch = "";
    let currentStatus = "";
    let currentSiteId = "";
    let currentStartDate = "";
    let currentEndDate = "";

    // Initialize variables for Ready PO
    let readyPoCurrentPage = 1;
    let readyPoTotalPages = 1;
    let readyPoPerPage = 10;
    let readyPoTotalTransactions = 0;
    let readyPoCurrentSearch = "";
    let readyPoCurrentSiteId = "";

    // Get DOM elements for invoices
    const skeletonLoader = document.getElementById("skeleton-loader");
    const contentWrapper = document.querySelector(".content-wrapper");
    const searchInput = document.getElementById("search-input");
    const statusFilter = document.getElementById("status-filter");
    const siteFilter = document.getElementById("site-filter");
    const invoiceTableBody = document.getElementById("invoice-table-body");
    const paginationStart = document.getElementById("pagination-start");
    const paginationEnd = document.getElementById("pagination-end");
    const paginationTotal = document.getElementById("pagination-total");
    const prevPageBtn = document.getElementById("prev-page");
    const nextPageBtn = document.getElementById("next-page");
    const monthPicker = document.getElementById("monthPicker");
    const prevMonthBtn = document.getElementById("prevMonthBtn");
    const nextMonthBtn = document.getElementById("nextMonthBtn");

    // Get DOM elements for Ready PO
    const readyPoSearchInput = document.getElementById("ready-po-search-input");
    const readyPoSiteFilter = document.getElementById("ready-po-site-filter");
    const readyPoTableBody = document.getElementById("ready-po-table-body");
    const readyPoPaginationStart = document.getElementById(
        "ready-po-pagination-start"
    );
    const readyPoPaginationEnd = document.getElementById(
        "ready-po-pagination-end"
    );
    const readyPoPaginationTotal = document.getElementById(
        "ready-po-pagination-total"
    );
    const readyPoPrevPageBtn = document.getElementById("ready-po-prev-page");
    const readyPolengthPageBtn = document.getElementById(
        "ready-po-length-page"
    );
    const readyPoNextPageBtn = document.getElementById("ready-po-next-page");

    // Initialize month picker
    const currentDate = new Date();
    const currentMonth =
        currentDate.getFullYear() +
        "-" +
        String(currentDate.getMonth() + 1).padStart(2, "0");
    monthPicker.value = currentMonth;

    // Set up event listeners for invoices
    searchInput.addEventListener("input", debounce(handleSearch, 500));
    statusFilter.addEventListener("change", handleFilterChange);
    siteFilter.addEventListener("change", handleFilterChange);
    prevPageBtn.addEventListener("click", handlePrevPage);
    lengthPageBtn.addEventListener("change", handlelenghtPage);
    nextPageBtn.addEventListener("click", handleNextPage);
    monthPicker.addEventListener("change", handleMonthChange);
    prevMonthBtn.addEventListener("click", handlePrevMonth);
    nextMonthBtn.addEventListener("click", handleNextMonth);

    // Set up event listeners for Ready PO
    readyPoSearchInput.addEventListener(
        "input",
        debounce(handleReadyPoSearch, 500)
    );
    readyPoSiteFilter.addEventListener("change", handleReadyPoFilterChange);
    readyPoPrevPageBtn.addEventListener("click", handleReadyPoPrevPage);
    readyPoNextPageBtn.addEventListener("click", handleReadyPoNextPage);

    // Load initial data
    loadInvoicesData();
    loadReadyPoData();

    /**
     * Load invoices data from the server
     */
    function loadInvoicesData() {
        // Show skeleton loader
        skeletonLoader.style.display = "block";
        contentWrapper.style.display = "none";

        // Get month value for date filtering
        const monthValue = monthPicker.value;
        if (monthValue) {
            const [year, month] = monthValue.split("-");
            currentStartDate = `${year}-${month}-01`;
            // Calculate last day of month
            const lastDay = new Date(year, month, 0).getDate();
            currentEndDate = `${year}-${month}-${lastDay}`;
        }

        // Build query parameters
        const params = new URLSearchParams({
            page: currentPage,
            per_page: lengthPageBtn.value ?? perPage,
            search: currentSearch,
            status: currentStatus,
            site_id: currentSiteId,
        });

        if (currentStartDate && currentEndDate) {
            params.append("start_date", currentStartDate);
            params.append("end_date", currentEndDate);
        }

        // Fetch data from the server
        fetch(`/superadmin/invoices-data?${params.toString()}`)
            .then((response) => {
                if (!response.ok) {
                    throw new Error("Network response was not ok");
                }
                return response.json();
            })
            .then((data) => {
                // Update site filter options if needed
                if (siteFilter.children.length <= 1) {
                    populateSiteFilter(data.sites);
                }

                // Update invoices table
                renderInvoicesTable(data.invoices);

                // Update pagination
                updatePagination(data.pagination);

                // Hide skeleton loader and show content
                skeletonLoader.style.display = "none";
                contentWrapper.style.display = "block";
            })
            .catch((error) => {
                skeletonLoader.style.display = "none";
                contentWrapper.style.display = "block";
                invoiceTableBody.innerHTML = `
                    <tr>
                        <td colspan="6" class="text-center">Error loading data. Please try again.</td>
                    </tr>
                `;
            });
    }

    /**
     * Populate site filter dropdown with options
     */
    function populateSiteFilter(sites) {
        // Clear existing options except the first one (All Sites)
        while (siteFilter.children.length > 1) {
            siteFilter.removeChild(siteFilter.lastChild);
        }

        // Add non-site option
        const nonSiteOption = document.createElement("option");
        nonSiteOption.value = "non-site";
        nonSiteOption.textContent = "Non-Site";
        siteFilter.appendChild(nonSiteOption);

        // Add site options (exclude warehouse)
        sites.forEach((site) => {
            if (site.site_id !== "WHO") {
                const option = document.createElement("option");
                option.value = site.site_id;
                option.textContent = site.site_name;
                siteFilter.appendChild(option);
            }
        });
    }

    /**
     * Render invoices table with data
     */
    function renderInvoicesTable(invoices) {
        // Clear existing rows
        invoiceTableBody.innerHTML = "";

        let i = (currentPage - 1) * perPage + 1;

        if (invoices.length === 0) {
            // Show no data message
            invoiceTableBody.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center">Tidak ada data invoice yang ditemukan.</td>
                </tr>
            `;
            return;
        }

        // Add rows for each invoice
        invoices.forEach((invoice) => {
            const row = document.createElement("tr");

            // Format currency values - only use total amount (no PPN)
            const formattedTotalAmount = formatCurrency(invoice.total_amount);

            // Format date
            const formattedDate = invoice.tanggal_invoice
                ? formatDate(invoice.tanggal_invoice)
                : "-";

            // Create status badge with blue theme colors
            let statusBadgeClass = "bg-secondary";
            if (
                invoice.payment_status === "Lunas" ||
                invoice.invoice_status === "Lunas"
            ) {
                statusBadgeClass = "bg-success"; // Using success color from theme
            } else if (
                invoice.payment_status === "Jatuh Tempo" ||
                invoice.invoice_status === "Jatuh Tempo"
            ) {
                statusBadgeClass = "bg-danger"; // Using danger color from theme
            } else if (
                invoice.payment_status === "Belum Lunas" ||
                invoice.invoice_status === "Belum Lunas"
            ) {
                statusBadgeClass = "bg-warning text-dark"; // Using warning color from theme
            }

            // Check for document path (either signed_document_path or document_path)
            const documentPath =
                invoice.signed_document_path || invoice.document_path;

            // Set row content
            row.innerHTML = `
                <td>${i++}</td>
                <td>
                    <a href="#" class="invoice-link" data-id="${invoice.id}" ${
                documentPath ? `data-document="${documentPath}"` : ""
            }>${invoice.no_invoice || "-"}</a>
                </td>
                <td>${invoice.site ? invoice.site.site_name : "-"}</td>
                <td>${invoice.customer || "-"}</td>
                <td>${formattedDate}</td>
                <td>${formattedTotalAmount}</td>
                <td><span class="badge ${statusBadgeClass}">${
                invoice.invoice_status
            }</span></td>
            `;

            invoiceTableBody.appendChild(row);
        });

        // Add event listeners to invoice links - ALWAYS show document viewer, not details
        document.querySelectorAll(".invoice-link").forEach((link) => {
            link.addEventListener("click", function (e) {
                e.preventDefault();
                const invoiceId = this.getAttribute("data-id");
                const documentPath = this.getAttribute("data-document");

                // If document path is available, show document directly
                if (documentPath) {
                    showDocumentViewer(documentPath);
                } else {
                    // If no document is available, fetch invoice details to get document path
                    fetch(`/superadmin/invoices/${invoiceId}`)
                        .then((response) => response.json())
                        .then((data) => {
                            // Check for either signed_document_path or document_path
                            const documentPath = data.success
                                ? data.invoice.signed_document_path ||
                                  data.invoice.document_path
                                : null;

                            if (documentPath) {
                                showDocumentViewer(documentPath);
                            } else {
                                // If still no document, show a message
                                alert(
                                    "Tidak ada lampiran invoice yang tersedia."
                                );
                            }
                        })
                        .catch((error) => {
                            alert("Gagal mengambil data lampiran invoice.");
                        });
                }
            });
        });
    }

    /**
     * Show invoice details in a modal - DEPRECATED, use showDocumentViewer instead
     * This function is kept for backward compatibility but should not be used anymore
     */
    function showInvoiceDetails(invoiceId) {
        // Instead of showing details, fetch the document path and show the document
        fetch(`/superadmin/invoices/${invoiceId}`)
            .then((response) => response.json())
            .then((data) => {
                if (data.success) {
                    // Check for either signed_document_path or document_path
                    const documentPath =
                        data.invoice.signed_document_path ||
                        data.invoice.document_path;

                    if (documentPath) {
                        showDocumentViewer(documentPath);
                    } else {
                        alert("Tidak ada lampiran invoice yang tersedia.");
                    }
                } else {
                    alert("Gagal mengambil data lampiran invoice.");
                }
            })
            .catch((error) => {
                alert("Gagal mengambil data lampiran invoice.");
            });
    }

    /**
     * Render invoice details in the modal
     */
    function renderInvoiceDetails(invoice) {
        const detailContent = document.getElementById("invoice-detail-content");

        // Format currency and date values
        const formattedSubtotal = formatCurrency(invoice.subtotal);
        const formattedTotalAmount = formatCurrency(invoice.total_amount);
        const formattedDate = invoice.tanggal_invoice
            ? formatDate(invoice.tanggal_invoice)
            : "-";
        const formattedDueDate = invoice.due_date
            ? formatDate(invoice.due_date)
            : "-";

        // Create status badge with blue theme colors
        let statusBadgeClass = "bg-secondary";
        if (invoice.payment_status === "Lunas") {
            statusBadgeClass = "bg-success"; // Using success color from theme
        } else if (invoice.payment_status === "Jatuh Tempo") {
            statusBadgeClass = "bg-danger"; // Using danger color from theme
        } else if (invoice.payment_status === "Belum Lunas") {
            statusBadgeClass = "bg-warning text-dark"; // Using warning color from theme
        }

        // Build HTML content
        let html = `
            <div class="row mb-3">
                <div class="col-md-6">
                    <h5>Informasi Invoice</h5>
                    <table class="table table-sm">
                        <tr>
                            <th>No. Invoice</th>
                            <td>${invoice.no_invoice || "-"}</td>
                        </tr>
                        <tr>
                            <th>Customer</th>
                            <td>${invoice.customer || "-"}</td>
                        </tr>
                        <tr>
                            <th>Lokasi</th>
                            <td>${invoice.location || "-"}</td>
                        </tr>
                        <tr>
                            <th>Tanggal Invoice</th>
                            <td>${formattedDate}</td>
                        </tr>
                        <tr>
                            <th>Jatuh Tempo</th>
                            <td>${formattedDueDate}</td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h5>Informasi Pembayaran</h5>
                    <table class="table table-sm">
                        <tr>
                            <th>Subtotal</th>
                            <td>${formattedSubtotal}</td>
                        </tr>
                        <tr>
                            <th>Total</th>
                            <td class="fw-bold">${formattedTotalAmount}</td>
                        </tr>
                        <tr>
                            <th>Status</th>
                            <td><span class="badge ${statusBadgeClass}">${
            invoice.payment_status
        }</span></td>
                        </tr>
                    </table>
                </div>
            </div>
        `;

        // Check for document path (either signed_document_path or document_path)
        const documentPath =
            invoice.signed_document_path || invoice.document_path;

        // Add document section if available
        if (documentPath) {
            html += `
                <div class="row mb-3">
                    <div class="col-12">
                        <h5>Dokumen Invoice</h5>
                        <button class="btn btn-sm btn-info view-document-btn" data-path="${documentPath}">
                            <i class="mdi mdi-file-pdf"></i> Lihat Dokumen
                        </button>
                    </div>
                </div>
            `;
        }

        // Add unit transactions section
        html += `
            <div class="row">
                <div class="col-12">
                    <h5>Unit Transactions</h5>
                    <div class="table-responsive">
                        <table class="table table-sm table-hover">
                            <thead>
                                <tr>
                                    <th>Unit</th>
                                    <th>Site</th>
                                    <th>Status</th>
                                    <th>Tanggal</th>
                                </tr>
                            </thead>
                            <tbody>
        `;

        // Add rows for each unit transaction
        if (invoice.unit_transactions && invoice.unit_transactions.length > 0) {
            invoice.unit_transactions.forEach((transaction) => {
                const unitCode = transaction.unit
                    ? transaction.unit.unit_code
                    : "-";
                const siteName = transaction.site
                    ? transaction.site.site_name
                    : "-";
                const formattedCreatedAt = formatDate(transaction.created_at);

                html += `
                    <tr>
                        <td>${unitCode}</td>
                        <td>${siteName}</td>
                        <td>${transaction.status}</td>
                        <td>${formattedCreatedAt}</td>
                    </tr>
                `;
            });
        } else {
            html += `
                <tr>
                    <td colspan="4" class="text-center">Tidak ada data transaksi unit.</td>
                </tr>
            `;
        }

        html += `
                        </tbody>
                    </table>
                </div>
            </div>
        `;

        // Set the HTML content
        detailContent.innerHTML = html;

        // Add event listener to view document button
        const viewDocumentBtn =
            detailContent.querySelector(".view-document-btn");
        if (viewDocumentBtn) {
            viewDocumentBtn.addEventListener("click", function () {
                const documentPath = this.getAttribute("data-path");
                showDocumentViewer(documentPath);
            });
        }
    }

    /**
     * Show document viewer in a modal
     */
    function showDocumentViewer(documentPath) {
        const viewerContent = document.getElementById(
            "document-viewer-content"
        );
        const documentUrl = `/assets/invoice_documents/${documentPath}`;

        // Set the document viewer content with 100% width
        viewerContent.innerHTML = `
            <iframe src="${documentUrl}" style="width: 100%; height: 100%; border: none; display: block;"></iframe>
        `;

        // Show the modal
        const modal = new bootstrap.Modal(
            document.getElementById("document-viewer-modal")
        );
        modal.show();
    }

    /**
     * Load Ready PO data from the server
     */
    function loadReadyPoData() {
        // Build query parameters
        const params = new URLSearchParams({
            page: readyPoCurrentPage,
            per_page: readyPoPerPage,
            search: readyPoCurrentSearch,
            site_id: readyPoCurrentSiteId,
        });

        // Fetch data from the server
        fetch(`/superadmin/ready-po-data?${params.toString()}`)
            .then((response) => {
                if (!response.ok) {
                    throw new Error("Network response was not ok");
                }
                return response.json();
            })
            .then((data) => {
                // Update Ready PO table
                renderReadyPoTable(data.transactions);

                // Update pagination
                updateReadyPoPagination(data.pagination);
            })
            .catch((error) => {
                readyPoTableBody.innerHTML = `
                    <tr>
                        <td colspan="7" class="text-center">Error loading Ready PO data. Please try again.</td>
                    </tr>
                `;
            });
    }

    /**
     * Render Ready PO table with data
     */
    function renderReadyPoTable(transactions) {
        // Clear existing rows
        readyPoTableBody.innerHTML = "";

        if (transactions.length === 0) {
            // Show no data message
            readyPoTableBody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center">Tidak ada data Ready PO yang ditemukan.</td>
                </tr>
            `;
            return;
        }

        // Add rows for each transaction
        transactions.forEach((transaction) => {
            const row = document.createElement("tr");

            // Calculate total value with proper null/undefined checks
            let totalValue = 0;
            if (
                transaction.parts &&
                Array.isArray(transaction.parts) &&
                transaction.parts.length > 0
            ) {
                totalValue = transaction.parts.reduce((sum, part) => {
                    // Ensure part exists and has required properties
                    if (!part) return sum;

                    const partPrice = parseFloat(part.price) || 0;
                    const quantity = parseFloat(part.quantity) || 0;
                    const partTotal = partPrice * quantity;
                    // Add 11% tax as per the formula
                    return sum + (partTotal + partTotal * 0.11);
                }, 0);
            }

            // Format currency values
            const formattedTotalValue = formatCurrency(totalValue);

            // Format date - using updated_at to show when data was last updated by site
            const formattedDate = transaction.updated_at
                ? formatDate(transaction.updated_at)
                : "-";

            // Get unit information with proper null/undefined checks
            const unitCode =
                transaction.unit && transaction.unit.unit_code
                    ? transaction.unit.unit_code
                    : "-";
            const unitType =
                transaction.unit && transaction.unit.unit_type
                    ? transaction.unit.unit_type
                    : "-";

            // Get site name with null check
            const siteName =
                transaction.site && transaction.site.site_name
                    ? transaction.site.site_name
                    : "-";

            // Get customer name with null check
            const customerName = transaction.customer || "-";

            // Get PO number - handle IMK site special case with proper null checks
            let poNumber = transaction.po_number || "-";
            if (transaction.site && transaction.site.site_id === "IMK") {
                // For IMK site, show MR number instead of PO number
                poNumber = transaction.noireq || "-";
            }

            // Set row content with safe attachment path handling
            const attachmentAttr =
                transaction.attachment_path &&
                transaction.attachment_path.trim()
                    ? `data-attachment="${transaction.attachment_path}"`
                    : "";

            row.innerHTML = `
                <td>
                    <a href="#" class="ready-po-link" data-id="${
                        transaction.id || ""
                    }" ${attachmentAttr}>${poNumber}</a>
                </td>
                <td>${unitCode}</td>
                <td>${unitType}</td>
                <td>${siteName}</td>
                <td>${customerName}</td>
                <td>${formattedDate}</td>
                <td>${formattedTotalValue}</td>
            `;

            readyPoTableBody.appendChild(row);
        });

        // Add event listeners to Ready PO links with better error handling
        document.querySelectorAll(".ready-po-link").forEach((link) => {
            link.addEventListener("click", function (e) {
                e.preventDefault();
                const attachmentPath = this.getAttribute("data-attachment");

                if (attachmentPath && attachmentPath.trim()) {
                    showReadyPoAttachment(attachmentPath.trim());
                } else {
                    alert(
                        "Tidak ada lampiran yang tersedia untuk transaksi ini."
                    );
                }
            });
        });
    }

    /**
     * Show Ready PO attachment in a modal
     */
    function showReadyPoAttachment(attachmentPath) {
        try {
            const viewerContent = document.getElementById(
                "ready-po-attachment-content"
            );
            if (!viewerContent) {
                alert("Error: Modal content element not found.");
                return;
            }

            const documentUrl = `/assets/lampiranunits/${encodeURIComponent(
                attachmentPath
            )}`;

            // Set the attachment viewer content with 100% width
            viewerContent.innerHTML = `
                <iframe src="${documentUrl}" style="width: 100%; height: 100%; border: none; display: block;"
                        onload="this.style.display='block';"
                        onerror="this.style.display='none'; this.parentNode.innerHTML='<div class=\\'text-center p-4\\'>Error loading document. File may not exist or is corrupted.</div>';">
                </iframe>
            `;

            // Show the modal
            const modalElement = document.getElementById(
                "ready-po-attachment-modal"
            );
            if (modalElement) {
                const modal = new bootstrap.Modal(modalElement);
                modal.show();
            } else {
                alert("Error: Modal element not found.");
            }
        } catch (error) {
            console.error("Error showing Ready PO attachment:", error);
            alert("Error loading attachment. Please try again.");
        }
    }

    /**
     * Update pagination information and controls
     */
    function updatePagination(pagination) {
        currentPage = pagination.current_page;
        totalPages = pagination.last_page;
        totalInvoices = pagination.total;

        // Update pagination text
        const start = (currentPage - 1) * perPage + 1;
        const end = Math.min(currentPage * perPage, totalInvoices);
        paginationStart.textContent = totalInvoices > 0 ? start : 0;
        paginationEnd.textContent = end;
        paginationTotal.textContent = totalInvoices;

        // Update pagination buttons
        prevPageBtn.disabled = currentPage <= 1;
        nextPageBtn.disabled = currentPage >= totalPages;
    }

    /**
     * Handle search input
     */
    function handleSearch() {
        currentSearch = searchInput.value.trim();
        currentPage = 1; // Reset to first page
        loadInvoicesData();
    }

    /**
     * Handle filter change
     */
    function handleFilterChange() {
        currentStatus = statusFilter.value;
        currentSiteId = siteFilter.value;
        currentPage = 1; // Reset to first page
        loadInvoicesData();
    }

    /**
     * Handle previous page button click
     */
    function handlePrevPage() {
        if (currentPage > 1) {
            currentPage--;
            loadInvoicesData();
        }
    }
    function handlelenghtPage() {
        perPage = lengthPageBtn.value;
        currentPage = 1;
        loadInvoicesData();
    }

    /**
     * Handle next page button click
     */
    function handleNextPage() {
        if (currentPage < totalPages) {
            currentPage++;
            loadInvoicesData();
        }
    }

    /**
     * Handle month picker change
     */
    function handleMonthChange() {
        currentPage = 1; // Reset to first page
        loadInvoicesData();
    }

    /**
     * Handle previous month button click
     */
    function handlePrevMonth() {
        const [year, month] = monthPicker.value.split("-");
        let prevMonth = parseInt(month) - 1;
        let prevYear = parseInt(year);

        if (prevMonth < 1) {
            prevMonth = 12;
            prevYear--;
        }

        monthPicker.value = `${prevYear}-${String(prevMonth).padStart(2, "0")}`;
        handleMonthChange();
    }

    /**
     * Handle next month button click
     */
    function handleNextMonth() {
        const [year, month] = monthPicker.value.split("-");
        let nextMonth = parseInt(month) + 1;
        let nextYear = parseInt(year);

        if (nextMonth > 12) {
            nextMonth = 1;
            nextYear++;
        }

        monthPicker.value = `${nextYear}-${String(nextMonth).padStart(2, "0")}`;
        handleMonthChange();
    }

    /**
     * Update Ready PO pagination information and controls
     */
    function updateReadyPoPagination(pagination) {
        readyPoCurrentPage = pagination.current_page;
        readyPoTotalPages = pagination.last_page;
        readyPoTotalTransactions = pagination.total;

        // Update pagination text
        const start = (readyPoCurrentPage - 1) * readyPoPerPage + 1;
        const end = Math.min(
            readyPoCurrentPage * readyPoPerPage,
            readyPoTotalTransactions
        );
        readyPoPaginationStart.textContent =
            readyPoTotalTransactions > 0 ? start : 0;
        readyPoPaginationEnd.textContent = end;
        readyPoPaginationTotal.textContent = readyPoTotalTransactions;

        // Update pagination buttons
        readyPoPrevPageBtn.disabled = readyPoCurrentPage <= 1;
        readyPoNextPageBtn.disabled = readyPoCurrentPage >= readyPoTotalPages;
    }

    /**
     * Handle Ready PO search input
     */
    function handleReadyPoSearch() {
        readyPoCurrentSearch = readyPoSearchInput.value.trim();
        readyPoCurrentPage = 1; // Reset to first page
        loadReadyPoData();
    }

    /**
     * Handle Ready PO filter change
     */
    function handleReadyPoFilterChange() {
        readyPoCurrentSiteId = readyPoSiteFilter.value;
        readyPoCurrentPage = 1; // Reset to first page
        loadReadyPoData();
    }

    /**
     * Handle Ready PO previous page button click
     */
    function handleReadyPoPrevPage() {
        if (readyPoCurrentPage > 1) {
            readyPoCurrentPage--;
            loadReadyPoData();
        }
    }

    function handleReadyPolengthPage() {
        if (readyPoCurrentPage > 1) {
            readyPoCurrentPage--;
            loadReadyPoData();
        }
    }

    /**
     * Handle Ready PO next page button click
     */
    function handleReadyPoNextPage() {
        if (readyPoCurrentPage < readyPoTotalPages) {
            readyPoCurrentPage++;
            loadReadyPoData();
        }
    }

    /**
     * Format currency value
     */
    function formatCurrency(value) {
        return new Intl.NumberFormat("id-ID", {
            style: "currency",
            currency: "IDR",
            minimumFractionDigits: 0,
            maximumFractionDigits: 0,
        }).format(value);
    }

    /**
     * Format date value
     */
    function formatDate(dateString) {
        const options = { year: "numeric", month: "long", day: "numeric" };
        return new Date(dateString).toLocaleDateString("id-ID", options);
    }

    /**
     * Debounce function to limit how often a function can be called
     */
    function debounce(func, delay) {
        let timeout;
        return function () {
            const context = this;
            const args = arguments;
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(context, args), delay);
        };
    }

    // Export functionality
    const exportInvoicesBtn = document.getElementById("export-invoices-btn");
    const exportInvoicesModal = new bootstrap.Modal(document.getElementById("exportInvoicesModal"));
    const exportInvoicesPdfBtn = document.getElementById("export-invoices-pdf-btn");
    const exportInvoicesExcelBtn = document.getElementById("export-invoices-excel-btn");

    // Show export modal
    if (exportInvoicesBtn) {
        exportInvoicesBtn.addEventListener("click", function () {
            exportInvoicesModal.show();
        });
    }

    // Export to PDF
    if (exportInvoicesPdfBtn) {
        exportInvoicesPdfBtn.addEventListener("click", function () {
            exportInvoices('pdf');
            exportInvoicesModal.hide();
        });
    }

    // Export to Excel
    if (exportInvoicesExcelBtn) {
        exportInvoicesExcelBtn.addEventListener("click", function () {
            exportInvoices('excel');
            exportInvoicesModal.hide();
        });
    }

    /**
     * Export invoices data
     *
     * @param {string} format - Export format ('pdf' or 'excel')
     */
    function exportInvoices(format) {
        // Get current filter values
        const params = new URLSearchParams({
            search: currentSearch,
            status: currentStatus,
            site_id: currentSiteId,
        });

        if (currentStartDate && currentEndDate) {
            params.append("start_date", currentStartDate);
            params.append("end_date", currentEndDate);
        }

        // Create export URL
        const exportUrl = `/superadmin/invoices/export/${format}?${params.toString()}`;

        // Open export URL in new window/tab
        window.open(exportUrl, '_blank');
    }
});
