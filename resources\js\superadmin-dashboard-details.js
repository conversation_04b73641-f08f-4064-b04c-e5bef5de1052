window.openSiteDetailsModal = function(siteId) {
    // Get the selected month and filters
    const monthPicker = document.getElementById('month-picker');
    const divisionFilter = document.getElementById('division-filter');
    const siteFilter = document.getElementById('site-filter');

    const selectedMonth = monthPicker ? monthPicker.value : '';
    const selectedDivision = divisionFilter ? divisionFilter.value : '';
    const selectedSite = siteFilter ? siteFilter.value : '';

    // Get the site name from the site card
    const siteName = document.querySelector(`.site-card[data-site-id="${siteId}"] .card-title`).textContent;

    // Update modal title with site name
    document.getElementById('siteDetailsModalLabel').textContent = `Detail Informasi ${siteName}`;

    // Add division filter to title if applicable
    if (selectedDivision) {
        document.getElementById('siteDetailsModalLabel').textContent += ` - Divisi: ${selectedDivision}`;
    }

    // Show loading spinner, hide content
    const loadingSpinner = document.querySelector('#siteDetailsModal .spinner-border').parentElement;
    const contentContainer = document.getElementById('siteDetailsContent');

    loadingSpinner.style.display = 'block';
    contentContainer.style.display = 'none';

    // Show the modal
    const modal = new bootstrap.Modal(document.getElementById('siteDetailsModal'));
    modal.show();

    // Build query parameters
    const params = new URLSearchParams();
    params.append('month', selectedMonth);
    if (selectedDivision) params.append('division', selectedDivision);
    if (selectedSite) params.append('site', selectedSite);

    // Fetch site details via AJAX
    fetch(`/superadmin/site-details/${siteId}?${params.toString()}`)
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            // Render the site details content
            renderSiteDetails(data, siteId, siteName);

            // Hide loading spinner, show content
            loadingSpinner.style.display = 'none';
            contentContainer.style.display = 'block';
        })
        .catch(error => {
            contentContainer.innerHTML = `
                <div class="alert alert-danger">
                    <i class="mdi mdi-alert-circle-outline me-2"></i>
                    Gagal memuat data. Silakan coba lagi.
                </div>
            `;
            loadingSpinner.style.display = 'none';
            contentContainer.style.display = 'block';
        });
};

/**
 * Render site details content
 *
 * @param {Object} data - The site details data
 * @param {string} siteId - The ID of the site
 * @param {string} siteName - The name of the site
 */
function renderSiteDetails(data, siteId, siteName) {
    const container = document.getElementById('siteDetailsContent');

    // Build HTML content
    let html = `
        <div class="site-details-header mb-4">
            <h4 class="text-center text-primary">${siteName}</h4>
        </div>

        <div class="row row-cols-1 row-cols-lg-2">
            <!-- Left Column -->
            <div class="col mb-4">
                <!-- Repair/Pending Invoices -->
                <div class="card mb-4">
                    <div class="card-header bg-info text-dark">
                        <h5 class="mb-0">Invoice Pending / Perbaikan</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-6">
                                <div class="d-flex align-items-center">
                                    <div class="stats-icon bg-warning text-white me-3" style="width: 40px; height: 40px; font-size: 18px; display: flex; align-items: center; justify-content: center; border-radius: 50%;">
                                        <i class="mdi mdi-file-document"></i>
                                    </div>
                                    <div>
                                        <small class="text-muted">Jumlah Invoice</small>
                                        <h4 class="mb-0">${data.repair_pending_invoices.count}</h4>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="d-flex align-items-center">
                                    <div class="stats-icon bg-primary text-white me-3" style="width: 40px; height: 40px; font-size: 18px; display: flex; align-items: center; justify-content: center; border-radius: 50%;">
                                        <i class="mdi mdi-currency-usd"></i>
                                    </div>
                                    <div>
                                        <small class="text-muted">Total Nilai</small>
                                        <h4 class="mb-0">Rp ${formatNumber(data.repair_pending_invoices.value)}</h4>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Part Not Ready -->
                <div class="card mb-4">
                    <div class="card-header bg-info text-dark">
                        <h5 class="mb-0">Part Not Ready</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="stats-icon bg-danger text-white me-3" style="width: 40px; height: 40px; font-size: 18px; display: flex; align-items: center; justify-content: center; border-radius: 50%;">
                                <i class="mdi mdi-alert-circle"></i>
                            </div>
                            <div>
                                <small class="text-muted">Jumlah Part</small>
                                <h4 class="mb-0">${data.part_not_ready.count}</h4>
                            </div>
                        </div>

                        ${renderPartNotReadyTable(data.part_not_ready)}
                    </div>
                </div>

                <!-- Pending Requests -->
                <div class="card mb-4">
                    <div class="card-header bg-info text-dark">
                        <h5 class="mb-0">Permintaan Part ke HO (Belum Selesai)</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="stats-icon bg-info text-white me-3" style="width: 40px; height: 40px; font-size: 18px; display: flex; align-items: center; justify-content: center; border-radius: 50%;">
                                <i class="mdi mdi-truck-delivery"></i>
                            </div>
                            <div>
                                <small class="text-muted">Jumlah Permintaan</small>
                                <h4 class="mb-0">${data.pending_requests.count}</h4>
                            </div>
                        </div>

                        ${renderPendingRequestsTable(data.pending_requests)}
                    </div>
                </div>
            </div>

            <!-- Right Column -->
            <div class="col mb-4">
                <!-- Best Part Sales -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Part Penjualan Terbaik</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="best-part-sales">
                            ${renderBestPartSalesSections(data.best_part_sales, data.division_filter)}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    container.innerHTML = html;
}

/**
 * Render part not ready table
 *
 * @param {Object} data - The part not ready data
 * @returns {string} HTML for the table
 */
function renderPartNotReadyTable(data) {
    if (data.count === 0) {
        return `
            <div class="empty-state">
                <div class="empty-state-icon">
                    <i class="mdi mdi-check-circle text-a11y-success"></i>
                </div>
                <h5 class="empty-state-title">Semua Part Tersedia</h5>
                <p class="empty-state-text">Tidak ada part yang perlu diperhatikan saat ini.</p>
            </div>
        `;
    }

    let html = `
        <div class="table-responsive" style="max-height: 300px; overflow-y: auto; overflow-x: auto; -webkit-overflow-scrolling: touch;">
            <table class="table table-sm table-striped" style="min-width: 400px;">
                <thead>
                    <tr>
                        <th>Part Name</th>
                        <th>Stock</th>
                        <th>Min Stock</th>
                    </tr>
                </thead>
                <tbody>
    `;

    data.items.forEach(part => {
        html += `
            <tr>
                <td>${part.part_name}</td>
                <td>${part.stock}</td>
                <td>${part.min_stock}</td>
            </tr>
        `;
    });

    html += `
                </tbody>
            </table>
        </div>
    `;

    return html;
}

/**
 * Render pending requests table
 *
 * @param {Object} data - The pending requests data
 * @returns {string} HTML for the table
 */
function renderPendingRequestsTable(data) {
    if (data.count === 0) {
        return `
            <div class="empty-state">
                <div class="empty-state-icon">
                    <i class="mdi mdi-package-variant-closed-check text-a11y-success"></i>
                </div>
                <h5 class="empty-state-title">Tidak Ada Permintaan Tertunda</h5>
                <p class="empty-state-text">Semua permintaan part telah diproses.</p>
            </div>
        `;
    }

    let html = `
        <div class="table-responsive" style="max-height: 300px; overflow-y: auto; overflow-x: auto; -webkit-overflow-scrolling: touch;">
            <table class="table table-sm table-striped" style="min-width: 400px;">
                <thead>
                    <tr>
                        <th>Part Name</th>
                        <th>Quantity</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
    `;

    data.items.forEach(request => {
        html += `
            <tr>
                <td>${request.part_name}</td>
                <td>${request.quantity}</td>
                <td>${request.status}</td>
            </tr>
        `;
    });

    html += `
                </tbody>
            </table>
        </div>
    `;

    return html;
}

/**
 * Render best part sales section
 *
 * @param {Object} data - The best part sales data for a specific type
 * @param {string} type - The part type (AC, TYRE, FABRIKASI)
 * @param {string} icon - The icon class for this part type
 * @returns {string} HTML for the section
 */
function renderBestPartSalesSection(data, type, icon) {
    let html = `
        <div class="part-type-section p-3 border-bottom">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div class="d-flex align-items-center">
                    <div class="stats-icon bg-a11y-success text-white me-2" style="width: 36px; height: 36px; font-size: 18px; display: flex; align-items: center; justify-content: center; border-radius: 50%;">
                        <i class="mdi ${icon}"></i>
                    </div>
                    <h5 class="mb-0">${type}</h5>
                </div>
                <div class="text-end">
                    <small class="text-muted d-block">Total Pendapatan (inc. PPN)</small>
                    <h5 class="mb-0 text-success">Rp ${formatNumber(data.total_revenue_with_ppn)}</h5>
                </div>
            </div>
    `;

    if (data.count === 0) {
        html += `
            <div class="empty-state">
                <div class="empty-state-icon">
                    <i class="mdi mdi-${icon} text-a11y-secondary"></i>
                </div>
                <h5 class="empty-state-title">Belum Ada Data Penjualan</h5>
                <p class="empty-state-text">Belum ada data penjualan part ${type} untuk periode ini.</p>
                <div class="empty-state-cta">
                    <button type="button" class="btn btn-sm btn-outline-primary view-stock-btn">
                        <i class="mdi mdi-database-search me-1"></i> Lihat Stok Part
                    </button>
                </div>
            </div>
        `;
    } else {
        html += `
            <div class="table-responsive" style="overflow-x: auto; -webkit-overflow-scrolling: touch;">
                <table class="table table-sm table-striped mb-0" style="min-width: 400px;">
                    <thead>
                        <tr>
                            <th>Part Name</th>
                            <th>Qty</th>
                            <th>Total</th>
                            <th>Kontribusi</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        data.items.forEach(part => {
            html += `
                <tr>
                    <td>${part.part_name}</td>
                    <td>${formatNumber(part.total_quantity,0)}</td>       
                    <td>Rp ${formatNumber(part.total_value)}</td>
                    </tr>
                    `;
                    // <td>${part.contribution_percent}%</td>
        });

        html += `
                    </tbody>
                </table>
            </div>
        `;
    }

    html += '</div>';

    return html;
}

/**
 * Render all best part sales sections or just the filtered one
 *
 * @param {Object} bestPartSales - The best part sales data for all types
 * @param {string|null} divisionFilter - The division filter if applied
 * @returns {string} HTML for all sections
 */
function renderBestPartSalesSections(bestPartSales, divisionFilter) {
    // If division filter is applied, only show that division
    if (divisionFilter) {
        // Check if the division exists in the data
        if (bestPartSales[divisionFilter]) {
            let icon = 'mdi-package-variant';

            // Set appropriate icon based on division
            if (divisionFilter === 'AC') {
                icon = 'mdi-air-conditioner';
            } else if (divisionFilter === 'TYRE') {
                icon = 'ion ion-md-aperture';
            } else if (divisionFilter === 'FABRIKASI') {
                icon = 'mdi-factory';
            } else if (divisionFilter === 'PERLENGKAPAN AC') {
                icon = 'mdi-tools';
            }

            return renderBestPartSalesSection(bestPartSales[divisionFilter], divisionFilter, icon);
        } else {
            return `
                <div class="empty-state p-4">
                    <div class="empty-state-icon">
                        <i class="mdi mdi-alert-circle-outline text-warning"></i>
                    </div>
                    <h5 class="empty-state-title">Tidak Ada Data</h5>
                    <p class="empty-state-text">Tidak ada data penjualan untuk divisi ${divisionFilter}.</p>
                </div>
            `;
        }
    }

    // If no division filter, show all divisions
    let html = '';

    // Standard divisions
    const divisions = {
        'AC': 'mdi-air-conditioner',
        'TYRE': 'ion ion-md-aperture',
        'FABRIKASI': 'mdi-factory',
        'PERLENGKAPAN AC': 'mdi-tools',
        'PERSEDIAAN LAINNYA': 'mdi-package-variant'
    };

    // Render each division that exists in the data
    for (const [division, icon] of Object.entries(divisions)) {
        if (bestPartSales[division]) {
            html += renderBestPartSalesSection(bestPartSales[division], division, icon);
        }
    }

    return html;
}

/**
 * Format number as Indonesian currency
 *
 * @param {number} number - The number to format
 * @returns {string} Formatted number
 */
function formatNumber(number) {
    return new Intl.NumberFormat('id-ID').format(Math.round(number));
}
