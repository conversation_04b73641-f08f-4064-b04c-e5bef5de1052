@php
use Carbon\Carbon;
@endphp

<!DOCTYPE html>
<html>

<head>
    <title>Data <PERSON><PERSON><PERSON><PERSON></title>
    <style>
        body {
            font-family: 'Times New Roman', Times, serif;
            font-size: 12px;
        }

        .container {
            padding: 40px;
        }

        .header-table {
            width: 100%;
            border: 0;
        }

        .header-table td {
            vertical-align: middle;
        }

        .logo-cell {
            width: 70px;
        }

        .logo-cell img {
            width: 60px;
        }

        .company-info {
            text-align: center;
            font-weight: bold;
        }

        .company-info h1 {
            font-size: 28px;
        }

        .hr {
            border: 0.4px solid black;
            margin: 10px 0;
        }

        .table-title {
            background-color: #00BCD4;
            color: white;
            font-weight: bold;
            text-align: center;
            padding: 6px;
            margin-bottom: 10px;
        }

        .table-title {
            color: #000;
            font-family: Arial, Helvetica, sans-serif;
            font-weight: bold;
            font-size: 16px;
        }

        table {
            border-collapse: collapse;
            margin-top: 5px;
        }

        table,
        th,
        td {
            border: 1px solid #000;
        }

        th,
        td {
            padding: 6px;
            text-align: left;
        }

        .signature {
            margin-top: 60px;
            width: 100%;
        }

        .signature td {
            width: 50%;
            text-align: center;
            vertical-align: top;
            border: 0px;
        }

        .signature tr td {
            border: 0px;
        }

        .footer-label {
            font-weight: bold;
            text-decoration: underline;
        }
    </style>
</head>

<body>
    <div class="container">
        <!-- HEADER DENGAN TABEL -->
        <table class="header-table">
            <tr>
                <td class="logo-cell" style="border: 0px;">
                    <img src="{{ public_path('assets/images/logo-nom.png') }}" alt="PWB LOGO">
                </td>
                <td style="border: 0px;">
                    <div class="company-info">
                        <h1 style="margin: 0;">PT. PUTERA WIBOWO BORNEO</h1>
                        <p style="margin: 0;">Address : Jl. Palam Raya No. 10B Kel. Guntung Manggis, Kec. Landasan Ulin</p>
                        <p style="margin: 0;">Banjarbaru, Kalimantan Selatan Kode Pos 70721</p>
                    </div>
                    <!-- GARIS HITAM -->
                    <div class="hr"></div>
                </td>
            </tr>
        </table>
        <!-- JUDUL TABEL -->
        <div class="table-title">DATA PENGGUNAAN FREON</div>

        @php
        $partCodes = [];
        $partName = [];
        @endphp
        @foreach($transactions as $transaction) @foreach($transaction->parts as $part) @php $partCodes[] = $part->partInventory->part->part_code; $partName[] = $part->partInventory->part->part_name; @endphp @endforeach @endforeach

        <table style="border: 0px;font-family: Arial, Helvetica; font-size: 12px; font-weight: bold;">
            <tr>
                <td style="width: 100px;border: 0px; padding: 0;margin: 0;">PART NUMBER</td>
                <td style="width: 8px;border: 0px; padding: 0;margin: 0;">:</td>

                <td style="border: 0px; padding: 0;margin: 0;">
                    {{ implode(', ', $partCodes) }}
                </td>
            </tr>
            <tr>
                <td style="border: 0px; padding: 10px 0px 0px 0px;margin: 0;">DESCRIPTION</td>
                <td style="border: 0px; padding: 0;margin: 0;">:</td>
                <td style="border: 0px; padding: 0;margin: 0;">
                    {{ implode(', ', $partName) }}
                </td>
            </tr>
        </table>

        <!-- TABEL DATA -->
        <table style="margin-left: 100px; width: max-content; padding-top: 10px; font-family: Arial, Helvetica, sans-serif;">
            <thead style=" background-color: #00BCD4;">
                <tr>
                    <th style="width: 30px;  padding: 4px; margin: 0px; text-align: center;">NO</th>
                    <th style="width: 100px; padding: 4px; margin: 0px; text-align: center;">DATE</th>
                    <th style="width: 160px; padding: 4px; margin: 0px; text-align: center;">UNIT</th>
                    <th style="width: 100px; padding: 4px; margin: 0px; text-align: center;">KETERANGAN</th>
                </tr>
            </thead>
            <tbody>
                @php
                $grouped = $transactions->groupBy('do_date');
                $i=1;
                @endphp
                @foreach ($grouped as $doDate => $group)
                @foreach ($group as $index => $transaction)
                <tr>
                    <td style="width: 10px; padding: 4px; text-align: center;">{{ $i++}}</td>
                    @if ($index === 0)
                    <td style="width: 100px; padding: 4px; text-align: center;" rowspan="{{ count($group) }}">
                        {{ $doDate ? Carbon::parse($doDate)->translatedFormat('d F Y') : '-' }}
                    </td>
                    @endif
                    <td style="width: 100px; padding: 4px; text-align: center;">{{ $transaction->unit->unit_code ?? '-' }}</td>
                    <td style="width: 200px; padding: 4px; text-align: center;">{{ $transaction->remarks ?? '-' }}</td>
                </tr>
                @endforeach
                @endforeach
                @for ($j = $i + 1; $j <= 10; $j++)
                    <tr>
                    <td style="padding: 4px;text-align: center;">{{ $j }}</td>
                    <td style="padding: 4px;"></td>
                    <td style="padding: 4px;"></td>
                    <td style="padding: 4px;"></td>
                    </tr>
                    @endfor
            </tbody>
        </table>

        <!-- TANDA TANGAN -->
        <table class="signature" style="border: 0px; font-family: Arial, Helvetica, sans-serif; font-weight: bold;">
            <tr>
                <td style="border: 0px;">
                    Approved By,<br><br><br><br><br><br>
                    <span class="footer-label">Technician</span><br>
                    PT. Putera Wibowo Borneo
                </td>
                <td>
                    Approved By,<br><br><br><br><br><br>
                    <span class="footer-label">Pengawas</span><br>
                    PT. Tanjung Raya Bersama
                </td>
            </tr>
        </table>
    </div>
</body>

</html>