<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('backlog_parts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('backlog_id')
                ->constrained('backlogs')
                ->onUpdate('cascade')
                ->onDelete('cascade');
            $table->string('part_code', 50);
            $table->foreign('part_code')
                ->references('part_code')
                ->on('parts')
                ->onUpdate('cascade')
                ->onDelete('cascade');
            $table->integer('quantity')->default(1);
            $table->timestamps();
            
            // Add indexes for better performance
            $table->index(['backlog_id', 'part_code']);
            $table->index('part_code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('backlog_parts');
    }
};
