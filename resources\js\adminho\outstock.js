import Swal from "sweetalert2";
import bootstrap from '../bootstrap-init';

// Global variables for pagination
let currentPage = 1;
const itemsPerPage = 15; // 15 items per page for outstock table

document.addEventListener("DOMContentLoaded", function () {
    const outStockTable = document.getElementById("out_stock_table");
    const siteIdSelect = document.getElementById("idfilter");
    const startDateInput = document.getElementById("start_date");
    const endDateInput = document.getElementById("end_date");
    const addButton = document.getElementById("add_button");
    const idPartInventorySelect = document.getElementById("id_partinventory");
    const newSiteIdSelect = document.getElementById("new_site_id");
    const dateOutInput = document.getElementById("date_out");
    const quantityInput = document.getElementById("quantity");
    const destinationSelect = document.getElementById("destination");
    const noteInput = document.getElementById("note");
    const suratJalanInput = document.getElementById("surat_jalan");
    const totalid = document.getElementById("totalout");
    const partInventoryAutocomplete = document.getElementById(
        "part_inventory_autocomplete"
    );
    const idPartinventoryInput = document.getElementById("id_partinventory");
    const autocompleteList = document.getElementById("autocomplete_list");

    // format tanggal
    function formatTanggal(dateString) {
        const bulan = [
            "Januari",
            "Februari",
            "Maret",
            "April",
            "Mei",
            "Juni",
            "Juli",
            "Agustus",
            "September",
            "Oktober",
            "November",
            "Desember",
        ];

        const date = new Date(dateString);
        if (isNaN(date.getTime())) return "Tanggal tidak valid";

        const tanggal = date.getDate();
        const bulanNama = bulan[date.getMonth()];
        const tahun = date.getFullYear();

        return `${tanggal} ${bulanNama} ${tahun}`;
    }

    function loadLastActivities() {
        fetch("/last-activities")
            .then((response) => response.json())
            .then((data) => {
                const ul = document.getElementById("activityList");

                if (!ul) {
                    console.error('Element with ID "activityList" not found!');
                    return;
                }
                ul.innerHTML = "";
                data.forEach((activity) => {
                    const li = document.createElement("li");
                    const a = document.createElement("a");
                    a.href = "#";
                    a.textContent = activity.description;
                    li.appendChild(a);
                    ul.appendChild(li);
                });
            })
            .catch((error) => {
                console.error("Error fetching activities:", error);
                const ul = document.getElementById("activityList");
                if (ul) {
                    ul.innerHTML = "<li>Error loading activities.</li>";
                }
            });
    }

    let timeoutId;

    partInventoryAutocomplete.addEventListener("input", function () {
        const searchTerm = this.value.trim();
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => {
            if (searchTerm.length >= 2) {
                fetchAutocompleteResults(searchTerm);
            } else {
                clearAutocompleteList();
            }
        }, 300);
    });

    function fetchAutocompleteResults(searchTerm) {
        fetch(`/part-inventories/autocomplete?term=${searchTerm}`, {
            headers: {
                "X-CSRF-TOKEN": document.querySelector(
                    'meta[name="csrf-token"]'
                ).content,
            },
        })
            .then((response) => {
                if (!response.ok) {
                    return response.json().then((err) => {
                        throw new Error(
                            `HTTP error! status: ${response.status}, message: ${
                                err.message || response.statusText
                            }`
                        );
                    });
                }
                return response.json();
            })
            .then((data) => {
                populateAutocompleteList(data);
            })
            .catch((error) => {
                console.error("Error fetching autocomplete data:", error);
                Swal.fire(
                    "Kesalahan",
                    error.message ||
                        "Gagal mengambil data autocomplete. Silakan coba lagi.",
                    "error"
                );
            });
    }

    function populateAutocompleteList(data) {
        clearAutocompleteList();

        if (data && data.length > 0) {
            autocompleteList.style.display = "block";
            data.forEach((item) => {
                const listItem = document.createElement("li");
                listItem.textContent = `${item.part.part_name+' Stock :'+item.stock_quantity}`;
                listItem.dataset.id = item.part_inventory_id;
                listItem.addEventListener("click", handleAutocompleteSelection);
                autocompleteList.appendChild(listItem);
            });
        } else {
            autocompleteList.style.display = "none";
        }
    }

    function clearAutocompleteList() {
        autocompleteList.innerHTML = "";
        autocompleteList.style.display = "none";
    }

    function handleAutocompleteSelection(event) {
        const selectedItem = event.target;
        partInventoryAutocomplete.value = selectedItem.textContent;
        idPartinventoryInput.value = selectedItem.dataset.id;
        clearAutocompleteList();
    }
    document.addEventListener("click", function (event) {
        if (
            !partInventoryAutocomplete.contains(event.target) &&
            !autocompleteList.contains(event.target)
        ) {
            clearAutocompleteList();
        }
    });

    // Function to create pagination item
    function createPaginationItem(page, text, isActive = false) {
        const li = document.createElement('li');
        li.className = `page-item ${isActive ? 'active' : ''}`;

        const a = document.createElement('a');
        a.className = 'page-link';
        a.href = '#';
        a.textContent = text;
        a.dataset.page = page;

        li.appendChild(a);
        return li;
    }

    // Function to render pagination
    function renderPagination(data) {
        try {
            const paginationContainer = document.getElementById('outstock-pagination');
            if (!paginationContainer) {
                console.error('Pagination container not found');
                return;
            }

            paginationContainer.innerHTML = '';

            if (data.last_page > 1) {
                const pagination = document.createElement('ul');
                pagination.className = 'pagination pagination-rounded  justify-content-center';

                // Previous button
                if (data.current_page > 1) {
                    pagination.appendChild(createPaginationItem(data.current_page - 1, '\u00ab'));
                }

                // Page numbers
                for (let i = 1; i <= data.last_page; i++) {
                    pagination.appendChild(createPaginationItem(i, i, i === data.current_page));
                }

                // Next button
                if (data.current_page < data.last_page) {
                    pagination.appendChild(createPaginationItem(data.current_page + 1, '\u00bb'));
                }

                paginationContainer.appendChild(pagination);

                // Add event listeners to pagination links
                paginationContainer.querySelectorAll('.page-link').forEach(link => {
                    link.addEventListener('click', function(e) {
                        e.preventDefault();
                        const page = parseInt(this.dataset.page);
                        currentPage = page;
                        fetchOutStocks(siteIdSelect.value, startDateInput.value, endDateInput.value, page);
                    });
                });
            }

            // Update global pagination data
            window.outstockPaginationData = data;
        } catch (error) {
            console.error('Error rendering pagination:', error);
        }
    }

    function fetchOutStocks(siteId = "all", startDate = null, endDate = null, page = 1) {
        totalid.innerText = 0;
        loadLastActivities();
        currentPage = page;

        // Show loading indicator
        outStockTable.innerHTML = '<tr><td colspan="8" class="text-center">.</td></tr>';

        let url = `/out-parts/filter?site_id=${siteId}&page=${page}&per_page=${itemsPerPage}`;
        if (startDate) {
            url += `&start_date=${startDate}`;
        }
        if (endDate) {
            url += `&end_date=${endDate}`;
        }

        fetch(url, {
            headers: {
                "X-CSRF-TOKEN": document.querySelector(
                    'meta[name="csrf-token"]'
                ).content,
            },
        })
            .then((response) => {
                if (!response.ok) {
                    return response.json().then((err) => {
                        throw new Error(
                            `HTTP error! status: ${response.status}, message: ${
                                err.message || response.statusText
                            }`
                        );
                    });
                }
                return response.json();
            })
            .then((data) => {
                let outStocks = data.warehouseOutStocks;
                if (!outStocks || outStocks.length === 0) {
                    console.warn("No outStocks data received.");
                    outStockTable.innerHTML =
                        '<tr><td colspan="8">No data available.</td></tr>';
                    return;
                }

                let html = "";
                let total = 0;
                let rowCounter = 1;
                outStocks.forEach((outStock) => {
                    total += outStock.quantity;
                    const partCode =
                        outStock.part_inventory?.part?.part_code || "N/A";
                    const partName =
                        outStock.part_inventory?.part?.part_name || "N/A";
                    const destinationName =
                        outStock.destinationid?.site_name ||
                        outStock.customer?.customer_name ||
                        "Out Stok";
                    const quantity = outStock.quantity || 0;
                    const dateOut = outStock.date_out || "N/A";
                    const status = outStock.status || "out";
                    const note = outStock.notes || "";
                    const destinationtype = outStock.destination_type;
                    const idOutstock = outStock.warehouse_out_stock_id;

                    let actionButtons = "";

                    // Check if notes contain 'Part Digabung ke' to determine whether to show delete button
                    const isPartMerged = note && note.includes('Part Digabung ke');

                    if (destinationtype || isPartMerged) {
                        // Don't show delete button for destination type or merged parts
                        actionButtons = isPartMerged ?
                            '<span class="text-muted" title="Part ini telah digabung dan tidak dapat dihapus">Tidak dapat dihapus</span>' :
                            'no action';
                    } else {
                        actionButtons = `<button class="btn btn-danger btn-sm delete-button" data-id="${idOutstock}">Delete</button>`;
                    }

                    // Add a special class for merged parts
                    const rowClass = isPartMerged ? 'class="table-secondary"' : '';

                    html += `<tr ${rowClass}>
                        <td>${rowCounter++}</td>
                        <td>${partCode}</td>
                        <td>${partName}</td>
                        <td>${destinationName}</td>
                        <td>${quantity}</td>
                        <td>${formatTanggal(dateOut)}</td>
                        <td>${note}</td>
                        <td>${actionButtons}</td>
                    </tr>`;
                });
                totalid.innerText = total;
                outStockTable.innerHTML = html;
                // Attach event listeners to delete buttons
                const deleteButtons = document.querySelectorAll(".delete-button");
                deleteButtons.forEach((button) => {
                    button.addEventListener("click", function () {
                        const id = this.dataset.id;
                        deleteOutStock(id);
                    });
                });

                // Initialize tooltips for any elements with title attributes
                const tooltipTriggerList = [].slice.call(document.querySelectorAll('[title]'));
                tooltipTriggerList.map(function (tooltipTriggerEl) {
                    return new bootstrap.Tooltip(tooltipTriggerEl);
                });

                // Render pagination
                renderPagination({
                    current_page: data.current_page,
                    per_page: data.per_page,
                    last_page: data.last_page,
                    total: data.total
                });
            })
            .catch((error) => {
                console.error("Error fetching data:", error);
                Swal.fire(
                    "Kesalahan",
                    error.message ||
                        "Gagal mengambil data autocomplete. Silakan coba lagi.",
                    "error"
                );
                outStockTable.innerHTML =
                    '<tr><td colspan="8">Error fetching data.</td></tr>';
            });
    }

    function deleteOutStock(id) {
        Swal.fire({
            title: "Apakah Anda yakin?",
            text: "Anda tidak akan dapat mengembalikan ini!",
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: "Ya, hapus!",
        }).then((result) => {
            if (result.isConfirmed) {
                fetch(`/out-parts/${id}`, {
                    method: "DELETE",
                    headers: {
                        "X-CSRF-TOKEN": document.querySelector(
                            'meta[name="csrf-token"]'
                        ).content,
                    },
                })
                    .then((response) => {
                        if (!response.ok) {
                            return response.json().then((err) => {
                                throw new Error(
                                    `HTTP error! status: ${
                                        response.status
                                    }, message: ${
                                        err.message || response.statusText
                                    }`
                                );
                            });
                        }
                        return response.json();
                    })
                    .then((data) => {
                        if (data.success) {
                            Swal.fire("Terhapus!", data.message, "success");
                        } else {
                            Swal.fire("Gagal!", data.message, "error");
                        }

                        // Reset to page 1 after deleting an item
                        fetchOutStocks(
                            siteIdSelect.value,
                            startDateInput.value,
                            endDateInput.value,
                            1
                        );
                    })
                    .catch((error) => {
                        Swal.fire(
                            "Kesalahan!",
                            error.message ||
                                "Terjadi kesalahan saat menghapus.",
                            "error"
                        );
                    });
            }
        });
    }

    function addOutStock() {
        Swal.fire({
            title: "Apakah Anda yakin ?",
            text: "Data tidak dapat di ubah!",
            icon: "question",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: "Lanjutkan !",
        }).then((result) => {
            if (result.isConfirmed) {
                const formData = new FormData();
                formData.append('part_inventory_id', idPartinventoryInput.value);
                formData.append('date_out', dateOutInput.value);
                formData.append('destination', destinationSelect.value);
                formData.append('quantity', quantityInput.value);
                formData.append('note', noteInput.value);

                // Append file if it exists
                if (suratJalanInput.files[0]) {
                    formData.append('surat_jalan', suratJalanInput.files[0]);
                }

                fetch("/out-parts", {
                    method: "POST",
                    headers: {
                        "X-CSRF-TOKEN": document.querySelector(
                            'meta[name="csrf-token"]'
                        ).content,
                    },
                    body: formData, // Use FormData instead of JSON
                })
                .then((response) => response.json())
                .then((data) => {
                    if (data.success) {
                        Swal.fire("Ditambahkan!", data.message, "success");
                        // Reset form
                        partInventoryAutocomplete.value = "";
                        idPartinventoryInput.value = "";
                        destinationSelect.value = "1";
                        dateOutInput.value = "";
                        quantityInput.value = "";
                        noteInput.value = "";
                        suratJalanInput.value = ""; // Clear file input
                    } else {
                        Swal.fire("Gagal!!", data.message, "error");
                    }

                    // Reset to page 1 after adding a new item
                    fetchOutStocks(
                        siteIdSelect.value,
                        startDateInput.value,
                        endDateInput.value,
                        1
                    );
                })
                .catch((error) => {
                    Swal.fire(
                        "Kesalahan!",
                        error.message ||
                            "Maaf terjadi masalah, silahkan ulangi lagi.",
                        "error"
                    );
                });
            }
        });
    }

    siteIdSelect.addEventListener("change", function () {
        const selectedSiteId = this.value;
        const startDate = startDateInput.value;
        const endDate = endDateInput.value;
        // Reset to page 1 when filter changes
        fetchOutStocks(selectedSiteId, startDate, endDate, 1);
    });

    addButton.addEventListener("click", function (e) {
        e.preventDefault();
        // Validate quantity input: allow only decimal with dot, not comma
        const quantityValue = quantityInput.value.trim();
        // Regex: only allow numbers like 12, 12.5, 0.1, but not 12,5 or 12..5 or 12, etc
        const validDecimalRegex = /^\d+(\.\d+)?$/;
        if (quantityValue.includes(",")) {
            Swal.fire({
                icon: "error",
                title: "Format Salah!",
                text: "Gunakan titik (.) untuk desimal, bukan koma. Contoh: 12.5",
            });
            quantityInput.focus();
            return;
        }
        if (!validDecimalRegex.test(quantityValue) || parseFloat(quantityValue) <= 0) {
            Swal.fire({
                icon: "error",
                title: "Input Tidak Valid!",
                text: "Masukkan jumlah desimal yang benar, contoh: 12.5",
            });
            quantityInput.focus();
            return;
        }
        addOutStock();
    });

    startDateInput.addEventListener("change", function () {
        const selectedSiteId = siteIdSelect.value;
        const startDate = startDateInput.value;
        const endDate = endDateInput.value;
        // Reset to page 1 when filter changes
        fetchOutStocks(selectedSiteId, startDate, endDate, 1);
    });

    endDateInput.addEventListener("change", function () {
        const selectedSiteId = siteIdSelect.value;
        const startDate = startDateInput.value;
        const endDate = endDateInput.value;
        // Reset to page 1 when filter changes
        fetchOutStocks(selectedSiteId, startDate, endDate, 1);
    });

    // Initial load with page 1
    fetchOutStocks(
        siteIdSelect.value,
        startDateInput.value,
        endDateInput.value,
        1
    );

    function populatePartInventories() {
        fetch("/out-parts/part-inventories", {
            headers: {
                "X-CSRF-TOKEN": document.querySelector(
                    'meta[name="csrf-token"]'
                ).content,
            },
        })
            .then((response) => {
                if (!response.ok) {
                    return response.json().then((err) => {
                        throw new Error(
                            `HTTP error! status: ${response.status}, message: ${
                                err.message || response.statusText
                            }`
                        );
                    });
                }
                return response.json();
            })
            .then((data) => {
                let html = '<option value="">Select Part</option>';
                data.forEach((partInventory) => {
                    html += `
                        <option value="${partInventory.id_partinventory}">${partInventory.part.part_name} (${partInventory.part.code_part})</option>
                    `;
                });
                idPartInventorySelect.innerHTML = html;
            })
            .catch((error) => {
                console.error("Error fetching part inventories:", error);
                Swal.fire(
                    "Kesalahan",
                    error.message ||
                        "Gagal mengambil inventaris suku cadang. Silakan coba lagi.",
                    "error"
                );
            });
    }
});
