<?php

namespace App\Http\Controllers;

use App\Models\Site;
use App\Models\SiteInStock;
use App\Models\SiteOutStock;
use App\Models\PartInventory;
use App\Models\Backlog;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DashboardController extends Controller
{

    public function AdminHO(Request $request)
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'group_by' => 'nullable|in:day,week,month'
        ]);

        $groupBy = $request->input('group_by', 'day');
        $today = Carbon::now();
        $defaultEndDate = $today->toDateString();

        if ($today->day === 1) {
            $defaultStartDate = $today->subMonth()->startOfMonth()->toDateString();
        } else {
            $defaultStartDate = $today->startOfMonth()->toDateString();
        }

        $startDate = $request->input('start_date', $defaultStartDate);
        $endDate = $request->input('end_date', $defaultEndDate);

        $sites = Site::all();
        $periods = [];
        $currentDate = Carbon::parse($startDate);
        $endDateObj = Carbon::parse($endDate);

        while ($currentDate <= $endDateObj) {
            switch ($groupBy) {
                case 'day':
                    $periodStart = $currentDate->copy()->startOfDay();
                    $periodEnd = $currentDate->copy()->endOfDay();
                    $label = $currentDate->format('d M Y');
                    $currentDate->addDay();
                    break;

                case 'week':
                    $periodStart = $currentDate->copy()->startOfWeek();
                    $periodEnd = $currentDate->copy()->endOfWeek()->min($endDateObj);
                    $label = $periodStart->format('d M') . ' - ' . $periodEnd->format('d M');
                    $currentDate = $periodEnd->copy()->addDay();
                    break;

                case 'month':
                    $periodStart = $currentDate->copy()->startOfMonth();
                    $periodEnd = $currentDate->copy()->endOfMonth()->min($endDateObj);
                    $label = $periodStart->format('M Y');
                    $currentDate = $periodEnd->copy()->addDay();
                    break;
            }

            $periods[] = [
                'start' => $periodStart,
                'end' => $periodEnd,
                'label' => $label
            ];
        }

        $inStockChartData = [];
        $outStockChartData = [];

        foreach ($sites as $site) {
            $inStockData = [];
            $outStockData = [];

            foreach ($periods as $period) {
                $inStock = SiteInStock::join('part_inventories', 'site_in_stocks.part_inventory_id', '=', 'part_inventories.part_inventory_id')
                    ->where('part_inventories.site_id', $site->site_id)
                    ->whereBetween('date_in', [$period['start'], $period['end']])
                    ->sum('site_in_stocks.quantity');

                $outStock = SiteOutStock::join('part_inventories', 'site_out_stocks.part_inventory_id', '=', 'part_inventories.part_inventory_id')
                    ->where('part_inventories.site_id', $site->site_id)
                    ->whereBetween('date_out', [$period['start'], $period['end']])
                    ->sum('site_out_stocks.quantity');

                $inStockData[] = $inStock;
                $outStockData[] = $outStock;
            }

            $inStockChartData[] = [
                'site_name' => $site->site_name,
                'labels' => array_column($periods, 'label'),
                'data' => $inStockData
            ];

            $outStockChartData[] = [
                'site_name' => $site->site_name,
                'labels' => array_column($periods, 'label'),
                'data' => $outStockData
            ];
        }

        // Inventory status for pie/bar
        $inventoryData = $this->getInventoryStatusData();

        // Get combined parts need data from backlog and upcoming services
        $partsneed = $this->getCombinedPartsNeed();

        return view('warehouse.dashboard', compact(
            'inStockChartData',
            'outStockChartData',
            'partsneed',
            'startDate',
            'endDate',
            'groupBy',
            'inventoryData'
        ));
    }

    private function getInventoryStatusData()
    {
        $sites = Site::all();
        $inventoryData = [];

        foreach ($sites as $site) {
            $inventories = PartInventory::where('site_id', $site->site_id)->get();

            // Filter inventories to only include parts with min_stock != 0 for status monitoring
            $monitoredInventories = $inventories->filter(function ($inventory) {
                return $inventory->min_stock != 0;
            });

            $totalMonitoredInventories = $monitoredInventories->count();
            $notReadyCount = 0;
            $mediumCount = 0;
            $readyCount = 0;
            $notReadyParts = [];
            $mediumParts = [];

            foreach ($monitoredInventories as $inventory) {
                $averageStock = ($inventory->min_stock + $inventory->max_stock) / 2;

                // Updated Stock Status Logic
                // Part Not Ready: stock < average AND (min_stock != 0 OR max_stock != 0)
                if ($inventory->stock_quantity < $averageStock && $inventory->min_stock > 0 && $inventory->max_stock > 0) {
                    $status = 'danger';
                    $notReadyCount++;
                    $notReadyParts[] = [
                        'part_name' => $inventory->part->part_name,
                        'min_stock' => $inventory->min_stock,
                        'max_stock' => $inventory->max_stock,
                        'stock_quantity' => $inventory->stock_quantity,
                        'status' => $status,
                    ];
                }
                // Part Medium/Yellow: stock >= average AND stock <= max_stock
                elseif ($inventory->stock_quantity >= $averageStock && $inventory->stock_quantity <= $inventory->max_stock) {
                    $status = 'warning';
                    $mediumCount++;
                    $mediumParts[] = [
                        'part_name' => $inventory->part->part_name,
                        'min_stock' => $inventory->min_stock,
                        'max_stock' => $inventory->max_stock,
                        'stock_quantity' => $inventory->stock_quantity,
                        'status' => $status,
                    ];
                }
                if ($inventory->stock_quantity >= $inventory->max_stock && $inventory->min_stock > 0 && $inventory->max_stock > 0) {
                    $readyCount++;
                } else {
                    $status = 'ok'; // Status aman
                }
            }

            // Percentage calculations based only on monitored inventories
            $totalMonitoredInventories = $notReadyCount + $readyCount;
            $readyPercentage = round(($readyCount / $totalMonitoredInventories) * 100, 0);
            $notReadyPercentage = round(($notReadyCount / $totalMonitoredInventories) * 100, 0);

            $inventoryData[] = [
                'site_name' => $site->site_name,
                'ready_percentage' => round($readyPercentage, 0),
                'not_ready_percentage' => round($notReadyPercentage, 0),
                'not_ready_count' => $notReadyCount,
                'medium_count' => $mediumCount,
                'not_ready_parts' => $notReadyParts,
                'medium_parts' => $mediumParts,
                'total_monitored' => $totalMonitoredInventories, // Add for debugging/reference
            ];
        }

        return $inventoryData;
    }

    public function index(Request $request)
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'group_by' => 'nullable|in:day,week,month'
        ]);

        $groupBy = $request->input('group_by', 'week');
        $defaultStartDate = Carbon::create(2024, 2, 1)->toDateString();
        $defaultEndDate = Carbon::create(2024, 2, 1)->endOfMonth()->toDateString();

        $startDate = $request->input('start_date', $defaultStartDate);
        $endDate = $request->input('end_date', $defaultEndDate);

        $sites = Site::all();
        $periods = [];
        $currentDate = Carbon::parse($startDate);
        $endDateObj = Carbon::parse($endDate);

        // Generate periods array
        while ($currentDate <= $endDateObj) {
            switch ($groupBy) {
                case 'day':
                    $periodStart = $currentDate->copy()->startOfDay();
                    $periodEnd = $currentDate->copy()->endOfDay();
                    $label = $currentDate->format('d M Y');
                    $currentDate->addDay();
                    break;

                case 'week':
                    $periodStart = $currentDate->copy()->startOfWeek();
                    $periodEnd = $currentDate->copy()->endOfWeek();
                    $periodEnd = $periodEnd->min($endDateObj);
                    $label = $periodStart->format('d M') . ' - ' . $periodEnd->format('d M');
                    $currentDate = $periodEnd->copy()->addDay();
                    break;
                case 'month':
                    $periodStart = $currentDate->copy()->startOfMonth();
                    $periodEnd = $currentDate->copy()->endOfMonth();
                    $periodEnd = $periodEnd->min($endDateObj);
                    $label = $periodStart->format('M Y');
                    $currentDate = $periodEnd->copy()->addDay();
                    break;
            }

            $periods[] = [
                'start' => $periodStart,
                'end' => $periodEnd,
                'label' => $label
            ];
        }

        $inStockChartData = [];
        $outStockChartData = [];

        foreach ($sites as $site) {
            $inStockData = [];
            $outStockData = [];

            foreach ($periods as $period) {
                // In Stock Calculation
                $inStock = SiteInStock::join('part_inventories', 'site_in_stocks.part_inventory_id', '=', 'part_inventories.part_inventory_id')
                    ->where('part_inventories.site_id', $site->site_id)
                    ->whereBetween('date_in', [$period['start'], $period['end']])
                    ->sum('site_in_stocks.quantity');

                // Out Stock Calculation
                $outStock = SiteOutStock::join('part_inventories', 'site_out_stocks.part_inventory_id', '=', 'part_inventories.part_inventory_id')
                    ->where('part_inventories.site_id', $site->site_id)
                    ->whereBetween('date_out', [$period['start'], $period['end']])
                    ->sum('site_out_stocks.quantity');

                $inStockData[] = $inStock;
                $outStockData[] = $outStock;
            }

            $inStockChartData[] = [
                'site_name' => $site->site_name,
                'labels' => array_column($periods, 'label'),
                'data' => $inStockData
            ];

            $outStockChartData[] = [
                'site_name' => $site->site_name,
                'labels' => array_column($periods, 'label'),
                'data' => $outStockData
            ];
        }

        return view('welcome', compact(
            'inStockChartData',
            'outStockChartData',
            'startDate',
            'endDate',
            'groupBy'
        ));
    }

    public function adminsite(Request $request)
    {
        return view('sites.content');
    }

    /**
     * Get combined parts need data from backlog and upcoming services
     */
    private function getCombinedPartsNeed()
    {
        $combinedParts = collect();

        // 1. Get backlog parts (plan_pull_date within next 10 days)
        $backlogParts = $this->getBacklogParts();
        $combinedParts = $combinedParts->merge($backlogParts);

        // 2. Get upcoming service parts (2000 HM and 4000 HM services within 10 days)
        $serviceParts = $this->getUpcomingServiceParts();
        $combinedParts = $combinedParts->merge($serviceParts);

        return $combinedParts->take(100);
    }

    /**
     * Get parts from backlogs with plan_pull_date within the next 10 days
     */
    private function getBacklogParts()
    {
        $today = Carbon::now()->format('Y-m-d');
        $tenDaysFromNow = Carbon::now()->addDays(10)->format('Y-m-d');

        $backlogParts = Backlog::with(['unit', 'backlogParts.part'])
            ->whereDate('plan_pull_date', '>=', $today)
            ->whereDate('plan_pull_date', '<=', $tenDaysFromNow)
            ->where('status', 'OPEN')
            ->get()
            ->flatMap(function ($backlog) {
                return $backlog->backlogParts->map(function ($backlogPart) use ($backlog) {
                    // Get stock quantity from site inventory
                    $partInventory = PartInventory::where('part_code', $backlogPart->part_code)
                        ->where('site_id', $backlog->unit->site_id)
                        ->first();

                    return (object) [
                        'part_name' => $backlogPart->part->part_name,
                        'site_id' => $backlog->unit->site_id,
                        'unit_code' => $backlog->unit_code,
                        'total_quantityneed' => $backlogPart->quantity,
                        'stock_quantity' => $partInventory ? $partInventory->stock_quantity : 0,
                        'estimated_next_service_date' => $backlog->plan_pull_date ? $backlog->plan_pull_date->format('Y-m-d') : 'N/A',
                        'unit_type' => $backlog->unit->unit_type ?? 'N/A',
                        'source' => 'backlog'
                    ];
                });
            });

        return $backlogParts;
    }

    /**
     * Get parts for units approaching 2000 HM or 4000 HM services within 10 days
     */
    private function getUpcomingServiceParts()
    {
        $serviceParts = collect();
        $tenDaysFromNow = Carbon::now()->addDays(10)->format('Y-m-d');

        // Default parts for 2000 HM and 4000 HM services
        $defaultParts = [
            'RD-A1180-PWB' => 'Receiver Dryer HD 78',
            'EV-564587-PWB' => 'Replace Expansion Valve',
            'OC-SP10-250C-PWB' => 'Oil Compressor SP10'
        ];

        try {
            // Get units with upcoming 2000 HM or 4000 HM services
            $unitsWithUpcomingService = DB::table('daily_reports as dly')
                ->join('units as u', 'u.id', '=', 'dly.unit_id')
                ->join(DB::raw('(SELECT unit_id, MAX(date_in) as latest_date FROM daily_reports GROUP BY unit_id) as latest'), function ($join) {
                    $join->on('latest.unit_id', '=', 'dly.unit_id')
                        ->on('latest.latest_date', '=', 'dly.date_in');
                })
                ->select(
                    'u.unit_code',
                    'u.unit_type',
                    'u.site_id',
                    'dly.hm',
                    'dly.date_in',
                    DB::raw('CEIL(dly.hm / 250) * 250 as next_hm'),
                    DB::raw('ROUND((CEIL(dly.hm / 250) * 250 - dly.hm) / 20) as days_to_next'),
                    DB::raw('DATE_ADD(dly.date_in, INTERVAL ROUND((CEIL(dly.hm / 250) * 250 - dly.hm) / 20) DAY) as estimated_next_service_date')
                )
                ->whereRaw("DATE_ADD(dly.date_in, INTERVAL ROUND((CEIL(dly.hm / 250) * 250 - dly.hm) / 20) DAY) <= ?", [$tenDaysFromNow])
                ->whereRaw("(CEIL(dly.hm / 250) * 250) IN (2000, 4000)")
                ->whereNotNull('dly.hm')
                ->get();

            foreach ($unitsWithUpcomingService as $unit) {
                foreach ($defaultParts as $partCode => $partName) {
                    // Get stock quantity from site inventory
                    $partInventory = PartInventory::where('part_code', $partCode)
                        ->where('site_id', $unit->site_id)
                        ->first();

                    $serviceParts->push((object) [
                        'part_name' => $partName,
                        'site_id' => $unit->site_id,
                        'unit_code' => $unit->unit_code,
                        'total_quantityneed' => 1,
                        'stock_quantity' => $partInventory ? $partInventory->stock_quantity : 0,
                        'estimated_next_service_date' => $unit->estimated_next_service_date ?? 'N/A',
                        'unit_type' => $unit->unit_type ?? 'N/A',
                        'source' => 'service'
                    ]);
                }
            }
        } catch (\Exception $e) {
            // Log error but don't break the dashboard
            Log::error('Error getting upcoming service parts: ' . $e->getMessage());
        }

        return $serviceParts;
    }
}
