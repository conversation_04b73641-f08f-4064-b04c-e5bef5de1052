<?php

namespace App\Http\Controllers;

use App\Models\DailyReport;
use App\Models\Job;
use App\Models\Technician;
use App\Models\Unit;
use App\Models\DailyReportImage;
use App\Models\PartProblem;
use App\Models\PartInventory;
use App\Models\LogAktivitas;
use App\Models\Backlog;
use App\Models\BacklogPart;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Barryvdh\DomPDF\Facade\Pdf;
use DateTime;
use Illuminate\Support\Carbon;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class DailyReportController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('daily-reports.index');
    }
    /**
     * Get daily reports data for AJAX request.
     */
    public function getData(Request $request)
    {
        $query = DailyReport::with(['unit', 'jobs', 'technicians', 'images']);

        // Apply search filter if provided
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('problem', 'like', "%{$search}%")
                    ->orWhere('problem_component', 'like', "%{$search}%")
                    ->orWhere('problem_description', 'like', "%{$search}%")
                    ->orWhereHas('unit', function ($q) use ($search) {
                        $q->where('unit_code', 'like', "%{$search}%")
                            ->orWhere('unit_type', 'like', "%{$search}%");
                    });
            });
        }

        // Apply date filter if provided
        if ($request->has('start_date') && $request->has('end_date')) {
            $startDate = $request->start_date;
            $endDate = $request->end_date;
            if (!empty($startDate) && !empty($endDate)) {
                $query->whereBetween('date_in', [$startDate, $endDate]);
            }
        }



        // Apply sorting
        $sortField = $request->get('sort_field', 'date_in');
        $sortDirection = $request->get('sort_direction', 'desc');

        // Map frontend sort fields to database columns
        $sortMapping = [
            'date' => 'date_in',
            'unit' => 'units.unit_code',
            'hm' => 'hm',
            'problem' => 'problem',
            'problem_component' => 'problem_component',
            'problem_description' => 'problem_description',
            'start' => 'hour_in',
            'finish' => 'hour_out',
            'shift' => 'shift'
        ];

        if (isset($sortMapping[$sortField])) {
            if ($sortField === 'unit') {
                $query->leftJoin('units', 'daily_reports.unit_id', '=', 'units.id')
                    ->orderBy('units.unit_code', $sortDirection)
                    ->select('daily_reports.*');
            } else {
                $query->orderBy($sortMapping[$sortField], $sortDirection);
            }
        } else {
            $query->orderBy('date_in', 'desc');
        }

        // Paginate the results
        $perPage = $request->input('per_page', 10);
        $dailyReports = $query->paginate($perPage);

        // Preserve query parameters in pagination links
        $dailyReports->appends($request->only(['search', 'start_date', 'end_date', 'sort_field', 'sort_direction']));

        return response()->json([
            'data' => $dailyReports->items(),
            'current_page' => $dailyReports->currentPage(),
            'last_page' => $dailyReports->lastPage(),
            'per_page' => $dailyReports->perPage(),
            'total' => $dailyReports->total(),
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'unit_id' => 'required|exists:units,id',
            'hm' => 'nullable|numeric|min:0',
            'problem' => 'nullable|string|max:255',
            'problem_component' => 'nullable|string|max:255',
            'problem_description' => 'nullable|string',
            'date_in' => 'required|date',
            'hour_in' => 'required|string',
            'hour_out' => 'required|string',
            'shift' => 'required|in:DAY,NIGHT',
            'jobs' => 'required|array|min:1',
            'jobs.*.description' => 'required|string|max:255',
            'jobs.*.highlight' => 'boolean',
            'technicians' => 'required|array|min:1',
            'technicians.*.name' => 'required|string|max:255',
            'before_images' => 'nullable|array',
            // Backlog validation (completely optional)
            'create_backlog' => 'nullable|boolean',
            'before_images.*' => 'image|mimes:jpeg,png,jpg,gif|max:5120',
            'after_images' => 'nullable|array',
            'after_images.*' => 'image|mimes:jpeg,png,jpg,gif|max:5120',
            'unit_images' => 'nullable|array',
            'unit_images.*' => 'image|mimes:jpeg,png,jpg,gif|max:5120',
            'plan_fix' => 'nullable|string',
            'plan_rekomen' => 'nullable|string',
            'lifetime_component' => 'nullable|string|max:255',
            'part_problems' => 'nullable|array',
            'part_problems.*.code_part' => 'required_with:part_problems|string|max:255',
            'part_problems.*.part_name' => 'required_with:part_problems|string|max:255',
            'part_problems.*.quantity' => 'required_with:part_problems|numeric|min:0',
            'part_problems.*.eum' => 'required_with:part_problems|string|max:10',
            'part_problems.*.remarks' => 'nullable|string',
        ], [
            'unit_id.required' => 'Unit harus dipilih',
            'unit_id.exists' => 'Unit yang dipilih tidak valid',
            'hm.numeric' => 'HM harus berupa angka',
            'hm.min' => 'HM tidak boleh negatif',
            'date_in.required' => 'Tanggal masuk harus diisi',
            'date_in.date' => 'Format tanggal masuk tidak valid',
            'hour_in.required' => 'Jam masuk harus diisi',
            'hour_in.date_format' => 'Format jam masuk harus HH:MM',
            'hour_out.required' => 'Jam keluar harus diisi',
            'hour_out.date_format' => 'Format jam keluar harus HH:MM',
            'shift.required' => 'Shift harus dipilih',
            'shift.in' => 'Shift harus DAY atau NIGHT',
            'jobs.required' => 'Minimal satu pekerjaan harus ditambahkan',
            'jobs.min' => 'Minimal satu pekerjaan harus ditambahkan',
            'jobs.*.description.required' => 'Deskripsi pekerjaan harus diisi',
            'jobs.*.description.max' => 'Deskripsi pekerjaan maksimal 255 karakter',
            'technicians.required' => 'Minimal satu teknisi harus ditambahkan',
            'technicians.min' => 'Minimal satu teknisi harus ditambahkan',
            'technicians.*.name.required' => 'Nama teknisi harus diisi',
            'technicians.*.name.max' => 'Nama teknisi maksimal 255 karakter',
            'before_images.*.image' => 'File harus berupa gambar',
            'before_images.*.mimes' => 'Format gambar harus jpeg, png, jpg, atau gif',
            'before_images.*.max' => 'Ukuran gambar maksimal 5MB',
            'after_images.*.image' => 'File harus berupa gambar',
            'after_images.*.mimes' => 'Format gambar harus jpeg, png, jpg, atau gif',
            'after_images.*.max' => 'Ukuran gambar maksimal 5MB',
            'unit_images.*.image' => 'File harus berupa gambar',
            'unit_images.*.mimes' => 'Format gambar harus jpeg, png, jpg, atau gif',
            'unit_images.*.max' => 'Ukuran gambar maksimal 5MB',
            'part_problems.*.code_part.required_with' => 'Kode part harus diisi',
            'part_problems.*.part_name.required_with' => 'Nama part harus diisi',
            'part_problems.*.quantity.required_with' => 'Quantity harus diisi',
            'part_problems.*.quantity.numeric' => 'Quantity harus berupa angka',
            'part_problems.*.quantity.min' => 'Quantity tidak boleh negatif',
            'part_problems.*.eum.required_with' => 'Satuan harus diisi',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Validate backlog separately if create_backlog is true and backlog data exists
        if ($request->create_backlog && $request->has('backlog') && !empty($request->backlog)) {
            // Check if backlog has meaningful data
            $backlogData = $request->backlog;
            $hasBacklogData = !empty($backlogData['unit_code']) ||
                             !empty($backlogData['problem_description']) ||
                             !empty($backlogData['backlog_job']);

            if ($hasBacklogData) {
                $backlogValidator = Validator::make($backlogData, [
                    'unit_code' => 'required|string|exists:units,unit_code',
                    'hm_found' => 'nullable|numeric|min:0',
                    'problem_description' => 'required|string|max:255',
                    'backlog_job' => 'required|string|max:255',
                    'plan_hm' => 'nullable|numeric|min:0',
                    'status' => 'required|in:OPEN,CLOSED',
                    'plan_pull_date' => 'nullable|date',
                    'notes' => 'nullable|string',
                    'parts' => 'required|array|min:1',
                    'parts.*.part_code' => 'required|string|exists:parts,part_code',
                    'parts.*.quantity' => 'required|integer|min:1',
                ]);

                if ($backlogValidator->fails()) {
                    return response()->json(['errors' => $backlogValidator->errors()], 422);
                }
            }
        }

        DB::beginTransaction();
        try {
            // Create new daily report record
            $dailyReport = DailyReport::create([
                'unit_id' => $request->unit_id,
                'hm' => $request->hm,
                'problem' => $request->problem,
                'problem_component' => $request->problem_component,
                'problem_description' => $request->problem_description,
                'date_in' => $request->date_in,
                'hour_in' => $request->hour_in,
                'hour_out' => $request->hour_out,
                'shift' => $request->shift,
                'plan_fix' => $request->plan_fix,
                'plan_rekomen' => $request->plan_rekomen,
                'lifetime_component' => $request->lifetime_component,
            ]);

            // Create and attach jobs
            $jobIds = [];
            foreach ($request->jobs as $jobData) {
                // Check if job already exists
                $job = Job::where('job_description', $jobData['description'])->first();

                if (!$job) {
                    // Create new job
                    $job = Job::create([
                        'job_description' => $jobData['description'],
                        'highlight' => isset($jobData['highlight']) && $jobData['highlight'] == '1',
                    ]);
                }

                $jobIds[] = $job->job_description_id;
            }
            $dailyReport->jobs()->attach($jobIds);

            // Create and attach technicians
            $technicianIds = [];
            foreach ($request->technicians as $technicianData) {
                // Check if technician already exists
                $technician = Technician::where('name', $technicianData['name'])->first();

                if (!$technician) {
                    // Create new technician
                    $technician = Technician::create([
                        'name' => $technicianData['name'],
                    ]);
                }

                $technicianIds[] = $technician->technician_id;
            }
            $dailyReport->technicians()->attach($technicianIds);

            // Create part problems if provided
            if ($request->has('part_problems') && is_array($request->part_problems)) {
                foreach ($request->part_problems as $partProblemData) {
                    if (!empty($partProblemData['code_part']) && !empty($partProblemData['part_name'])) {
                        PartProblem::create([
                            'daily_report_id' => $dailyReport->daily_report_id,
                            'code_part' => $partProblemData['code_part'],
                            'part_name' => $partProblemData['part_name'],
                            'quantity' => $partProblemData['quantity'],
                            'eum' => $partProblemData['eum'],
                            'remarks' => $partProblemData['remarks'] ?? null,
                        ]);
                    }
                }
            }

            // Handle before images upload
            if ($request->hasFile('before_images')) {
                foreach ($request->file('before_images') as $image) {
                    $fileName = time() . '_before_' . uniqid() . '.' . $image->getClientOriginalExtension();
                    $image->move(public_path('assets/daily_reports'), $fileName);

                    DailyReportImage::create([
                        'daily_report_id' => $dailyReport->daily_report_id,
                        'image_path' => $fileName,
                        'type' => 'before',
                    ]);
                }
            }

            // Handle after images upload
            if ($request->hasFile('after_images')) {
                foreach ($request->file('after_images') as $image) {
                    $fileName = time() . '_after_' . uniqid() . '.' . $image->getClientOriginalExtension();
                    $image->move(public_path('assets/daily_reports'), $fileName);

                    DailyReportImage::create([
                        'daily_report_id' => $dailyReport->daily_report_id,
                        'image_path' => $fileName,
                        'type' => 'after',
                    ]);
                }
            }

            // Handle unit images upload
            if ($request->hasFile('unit_images')) {
                foreach ($request->file('unit_images') as $image) {
                    $fileName = time() . '_unit_' . uniqid() . '.' . $image->getClientOriginalExtension();
                    $image->move(public_path('assets/daily_reports'), $fileName);

                    DailyReportImage::create([
                        'daily_report_id' => $dailyReport->daily_report_id,
                        'image_path' => $fileName,
                        'type' => 'unit',
                    ]);
                }
            }

            // Create backlog if requested and has meaningful data
            if ($request->create_backlog && $request->has('backlog') && !empty($request->backlog)) {
                $backlogData = $request->backlog;

                // Check if backlog has meaningful data
                $hasBacklogData = !empty($backlogData['unit_code']) ||
                                 !empty($backlogData['problem_description']) ||
                                 !empty($backlogData['backlog_job']);

                if ($hasBacklogData) {
                    // Use unit_code from backlog form, or fallback to daily report unit if empty
                    $unitCode = $backlogData['unit_code'];
                    if (empty($unitCode)) {
                        $unit = Unit::find($request->unit_id);
                        $unitCode = $unit ? $unit->unit_code : null;
                    }

                    // Use HM from backlog form, or fallback to daily report HM if empty
                    $hmFound = $backlogData['hm_found'] ?? $request->hm;

                    $backlog = Backlog::create([
                        'unit_code' => $unitCode,
                        'hm_found' => $hmFound,
                        'problem_description' => $backlogData['problem_description'],
                        'backlog_job' => $backlogData['backlog_job'],
                        'plan_hm' => $backlogData['plan_hm'] ?? null,
                        'status' => $backlogData['status'],
                        'plan_pull_date' => $backlogData['plan_pull_date'] ?? null,
                        'notes' => $backlogData['notes'] ?? null,
                    ]);

                    // Add parts to backlog
                    if (isset($backlogData['parts'])) {
                        foreach ($backlogData['parts'] as $partData) {
                            BacklogPart::create([
                                'backlog_id' => $backlog->id,
                                'part_code' => $partData['part_code'],
                                'quantity' => $partData['quantity'],
                            ]);
                        }
                    }
                }
            }

            // Log the activity
            LogAktivitas::create([
                'site_id' => session('site_id'),
                'name' => session('name'),
                'action' => 'Membuat Daily Report',
                'description' => 'User ' . session('name') . ' membuat Daily Report baru dengan ID ' . $dailyReport->daily_report_id,
                'table' => 'daily_reports',
                'ip_address' => request()->ip(),
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Daily Report berhasil dibuat',
                'data' => $dailyReport->load(['unit', 'jobs', 'technicians', 'images'])
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error creating daily report: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Calculate DT time in minutes if both hour_in and hour_out are in time format
     */
    private function calculateDTTime($hourIn, $hourOut)
    {
        // Check if both inputs are in time format (HH:MM)
        if (preg_match('/^\d{1,2}:\d{2}$/', $hourIn) && preg_match('/^\d{1,2}:\d{2}$/', $hourOut)) {
            try {
                $startTime = new DateTime($hourIn);
                $endTime = new DateTime($hourOut);
                $interval = $startTime->diff($endTime);
                $minutes = ($interval->h * 60) + $interval->i;

                // Handle overnight shifts
                if ($minutes < 0) {
                    $minutes += 24 * 60;
                }

                return $minutes;
            } catch (\Exception $e) {
                return 0; // Default to 0 if parsing fails
            }
        }

        return 0; // Default to 0 if not in time format
    }

    public function show($id)
    {
        $dailyReport = DailyReport::with(['unit', 'jobs', 'technicians', 'images', 'partProblems'])
            ->findOrFail($id);
        return response()->json($dailyReport);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        $dailyReport = DailyReport::findOrFail($id);
        $validator = Validator::make($request->all(), [
            'unit_id' => 'required|exists:units,id',
            'hm' => 'nullable|numeric|min:0',
            'problem' => 'nullable|string|max:255',
            'problem_component' => 'nullable|string|max:255',
            'problem_description' => 'nullable|string',
            'date_in' => 'required|date',
            'hour_in' => 'required|string',
            'hour_out' => 'required|string',
            'shift' => 'required|in:DAY,NIGHT',
            'jobs' => 'required|array|min:1',
            'jobs.*.description' => 'required|string|max:255',
            'jobs.*.highlight' => 'boolean',
            'technicians' => 'required|array|min:1',
            'technicians.*.name' => 'required|string|max:255',
            'before_images' => 'nullable|array',
            'before_images.*' => 'image|mimes:jpeg,png,jpg,gif|max:5120',
            'after_images' => 'nullable|array',
            'after_images.*' => 'image|mimes:jpeg,png,jpg,gif|max:5120',
            'unit_images' => 'nullable|array',
            'unit_images.*' => 'image|mimes:jpeg,png,jpg,gif|max:5120',
            'plan_fix' => 'nullable|string',
            'plan_rekomen' => 'nullable|string',
            'lifetime_component' => 'nullable|string|max:255',
            'part_problems' => 'nullable|array',
            'part_problems.*.code_part' => 'required_with:part_problems|string|max:255',
            'part_problems.*.part_name' => 'required_with:part_problems|string|max:255',
            'part_problems.*.quantity' => 'required_with:part_problems|numeric|min:0',
            'part_problems.*.eum' => 'required_with:part_problems|string|max:10',
            'part_problems.*.remarks' => 'nullable|string',
        ], [
            'unit_id.required' => 'Unit harus dipilih',
            'unit_id.exists' => 'Unit yang dipilih tidak valid',
            'hm.numeric' => 'HM harus berupa angka',
            'hm.min' => 'HM tidak boleh negatif',
            'date_in.required' => 'Tanggal masuk harus diisi',
            'date_in.date' => 'Format tanggal masuk tidak valid',
            'hour_in.required' => 'Jam masuk harus diisi',
            'hour_in.date_format' => 'Format jam masuk harus HH:MM',
            'hour_out.required' => 'Jam keluar harus diisi',
            'hour_out.date_format' => 'Format jam keluar harus HH:MM',
            'shift.required' => 'Shift harus dipilih',
            'shift.in' => 'Shift harus DAY atau NIGHT',
            'jobs.required' => 'Minimal satu pekerjaan harus ditambahkan',
            'jobs.min' => 'Minimal satu pekerjaan harus ditambahkan',
            'jobs.*.description.required' => 'Deskripsi pekerjaan harus diisi',
            'jobs.*.description.max' => 'Deskripsi pekerjaan maksimal 255 karakter',
            'technicians.required' => 'Minimal satu teknisi harus ditambahkan',
            'technicians.min' => 'Minimal satu teknisi harus ditambahkan',
            'technicians.*.name.required' => 'Nama teknisi harus diisi',
            'technicians.*.name.max' => 'Nama teknisi maksimal 255 karakter',
            'before_images.*.image' => 'File harus berupa gambar',
            'before_images.*.mimes' => 'Format gambar harus jpeg, png, jpg, atau gif',
            'before_images.*.max' => 'Ukuran gambar maksimal 5MB',
            'after_images.*.image' => 'File harus berupa gambar',
            'after_images.*.mimes' => 'Format gambar harus jpeg, png, jpg, atau gif',
            'after_images.*.max' => 'Ukuran gambar maksimal 5MB',
            'unit_images.*.image' => 'File harus berupa gambar',
            'unit_images.*.mimes' => 'Format gambar harus jpeg, png, jpg, atau gif',
            'unit_images.*.max' => 'Ukuran gambar maksimal 5MB',
            'part_problems.*.code_part.required_with' => 'Kode part harus diisi',
            'part_problems.*.part_name.required_with' => 'Nama part harus diisi',
            'part_problems.*.quantity.required_with' => 'Quantity harus diisi',
            'part_problems.*.quantity.numeric' => 'Quantity harus berupa angka',
            'part_problems.*.quantity.min' => 'Quantity tidak boleh negatif',
            'part_problems.*.eum.required_with' => 'Satuan harus diisi',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        DB::beginTransaction();
        try {
            // Update daily report record
            $dailyReport->update([
                'unit_id' => $request->unit_id,
                'hm' => $request->hm,
                'problem' => $request->problem,
                'problem_component' => $request->problem_component,
                'problem_description' => $request->problem_description,
                'date_in' => $request->date_in,
                'hour_in' => $request->hour_in,
                'hour_out' => $request->hour_out,
                'shift' => $request->shift,
                'plan_fix' => $request->plan_fix,
                'plan_rekomen' => $request->plan_rekomen,
                'lifetime_component' => $request->lifetime_component,
            ]);
            $nameunit = $dailyReport->unit->unit_code;


            // Sync jobs
            $jobIds = [];
            foreach ($request->jobs as $jobData) {
                // Check if job already exists
                $job = Job::where('job_description', $jobData['description'])->first();

                if (!$job) {
                    // Create new job
                    $job = Job::create([
                        'job_description' => $jobData['description'],
                        'highlight' => isset($jobData['highlight']) && $jobData['highlight'] == '1',
                    ]);
                }

                $jobIds[] = $job->job_description_id;
            }
            $dailyReport->jobs()->sync($jobIds);

            // Sync technicians
            $technicianIds = [];
            foreach ($request->technicians as $technicianData) {
                // Check if technician already exists
                $technician = Technician::where('name', $technicianData['name'])->first();

                if (!$technician) {
                    // Create new technician
                    $technician = Technician::create([
                        'name' => $technicianData['name'],
                    ]);
                }

                $technicianIds[] = $technician->technician_id;
            }
            $dailyReport->technicians()->sync($technicianIds);

            // Sync part problems - delete existing and create new ones
            $dailyReport->partProblems()->delete();
            if ($request->has('part_problems') && is_array($request->part_problems)) {
                foreach ($request->part_problems as $partProblemData) {
                    if (!empty($partProblemData['code_part']) && !empty($partProblemData['part_name'])) {
                        PartProblem::create([
                            'daily_report_id' => $dailyReport->daily_report_id,
                            'code_part' => $partProblemData['code_part'],
                            'part_name' => $partProblemData['part_name'],
                            'quantity' => $partProblemData['quantity'],
                            'eum' => $partProblemData['eum'],
                            'remarks' => $partProblemData['remarks'] ?? null,
                        ]);
                    }
                }
            }

            // Handle new before images upload
            if ($request->hasFile('before_images')) {
                foreach ($request->file('before_images') as $image) {
                    $fileName = time() . '_before_' . uniqid() . '.' . $image->getClientOriginalExtension();
                    $image->move(public_path('assets/daily_reports'), $fileName);

                    DailyReportImage::create([
                        'daily_report_id' => $dailyReport->daily_report_id,
                        'image_path' => $fileName,
                        'type' => 'before',
                    ]);
                }
            }

            // Handle new after images upload
            if ($request->hasFile('after_images')) {
                foreach ($request->file('after_images') as $image) {
                    $fileName = time() . '_after_' . uniqid() . '.' . $image->getClientOriginalExtension();
                    $image->move(public_path('assets/daily_reports'), $fileName);

                    DailyReportImage::create([
                        'daily_report_id' => $dailyReport->daily_report_id,
                        'image_path' => $fileName,
                        'type' => 'after',
                    ]);
                }
            }

            // Handle new unit images upload
            if ($request->hasFile('unit_images')) {
                foreach ($request->file('unit_images') as $image) {
                    $fileName = time() . '_unit_' . uniqid() . '.' . $image->getClientOriginalExtension();
                    $image->move(public_path('assets/daily_reports'), $fileName);

                    DailyReportImage::create([
                        'daily_report_id' => $dailyReport->daily_report_id,
                        'image_path' => $fileName,
                        'type' => 'unit',
                    ]);
                }
            }

            // Log the activity
            LogAktivitas::create([
                'site_id' => session('site_id'),
                'name' => session('name'),
                'action' => 'Mengubah Daily Report',
                'description' => 'User ' . session('name') . ' mengubah Daily Report dengan ' . $nameunit,
                'table' => 'daily_reports',
                'ip_address' => request()->ip(),
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Daily Report berhasil diperbarui',
                'data' => $dailyReport->load(['unit', 'jobs', 'technicians', 'images'])
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating daily report: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        $dailyReport = DailyReport::findOrFail($id);
        $nameunit = $dailyReport->unit->unit_code;

        DB::beginTransaction();
        try {
            // Delete all associated images from storage
            foreach ($dailyReport->images as $image) {
                $imagePath = public_path('assets/daily_reports/' . $image->image_path);
                if (file_exists($imagePath)) {
                    unlink($imagePath);
                }
            }
            $dailyReport->delete();
            LogAktivitas::create([
                'site_id' => session(key: 'site_id'),
                'name' => session('name'),
                'action' => 'Menghapus Daily Report',
                'description' => 'User ' . session('name') . ' menghapus Daily Report dengan ' . $nameunit,
                'table' => 'daily_reports',
                'ip_address' => request()->ip(),
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Daily Report berhasil dihapus'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error deleting daily report: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a specific image from daily report.
     */
    public function deleteImage($imageId)
    {
        $image = DailyReportImage::findOrFail($imageId);

        try {
            // Delete image file from storage
            $imagePath = public_path('assets/daily_reports/' . $image->image_path);
            if (file_exists($imagePath)) {
                unlink($imagePath);
            }

            // Delete image record
            $image->delete();

            return response()->json([
                'success' => true,
                'message' => 'Gambar berhasil dihapus'
            ]);
        } catch (\Exception $e) {
            Log::error('Error deleting image: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Search units for auto-suggestion.
     */
    public function searchUnits(Request $request)
    {
        $search = $request->get('search', '');

        if (strlen($search) < 2) {
            return response()->json([]);
        }

        $units = Unit::where('unit_code', 'like', "%{$search}%")
            ->orWhere('unit_type', 'like', "%{$search}%")
            ->limit(10)
            ->get(['id', 'unit_code', 'unit_type']);

        return response()->json($units);
    }

    /**
     * Get jobs for dropdown.
     */
    public function getJobs()
    {
        $jobs = Job::orderBy('job_description')->get();
        return response()->json($jobs);
    }

    /**
     * Get technicians for dropdown.
     */
    public function getTechnicians()
    {
        $technicians = Technician::orderBy('name')->get();
        return response()->json($technicians);
    }

    /**
     * Store a new job.
     */
    public function storeJob(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'job_description' => 'required|string|max:255|unique:jobs,job_description',
            'highlight' => 'boolean',
        ], [
            'job_description.required' => 'Deskripsi pekerjaan harus diisi',
            'job_description.unique' => 'Deskripsi pekerjaan sudah ada',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $job = Job::create([
                'job_description' => $request->job_description,
                'highlight' => $request->highlight ?? false,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Pekerjaan berhasil ditambahkan',
                'data' => $job
            ]);
        } catch (\Exception $e) {
            Log::error('Error creating job: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a new technician.
     */
    public function storeTechnician(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:technicians,name',
        ], [
            'name.required' => 'Nama teknisi harus diisi',
            'name.unique' => 'Nama teknisi sudah ada',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $technician = Technician::create([
                'name' => $request->name,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Teknisi berhasil ditambahkan',
                'data' => $technician
            ]);
        } catch (\Exception $e) {
            Log::error('Error creating technician: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export daily reports to PDF (TAR format) - Preview mode.
     */
    public function exportPdf(Request $request)
    {
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');
        $search = $request->input('search');

        // Get filtered data
        $query = DailyReport::with(['unit', 'jobs', 'technicians', 'images'])
            ->where('date_in', '>=', $startDate)
            ->where('date_in', '<=', $endDate);

        if ($search) {
            $query->whereHas('unit', function ($q) use ($search) {
                $q->where('unit_code', 'like', "%{$search}%")
                    ->orWhere('unit_type', 'like', "%{$search}%");
            });
        }

        $dailyReports = $query->orderBy('date_in', 'desc')->get();

        // Generate PDF
        $pdf = Pdf::loadView('daily-reports.pdf', compact('dailyReports', 'startDate', 'endDate'));
        $pdf->setPaper('a4', 'portrait');

        // Log the activity
        LogAktivitas::create([
            'site_id' => session('site_id'),
            'name' => session('name'),
            'action' => 'Preview PDF Daily Reports',
            'description' => 'User ' . session('name') . ' preview PDF Daily Reports',
            'table' => 'daily_reports',
            'ip_address' => request()->ip(),
        ]);

        // Return PDF for preview in browser instead of download
        return $pdf->stream('TAR_Daily_Reports_' . date('Y-m-d_H-i-s') . '.pdf');
    }

    /**
     * Export single daily report to PDF (TAR format) - Preview mode.
     */
    public function exportSinglePdf($id)
    {
        $dailyReport = DailyReport::with(['unit', 'jobs', 'technicians', 'images', 'partProblems'])
            ->findOrFail($id);

        // Generate PDF for single report
        $pdf = Pdf::loadView('daily-reports.single-pdf', compact('dailyReport'));
        $pdf->setPaper('a4', 'portrait');

        // Log the activity
        LogAktivitas::create([
            'site_id' => session('site_id'),
            'name' => session('name'),
            'action' => 'Preview Single PDF Daily Report',
            'description' => 'User ' . session('name') . ' preview single PDF Daily Report ID: ' . $id,
            'table' => 'daily_reports',
            'ip_address' => request()->ip(),
        ]);

        // Return PDF for preview in browser
        return $pdf->stream('TAR_Daily_Report_' . $dailyReport->daily_report_id . '_' . date('Y-m-d_H-i-s') . '.pdf');
    }

    /**
     * Export daily reports to Excel using template.
     */
    public function exportExcel(Request $request)
    {
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');
        $search = $request->input('search');

        // Get filtered data
        $query = DailyReport::with(['unit', 'jobs', 'technicians'])
            ->where('date_in', '>=', $startDate)
            ->where('date_in', '<=', $endDate);

        if ($search) {
            $query->whereHas('unit', function ($q) use ($search) {
                $q->where('unit_code', 'like', "%{$search}%")
                    ->orWhere('unit_type', 'like', "%{$search}%");
            });
        }

        $dailyReports = $query->orderBy('date_in', 'desc')->get();

        // Load template Excel file
        $templatePath = public_path('assets/templatereport.xlsx');

        if (!file_exists($templatePath)) {
            return response()->json([
                'success' => false,
                'message' => 'Template file tidak ditemukan'
            ], 404);
        }

        try {
            // Load the template
            $reader = new \PhpOffice\PhpSpreadsheet\Reader\Xlsx();
            $spreadsheet = $reader->load($templatePath);
            $sheet = $spreadsheet->getActiveSheet();

            // Add data
            $row = 10;
            $i = 1;
            foreach ($dailyReports as $report) {
                $start = new DateTime($report->hour_in);
                $end = new DateTime($report->hour_out);
                $interval = $start->diff($end);
                $minutes = ($interval->h * 60) + $interval->i;

                if ($minutes >= 60) {
                    $hours = floor($minutes / 60);
                    $remainingMinutes = $minutes % 60;
                    $durationFormatted = $hours . ' jam';
                    if ($remainingMinutes > 0) {
                        $durationFormatted .= ' ' . $remainingMinutes . ' menit';
                    }
                } else {
                    $durationFormatted = $minutes . ' menit';
                }

                $jobs = $report->jobs->pluck('job_description')->join(',');
                $technicians = $report->technicians->pluck('name')->join(',');

                $tanggalIndo = Carbon::parse($report->date_in)->translatedFormat('d F Y');
                $jobs = explode(',', $jobs);
                $jobs = array_map(fn($job) => '- ' . trim($job), $jobs);
                $technicians = explode(',', $technicians);
                $technicians = array_map(fn($tech) => '- ' . trim($tech), $technicians);

                $sheet->setCellValue('A' . $row, $i);
                $sheet->setCellValue('B' . $row, $tanggalIndo);
                $sheet->setCellValue('C' . $row, $report->unit->unit_type ?? '');
                $sheet->setCellValue('D' . $row, $report->hm);
                $sheet->setCellValue('E' . $row, $report->problem);
                $sheet->setCellValue('F' . $row, $report->problemcomponent ?? '');
                $sheet->setCellValue('G' . $row, $report->problem_description ?? '');
                $sheet->setCellValue('H' . $row, implode("\n", $jobs));
                $sheet->getStyle('H' . $row)->getAlignment()->setWrapText(true);
                $sheet->setCellValue('I' . $row, date('H:i', strtotime($report->hour_in)));
                $sheet->setCellValue('J' . $row, date('H:i', strtotime($report->hour_out)));
                $sheet->setCellValue('K' . $row, $durationFormatted);
                $sheet->setCellValue('L' . $row, $report->shift);
                $sheet->setCellValue('M' . $row, implode("\n", $technicians));
                $sheet->getStyle('M' . $row)->getAlignment()->setWrapText(true);

                // Set align left and vertical middle for each column in the row
                foreach (range('A', 'M') as $col) {
                    $sheet->getStyle($col . $row)->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_LEFT);
                    $sheet->getStyle($col . $row)->getAlignment()->setVertical(\PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER);
                }

                $i++;
                $row++;
            }


            // Generate filename
            $filename = 'Daily_Reports_' . date('Y-m-d_H-i-s') . '.xlsx';
            // Set headers for download
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header("Content-Disposition: attachment;filename=\"{$filename}\"");
            header('Cache-Control: max-age=0');

            $writer = new Xlsx($spreadsheet);
            $writer->save('php://output');
            exit;
        } catch (\Exception $e) {
            Log::error('Error exporting Excel: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat export Excel: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Search parts for auto-suggestion in daily reports.
     */
    public function searchParts(Request $request)
    {
        $search = $request->get('search', '');
        $siteId = session('site_id');

        if (strlen($search) < 2) {
            return response()->json([]);
        }

        // Search in part inventories for the current site
        $parts = PartInventory::with('part')
            ->where('site_id', $siteId)
            ->where(function ($query) use ($search) {
                $query->where('part_code', 'like', "%{$search}%")
                    ->orWhere('site_part_name', 'like', "%{$search}%")
                    ->orWhereHas('part', function ($q) use ($search) {
                        $q->where('part_name', 'like', "%{$search}%");
                    });
            })
            ->limit(10)
            ->get()
            ->map(function ($partInventory) {
                return [
                    'code_part' => $partInventory->part_code,
                    'part_name' => $partInventory->site_part_name ?: $partInventory->part->part_name,
                    'eum' => $partInventory->part->eum ?? 'EA',
                    'stock_quantity' => $partInventory->stock_quantity
                ];
            });

        return response()->json($parts);
    }
}
