/**
 * Superadmin Price List
 * Handles loading and filtering price list data via AJAX
 */

document.addEventListener("DOMContentLoaded", function () {
    // Set CSRF token for all AJAX requests
    $.ajaxSetup({
        headers: {
            "X-CSRF-TOKEN": document
                .querySelector('meta[name="csrf-token"]')
                .getAttribute("content"),
        },
    });

    // Global variables for pagination and filtering
    let currentPage = 1;
    const itemsPerPage = document.getElementById("lenght_page").value; // Changed items per page

    let currentSiteId = "WHO"; // Default to Warehouse
    let currentPartType = "all";
    let currentSearch = "";

    // Load price list data on page load
    loadPriceListData();

    document
        .getElementById("lenght_page")
        .addEventListener("change", function () {
            loadPriceListData();
        });

    // Add event listeners for site filter buttons
    document.querySelectorAll(".site-filters .btn").forEach((button) => {
        button.addEventListener("click", function () {
            // Remove active class from all buttons
            document.querySelectorAll(".site-filters .btn").forEach((btn) => {
                btn.classList.remove("active");
            });

            // Add active class to clicked button
            this.classList.add("active");

            // Update current site ID
            currentSiteId = this.dataset.siteId;

            // Reset to first page and reload data
            currentPage = 1;

            // Load data without refreshing the whole page
            loadPriceListData();

            // If search input is focused, keep it focused
            const searchInput = document.getElementById("searchInput");
            if (document.activeElement === searchInput) {
                setTimeout(() => {
                    searchInput.focus();
                    searchInput.setSelectionRange(
                        searchInput.selectionStart,
                        searchInput.selectionStart
                    );
                }, 10);
            }
        });
    });

    // Add event listener for part type filter buttons
    document.addEventListener("click", function (e) {
        if (e.target.closest(".part-type-filters .btn")) {
            const button = e.target.closest(".part-type-filters .btn");
            document
                .querySelectorAll(".part-type-filters .btn")
                .forEach((btn) => {
                    btn.classList.remove("active");
                });
            button.classList.add("active");
            currentPartType = button.dataset.partType;
            currentPage = 1;
            loadPriceListData();
            const searchInput = document.getElementById("searchInput");
            if (document.activeElement === searchInput) {
                setTimeout(() => {
                    searchInput.focus();
                    searchInput.setSelectionRange(
                        searchInput.selectionStart,
                        searchInput.selectionStart
                    );
                }, 10);
            }
        }
    });

    // Add event listener for search input
    const searchInput = document.getElementById("searchInput");

    if (searchInput) {
        // Add input event with debounce
        searchInput.addEventListener(
            "input",
            debounce(function () {
                const searchValue = this.value.trim();

                // Only trigger search if the value has changed
                if (searchValue !== currentSearch) {
                    currentSearch = searchValue;
                    currentPage = 1;

                    // Save current cursor position
                    const cursorPosition = this.selectionStart;

                    // Load data without refreshing the whole page
                    loadPriceListData();

                    // Ensure input stays focused and cursor position is maintained
                    setTimeout(() => {
                        this.focus();
                        this.setSelectionRange(cursorPosition, cursorPosition);
                    }, 10);
                }
            }, 300)
        ); // Reduced debounce time for more responsive feel

        // Add event listener for Enter key
        searchInput.addEventListener("keydown", function (e) {
            if (e.key === "Enter") {
                e.preventDefault();

                const searchValue = this.value.trim();
                currentSearch = searchValue;
                currentPage = 1;

                // Save current cursor position
                const cursorPosition = this.selectionStart;

                // Load data without refreshing the whole page
                loadPriceListData();

                // Ensure input stays focused and cursor position is maintained
                setTimeout(() => {
                    this.focus();
                    this.setSelectionRange(cursorPosition, cursorPosition);
                }, 10);
            }
        });
    }

    // Add event listener for pagination
    document.addEventListener("click", function (e) {
        if (e.target.closest(".pagination .page-link")) {
            e.preventDefault();
            const pageLink = e.target.closest(".pagination .page-link");
            if (pageLink.dataset.page) {
                // Store the current search input focus state
                const searchInput = document.getElementById("searchInput");
                const wasSearchFocused = document.activeElement === searchInput;
                const searchPosition = searchInput
                    ? searchInput.selectionStart
                    : 0;

                currentPage = parseInt(pageLink.dataset.page);

                // Load data without refreshing the whole page
                loadPriceListData();

                // Scroll to top of table
                document
                    .querySelector(".price-list-table")
                    .scrollIntoView({ behavior: "smooth" });

                // Restore search input focus if it was focused before
                if (wasSearchFocused && searchInput) {
                    setTimeout(() => {
                        searchInput.focus();
                        searchInput.setSelectionRange(
                            searchPosition,
                            searchPosition
                        );
                    }, 10);
                }
            }
        }
    });

    /**
     * Load price list data via AJAX
     */

    function loadPriceListData() {
        // Store the current search input focus state
        const searchInput = document.getElementById("searchInput");
        const wasSearchFocused = document.activeElement === searchInput;
        const searchPosition = searchInput ? searchInput.selectionStart : 0;
        const itemlenght = document.getElementById("lenght_page").value;

        // No loading indicators needed

        // Fetch price list data via AJAX
        fetch(
            `/superadmin/price-list-data?site_id=${currentSiteId}&part_type=${currentPartType}&search=${currentSearch}&page=${currentPage}&per_page=${itemlenght}`
        )
            .then((response) => {
                if (!response.ok) {
                    throw new Error("Network response was not ok");
                }
                return response.json();
            })
            .then((data) => {
                // Update part type filter buttons if this is the first load
                if (
                    currentPage === 1 &&
                    currentPartType === "all" &&
                    currentSearch === ""
                ) {
                    updatePartTypeFilters(data.part_types);
                }

                // Render price list table
                renderPriceListTable(data.parts);

                // Render pagination
                renderPagination(data.pagination);

                // Restore search input focus if it was focused before
                if (wasSearchFocused && searchInput) {
                    searchInput.focus();
                    searchInput.setSelectionRange(
                        searchPosition,
                        searchPosition
                    );
                }
            })
            .catch((error) => {
                // Show error message
                const tableBody = document.getElementById("priceListTableBody");
                if (tableBody) {
                    tableBody.innerHTML = `
                        <tr>
                            <td colspan="4" class="text-center">
                                <div class="alert alert-danger mb-0">
                                    <i class="mdi mdi-alert-circle-outline me-2"></i>
                                    Gagal memuat data. Silakan coba lagi.
                                </div>
                            </td>
                        </tr>
                    `;
                }

                // Restore search input focus if it was focused before
                if (wasSearchFocused && searchInput) {
                    searchInput.focus();
                    searchInput.setSelectionRange(
                        searchPosition,
                        searchPosition
                    );
                }
            });
    }

    /**
     * Update part type filter buttons
     *
     * @param {Array} partTypes
     */
    function updatePartTypeFilters(partTypes) {
        const filterContainer = document.querySelector(".part-type-filters");
        if (!filterContainer) return;

        // Keep the "All Types" button and add new buttons for each part type
        const allTypesButton = filterContainer.querySelector(
            '[data-part-type="all"]'
        );
        filterContainer.innerHTML = "";

        if (allTypesButton) {
            filterContainer.appendChild(allTypesButton);
        }

        // Add buttons for each part type
        partTypes.forEach((partType) => {
            if (!partType) return; // Skip empty part types

            const button = document.createElement("button");
            button.type = "button";

            // Set different button colors based on part type
            let buttonClass = "btn ";
            if (partType === "AC") {
                buttonClass += "btn-outline-success";
            } else if (partType === "TYRE") {
                buttonClass += "btn-outline-info";
            } else if (partType === "FABRIKASI") {
                buttonClass += "btn-outline-purple";
            } else {
                buttonClass += "btn-outline-primary";
            }

            button.className = buttonClass;
            button.dataset.partType = partType;
            button.textContent = partType;

            filterContainer.appendChild(button);
        });
    }

    /**
     * Render price list table
     *
     * @param {Array} parts
     */
    function renderPriceListTable(parts) {
        const tableBody = document.getElementById("priceListTableBody");
        if (!tableBody) return;

        // Clear table body
        tableBody.innerHTML = "";

        if (parts.length === 0) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="4" class="text-center">
                        <div class="alert alert-info mb-0">
                            <i class="mdi mdi-information-outline me-2"></i>
                            Tidak ada data yang ditemukan.
                        </div>
                    </td>
                </tr>
            `;
            return;
        }

        // Add rows for each part
        let countpage = document.getElementById('lenght_page').value
        let i = 1+(countpage*currentPage)-countpage;
        parts.forEach((part) => {
            const row = document.createElement("tr");

            // Part code
            const nocell = document.createElement("td");
            nocell.textContent = i++;
            row.appendChild(nocell);

            // Part code
            const codeCell = document.createElement("td");
            codeCell.textContent = part.part_code;
            row.appendChild(codeCell);

            // Part name
            const nameCell = document.createElement("td");
            nameCell.textContent = part.part_name;
            row.appendChild(nameCell);

            // Part type
            const typeCell = document.createElement("td");
            typeCell.textContent = part.part_type || "-";
            row.appendChild(typeCell);

            // Price cell - single price column
            const priceCell = document.createElement("td");

            // Determine the price based on the selected site
            let price = 0;

            if (currentSiteId === "WHO") {
                // For Warehouse, use the base price from parts table
                price = part.base_price || 0;
            } else if (
                part.part_inventories &&
                part.part_inventories.length > 0
            ) {
                // For other sites, we should only have one inventory item since we filtered in the controller
                const siteInventory = part.part_inventories[0];
                if (siteInventory) {
                    price = siteInventory.price || 0;
                }
            }

            // Display the price
            priceCell.textContent = formatRupiah(price);
            row.appendChild(priceCell);

            tableBody.appendChild(row);
        });
    }

    /**
     * Render pagination
     *
     * @param {Object} pagination
     */
    function renderPagination(pagination) {
        const container = document.getElementById("pagination-container");
        if (!container) return;

        container.innerHTML = "";

        if (pagination.total <= pagination.per_page) return;

        const ul = document.createElement("ul");
        ul.className = "pagination";

        // Previous button
        if (pagination.current_page > 1) {
            const li = document.createElement("li");
            li.className = "page-item";

            const a = document.createElement("a");
            a.className = "page-link";
            a.href = "#";
            a.dataset.page = pagination.current_page - 1;
            a.innerHTML = "&laquo;";

            li.appendChild(a);
            ul.appendChild(li);
        }

        // Page numbers
        const totalPages = pagination.last_page;
        const currentPage = pagination.current_page;

        // Determine which page numbers to show
        let startPage = Math.max(1, currentPage - 2);
        let endPage = Math.min(totalPages, startPage + 4);

        if (endPage - startPage < 4) {
            startPage = Math.max(1, endPage - 4);
        }

        for (let i = startPage; i <= endPage; i++) {
            const li = document.createElement("li");
            li.className = `page-item ${i === currentPage ? "active" : ""}`;

            const a = document.createElement("a");
            a.className = "page-link";
            a.href = "#";
            a.dataset.page = i;
            a.textContent = i;

            li.appendChild(a);
            ul.appendChild(li);
        }

        // Next button
        if (pagination.current_page < pagination.last_page) {
            const li = document.createElement("li");
            li.className = "page-item";

            const a = document.createElement("a");
            a.className = "page-link";
            a.href = "#";
            a.dataset.page = pagination.current_page + 1;
            a.innerHTML = "&raquo;";

            li.appendChild(a);
            ul.appendChild(li);
        }

        container.appendChild(ul);
    }

    /**
     * Format number to Indonesian Rupiah
     *
     * @param {number} number
     * @returns {string}
     */
    function formatRupiah(number) {
        return new Intl.NumberFormat("id-ID", {
            style: "currency",
            currency: "IDR",
            minimumFractionDigits: 0,
            maximumFractionDigits: 0,
        }).format(number);
    }

    /**
     * Debounce function to limit how often a function can be called
     *
     * @param {Function} func
     * @param {number} wait
     * @returns {Function}
     */
    function debounce(func, wait) {
        let timeout;
        return function () {
            const context = this;
            const args = arguments;
            clearTimeout(timeout);
            timeout = setTimeout(() => {
                func.apply(context, args);
            }, wait);
        };
    }

    // Export functionality
    const exportPriceListBtn = document.getElementById("export-price-list-btn");
    const exportPriceListModal = new bootstrap.Modal(document.getElementById("exportPriceListModal"));
    const exportPriceListPdfBtn = document.getElementById("export-price-list-pdf-btn");
    const exportPriceListExcelBtn = document.getElementById("export-price-list-excel-btn");

    // Show export modal
    if (exportPriceListBtn) {
        exportPriceListBtn.addEventListener("click", function () {
            exportPriceListModal.show();
        });
    }

    // Export to PDF
    if (exportPriceListPdfBtn) {
        exportPriceListPdfBtn.addEventListener("click", function () {
            exportPriceList('pdf');
            exportPriceListModal.hide();
        });
    }

    // Export to Excel
    if (exportPriceListExcelBtn) {
        exportPriceListExcelBtn.addEventListener("click", function () {
            exportPriceList('excel');
            exportPriceListModal.hide();
        });
    }

    /**
     * Export price list data
     *
     * @param {string} format - Export format ('pdf' or 'excel')
     */
    function exportPriceList(format) {
        // Get current filter values
        const params = new URLSearchParams({
            site_id: currentSiteId,
            part_type: currentPartType,
            search: currentSearch,
        });

        // Create export URL
        const exportUrl = `/superadmin/price-list/export/${format}?${params.toString()}`;

        // Open export URL in new window/tab
        window.open(exportUrl, '_blank');
    }
});
