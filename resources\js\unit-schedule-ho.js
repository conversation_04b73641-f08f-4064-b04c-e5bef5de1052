import Swal from "sweetalert2";

class UnitScheduleHOManager {
    constructor() {
        this.currentData = [];
        this.filteredData = [];
        this.currentFilter = "all";
        this.searchTerm = "";
        this.selectedSite = "";
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadData();
    }

    bindEvents() {
        // Site filter
        $("#site-filter").on("change", (e) => {
            this.selectedSite = e.target.value;
            this.loadData();
        });

        // Search input
        $("#search-input").on("input", (e) => {
            this.searchTerm = e.target.value.toLowerCase();
            this.applyFilters();
        });

        // Filter buttons
        $("#filter-all").on("click", () => this.setFilter("all"));
        $("#filter-overdue").on("click", () => this.setFilter("overdue"));
        $("#filter-due-soon").on("click", () => this.setFilter("due-soon"));
        $("#filter-normal").on("click", () => this.setFilter("normal"));

        // Refresh button
        $("#refresh-btn").on("click", () => this.loadData());

        // Export button
        $("#export-excel-btn").on("click", () => this.exportData());

        // details
        $("#unit-schedule-tbody").on("click", ".btn-detail", (e) => {
            const unitId = $(e.currentTarget).data("unit-id");
            this.showDetail(unitId);
        });
    }

    setFilter(filter) {
        this.currentFilter = filter;

        // Update button states
        $(
            ".btn-outline-primary, .btn-outline-danger, .btn-outline-warning, .btn-outline-success"
        ).removeClass("active");
        $(`#filter-${filter}`).addClass("active");

        this.applyFilters();
    }

    applyFilters() {
        let filtered = [...this.currentData];

        // Apply status filter
        if (this.currentFilter !== "all") {
            filtered = filtered.filter((item) => {
                switch (this.currentFilter) {
                    case "overdue":
                        return (
                            item.status === "Overdue" ||
                            item.status === "Critical"
                        );
                    case "due-soon":
                        return item.status === "Due Soon";
                    case "normal":
                        return item.status === "Normal";
                    default:
                        return true;
                }
            });
        }

        // Apply search filter
        if (this.searchTerm) {
            filtered = filtered.filter(
                (item) =>
                    item.unit_code.toLowerCase().includes(this.searchTerm) ||
                    item.unit_type.toLowerCase().includes(this.searchTerm) ||
                    item.site_name.toLowerCase().includes(this.searchTerm)
            );
        }

        this.filteredData = filtered;
        this.renderTable();
        this.updateSummary();
    }

    async loadData() {
        try {
            this.showLoading();

            const params = new URLSearchParams();
            if (this.selectedSite) {
                params.append("site_id", this.selectedSite);
            }

            const url =
                window.unitScheduleConfig.dataUrl + "?" + params.toString();

            const response = await fetch(url, {
                method: "GET",
                headers: {
                    "X-CSRF-TOKEN": window.unitScheduleConfig.csrfToken,
                    Accept: "application/json",
                    "Content-Type": "application/json",
                },
            });

            const result = await response.json();

            if (result.success) {
                this.currentData = result.data;
                this.filteredData = [...this.currentData];
                this.renderTable();
                this.updateSummary();
                this.hideLoading();
            } else {
                throw new Error(result.message || "Failed to load data");
            }
        } catch (error) {
            console.error("Error loading unit schedule data:", error);
            this.hideLoading();
            this.showError("Gagal memuat data jadwal unit: " + error.message);
        }
    }

    renderTable() {
        const tbody = $("#unit-schedule-tbody");
        tbody.empty();

        if (this.filteredData.length === 0) {
            $("#data-table-container").hide();
            $("#empty-state").show();
            $("#summary-cards").hide();
            return;
        }

        $("#empty-state").hide();
        $("#data-table-container").show();
        $("#summary-cards").show();

        this.filteredData.forEach((item) => {
            const row = this.createTableRow(item);
            tbody.append(row);
        });
    }

    createTableRow(item) {  
        const statusBadge = this.getStatusBadge(item.status, item.status_class);
        const days = Number(item.days_until_service).toFixed(0);
        const day = !isNaN(days) ? days : '-';
        return `
            <tr>
                <td><strong>${item.unit_code}</strong></td>
                <td>${item.unit_type}</td>
                <td><span class="badge bg-info">${item.site_name}</span></td>
                <td class="text-center">${item.current_hm}</td>
                <td class="text-center">${item.last_service_date}</td>
                <td class="text-center">${item.next_service_hm}</td>
                <td class="text-center">${item.remaining_hm}</td>
                <td class="text-center">${day}</td>
                <td class="text-center">${item.predicted_service_date}</td>
                <td class="text-center">${statusBadge}</td>
                <td><button class="btn btn-sm btn-primary btn-detail pt-0 pb-0 pl-1 pr-1" data-unit-id="${item.unit_id}" title="Detail">View Parts</button></td>
            </tr>
        `;
    }

    showDetail(unitid) {
        fetch(`/scheduleunitWHO/parts/detail/${unitid}`) // Adjust the URL to your CodeIgniter route
            .then((response) => response.json())
            .then((data) => {
                let html = ``;
                // Asumsikan data.parts adalah array daftar part
                if (data.length > 0) {
                    html += `
                            <table class="table table-striped table-bordered">
                                <thead>
                                    <tr>
                                        <th>No</th>
                                        <th>Part Code</th>
                                        <th>Part Name</th>
                                        <th>Jumlah</th>
                                        <th>Stock</th>
                                    </tr>
                                </thead>
                                <tbody>`;
                    data.forEach((part, index) => {
                        html += `
                                <tr class="${
                                    part.stock === 0
                                        ? "bg-secondary text-white"
                                        : ""
                                }">
                                <td>${index + 1}</td>
                                <td>${part.part_code}</td>
                                <td>${part.part_name}</td>
                                <td>${part.quantity}</td>
                                <td>${part.stock}</td>
                            </tr>`;
                    });
                    html += `</tbody></table> `;
                } else {
                    html += `
                            <table class="table table-striped table-bordered">
                                <thead>
                                    <tr>
                                        <th class="text-center">Tidak Ada Part yang Didaftarkan</th>
                                    </tr>
                                </thead>
                            </table> `;
                }
                document.getElementById("modalDetailBody").innerHTML = html;
                new bootstrap.Modal(
                    document.getElementById("unitDetailModal")
                ).show();
            })
            .catch((error) => {
                console.error("Error:", error);
                document.getElementById("modalDetailBody").innerHTML =
                    '<p class="text-danger">Failed to load data.</p>';
            });
    }

    getStatusBadge(status, statusClass) {
        let badgeClass = "badge bg-secondary";

        switch (status) {
            case "Overdue":
            case "Critical":
                badgeClass = "badge bg-danger";
                break;
            case "Due Soon":
                badgeClass = "badge bg-secondary";
                break;
            case "Normal":
                badgeClass = "badge bg-success";
                break;
            default:
                badgeClass = "badge bg-secondary";
        }

        return `<span class="${badgeClass}">${status}</span>`;
    }

    updateSummary() {
        const summary = {
            overdue: 0,
            dueSoon: 0,
            normal: 0,
            noData: 0,
        };

        this.currentData.forEach((item) => {
            if (!item.has_data) {
                summary.noData++;
            } else {
                switch (item.status) {
                    case "Overdue":
                    case "Critical":
                        summary.overdue++;
                        break;
                    case "Due Soon":
                        summary.dueSoon++;
                        break;
                    case "Normal":
                        summary.normal++;
                        break;
                    default:
                        summary.noData++;
                }
            }
        });

        $("#overdue-count").text(summary.overdue);
        $("#due-soon-count").text(summary.dueSoon);
        $("#normal-count").text(summary.normal);
        $("#no-data-count").text(summary.noData);
    }

    showLoading() {
        $("#loading-skeleton").removeClass("d-none");
        $("#data-table-container").hide();
        $("#empty-state").hide();
        $("#summary-cards").hide();
    }

    hideLoading() {
        $("#loading-skeleton").addClass("d-none");
    }

    async exportData() {
        try {
            const params = new URLSearchParams();
            if (this.selectedSite) {
                params.append("site_id", this.selectedSite);
            }
            if (this.searchTerm) {
                params.append("search", this.searchTerm);
            }

            const url =
                window.unitScheduleConfig.exportUrl + "?" + params.toString();
            window.open(url, "_blank");

            Swal.fire({
                title: "Export Berhasil",
                text: "Data jadwal unit berhasil diekspor",
                icon: "success",
                timer: 2000,
                showConfirmButton: false,
            });
        } catch (error) {
            console.error("Error exporting data:", error);
            this.showError("Gagal mengekspor data: " + error.message);
        }
    }

    showError(message) {
        Swal.fire({
            title: "Error",
            text: message,
            icon: "error",
            confirmButtonText: "OK",
        });
    }
}

// Initialize when document is ready
$(document).ready(() => {
    new UnitScheduleHOManager();
});
