# Inventory Export Testing Guide

## Prerequisites
1. Ensure you have inventory data in the database
2. Make sure different user roles are available for testing
3. Verify SweetAlert2 is working on the page

## Test Cases

### 1. Basic Export Functionality
- [ ] Navigate to `/inventory` page
- [ ] Verify export buttons are visible in the header
- [ ] Select a site and date range
- [ ] Click "Export Excel" button
- [ ] Verify file downloads successfully
- [ ] Click "Export PDF" button  
- [ ] Verify PDF downloads successfully

### 2. Access Control Testing
- [ ] <PERSON><PERSON> as site user (adminsite/karyawan)
- [ ] Verify only own site data is exported
- [ ] <PERSON><PERSON> as warehouse user (adminho)
- [ ] Verify can export any site data
- [ ] Login as superadmin
- [ ] Verify part type exclusions work (no 'PERSEDIAAN LAINNYA' or 'PERLENGKAPAN AC')

### 3. Filter Integration Testing
- [ ] Apply search filter on page
- [ ] Export and verify search term is applied
- [ ] Change date range
- [ ] Export and verify date range is applied
- [ ] Change site selection (for warehouse/superadmin)
- [ ] Export and verify site filter is applied

### 4. Data Validation Testing
- [ ] Try to export without selecting site
- [ ] Verify warning message appears
- [ ] Try to export without date range
- [ ] Verify validation works
- [ ] Test with empty search results
- [ ] Verify export handles empty data gracefully

### 5. File Content Verification
- [ ] Open exported Excel file
- [ ] Verify headers are correct
- [ ] Check status color coding (Red=Not Ready, Yellow=Medium, Green=Ready)
- [ ] Verify data matches page display
- [ ] Open exported PDF file
- [ ] Check layout and formatting
- [ ] Verify summary statistics
- [ ] Check Indonesian date formatting

### 6. Error Handling Testing
- [ ] Test with very large datasets
- [ ] Verify memory optimization works
- [ ] Test network interruption during export
- [ ] Verify error messages are user-friendly
- [ ] Test with invalid parameters
- [ ] Verify proper error responses

### 7. Performance Testing
- [ ] Test export with 1000+ inventory items
- [ ] Measure export time
- [ ] Verify no memory exhaustion errors
- [ ] Test concurrent exports
- [ ] Verify server stability

## Expected Results

### Excel Export
- File format: .xlsx
- Filename: `Inventory_Monitoring_{SiteName}_{DateTime}.xlsx`
- Headers: Kode Part, Nama Part, Stok Saat Ini, Min Stock, Max Stock, Jumlah Masuk, Jumlah Keluar, Status
- Status colors: Red (Not Ready), Yellow (Medium), Green (Ready)
- Auto-sized columns
- Bordered cells

### PDF Export
- File format: .pdf
- Filename: `Inventory_Monitoring_{SiteName}_{DateTime}.pdf`
- Landscape orientation
- Professional header with site name
- Summary statistics (Ready/Medium/Not Ready counts)
- Color-coded status in table
- Footer with export timestamp

### User Feedback
- Loading message during export
- Success message after download
- Warning for missing required fields
- Error messages for failures

## Troubleshooting

### Common Issues
1. **Export buttons not visible**: Check if user has proper permissions
2. **Download not starting**: Verify routes are registered correctly
3. **Empty files**: Check if data exists for selected filters
4. **Memory errors**: Verify memory optimization is working
5. **Permission errors**: Check file write permissions

### Debug Steps
1. Check browser console for JavaScript errors
2. Check Laravel logs for server errors
3. Verify database has inventory data
4. Test with smaller datasets first
5. Check network tab for failed requests
