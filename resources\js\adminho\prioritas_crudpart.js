import Swal from "sweetalert2";

// Fungsi untuk mengisi form part inventory
window.fillForm = function (id, code, name, site, location, min, max, stock) {
    const idPartInventory = document.getElementById("id_partinventory");
    const codePart = document.getElementById("code_part");
    const partName = document.getElementById("part_name");
    const siteName = document.getElementById("site_name");
    const locationInput = document.getElementById("location");
    const minStock = document.getElementById("min_stock");
    const maxStock = document.getElementById("max_stock");
    const stockInput = document.getElementById("stock");

    if (idPartInventory) idPartInventory.value = id;
    if (codePart) codePart.value = code;
    if (partName) partName.value = name;
    if (siteName) siteName.value = site;
    if (locationInput) locationInput.value = location;
    if (minStock) minStock.value = min;
    if (maxStock) maxStock.value = max;
    if (stockInput) stockInput.value = stock;

    // Update form action
    let form = document.getElementById("partForm");
    if (form) {
        form.action = form.action.replace("__ID__", id);
    }
};

document.addEventListener("DOMContentLoaded", function () {
    // Tangkap event ketika modal edit ditampilkan
    $("#editPartModal").on("show.bs.modal", function (event) {
        const button = event.relatedTarget; // Tombol yang memicu modal
        const codePart = button.getAttribute("data-code"); // Ambil data dari atribut data-code
        const partName = button.getAttribute("data-name"); // Ambil data dari atribut data-name
        const type = button.getAttribute("data-type"); // Ambil data dari atribut data-type
        const bin = button.getAttribute("data-bin"); // Ambil data dari atribut data-bin

        const modal = this;
        const editCodePart = modal.querySelector("#edit_code_part");
        const editPartName = modal.querySelector("#edit_part_name");
        const editType = modal.querySelector("#edit_type");
        const editBin = modal.querySelector("#edit_bin");

        if (editCodePart) editCodePart.value = codePart;
        if (editPartName) editPartName.value = partName;
        if (editType) editType.value = type;
        if (editBin) editBin.value = bin;

        // Update action form dengan route yang sesuai
        const form = modal.querySelector("#editPartForm");
        if (form) {
            form.setAttribute("action", `/parts/${codePart}`);
        }
    });
});

document.addEventListener("DOMContentLoaded", function () {
    const searchInput = document.getElementById("searchInput");
    const searchForm = document.getElementById("searchForm");
    const tableBody = document.querySelector("#partsTable tbody");

    if (searchForm && searchInput && tableBody) {
        const rows = tableBody.querySelectorAll("tr");

        // Handle Form Submission
        searchForm.addEventListener("submit", function (e) {
            e.preventDefault(); // Mencegah form submit
            const query = searchInput.value.trim().toLowerCase();
            filterTable(query);
        });

        // Realtime Search Suggestion
        searchInput.addEventListener("input", function () {
            const query = this.value.trim().toLowerCase();
            filterTable(query);
        });

        // Fungsi untuk memfilter tabel
        function filterTable(query) {
            rows.forEach((row) => {
                const cells = row.querySelectorAll("td");
                let match = false;

                // Cek setiap kolom (kecuali kolom aksi)
                for (let i = 0; i < cells.length - 1; i++) {
                    const cellText = cells[i].textContent.toLowerCase();
                    if (cellText.includes(query)) {
                        match = true;
                        break;
                    }
                }

                // Tampilkan atau sembunyikan baris berdasarkan hasil pencarian
                row.style.display = match ? "" : "none";
            });
        }
    }

    document.querySelectorAll(".site-checkbox").forEach((checkbox) => {
        checkbox.addEventListener("change", function () {
            const siteFields =
                this.closest(".card")?.querySelector(".site-fields");
            const siteId = this.dataset.site;

            if (siteFields) {
                if (this.checked) {
                    siteFields.style.display = "block";
                    // Tambahkan input hidden untuk menandai site terpilih
                    const hiddenInput = document.createElement("input");
                    hiddenInput.type = "hidden";
                    hiddenInput.name = `sites[${siteId}][selected]`;
                    hiddenInput.value = "1";
                    siteFields.appendChild(hiddenInput);
                } else {
                    siteFields.style.display = "none";
                    // Hapus input hidden jika checkbox dicentang ulang
                    const existingHidden = siteFields.querySelector(
                        'input[type="hidden"]'
                    );
                    if (existingHidden) existingHidden.remove();
                }
            }
        });
    });
});

document.addEventListener("DOMContentLoaded", function () {
    // Handle Edit Modal
    const editPartModal = document.getElementById("editPartModal");
    if (editPartModal) {
        editPartModal.addEventListener("show.bs.modal", function (event) {
            const button = event.relatedTarget; // Tombol yang memicu modal
            const codePart = button.getAttribute("data-code"); // Ambil data dari atribut data-code
            const partName = button.getAttribute("data-name"); // Ambil data dari atribut data-name
            const type = button.getAttribute("data-type"); // Ambil data dari atribut data-type
            const bin = button.getAttribute("data-bin"); // Ambil data dari atribut data-bin

            const modal = this;
            const editCodePart = modal.querySelector("#edit_code_part");
            const editPartName = modal.querySelector("#edit_part_name");
            const editType = modal.querySelector("#edit_type");
            const editBin = modal.querySelector("#edit_bin");

            if (editCodePart) editCodePart.value = codePart;
            if (editPartName) editPartName.value = partName;
            if (editType) editType.value = type;
            if (editBin) editBin.value = bin;

            // Update action form dengan route yang sesuai
            const form = modal.querySelector("#editPartForm");
            if (form) {
                form.setAttribute("action", `/parts/${codePart}`);
            }
        });
    }
});

document.addEventListener("DOMContentLoaded", function () {
    document.querySelectorAll(".delete-part-btn").forEach((button) => {
        button.addEventListener("click", function (event) {
            event.preventDefault(); // Mencegah form langsung terkirim

            const form = this.closest("form"); // Ambil form terdekat

            Swal.fire({
                title: "Apakah Anda yakin?",
                text: "Data ini akan dihapus secara permanen!",
                icon: "warning",
                showCancelButton: true,
                confirmButtonColor: "#d33",
                cancelButtonColor: "#3085d6",
                confirmButtonText: "Ya, hapus!",
                cancelButtonText: "Batal",
            }).then((result) => {
                if (result.isConfirmed && form) {
                    form.submit(); // Submit form jika dikonfirmasi
                }
            });
        });
    });
});

// Fungsi untuk mengambil daftar site
function debounce(func, wait) {
    let timeout;
    return function (...args) {
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(this, args), wait);
    };
}

let selectedPart = null;

const siteFilter = document.getElementById("siteFilter");
if (siteFilter) {
    siteFilter.addEventListener("change", function (e) {
        fetch(`/part-group/filter?site_id=${e.target.value}`)
            .then((res) => {
                if (!res.ok) {
                    throw new Error("Network response was not ok");
                }
                return res.text();
            })
            .then((html) => {
                const partTable = document.getElementById("partTable");
                if (partTable) {
                    partTable.innerHTML = html;
                }
            })
            .catch((error) => {
                console.error("Error:", error);
            });
    });
}

const partSearch = document.getElementById("partSearch");
if (partSearch) {
    partSearch.addEventListener(
        "input",
        debounce(function (e) {
            const searchValue = e.target.value;
            const suggestionsDiv = document.getElementById("suggestions");

            if (searchValue && suggestionsDiv) {
                fetch(`/part-group/autocomplete?search=${searchValue}`)
                    .then((res) => res.json())
                    .then((parts) => {
                        suggestionsDiv.innerHTML = parts
                            .map(
                                (part) => `
                        <div class="suggestion-item" data-code="${part.code_part}" data-name="${part.part_name}">
                            ${part.code_part} - ${part.part_name}
                        </div>
                    `
                            )
                            .join("");
                        suggestionsDiv.style.display = "block"; // Tampilkan div suggestions
                    });
            } else if (suggestionsDiv) {
                suggestionsDiv.style.display = "none"; // Sembunyikan div suggestions jika input kosong
            }
        }, 300)
    );
}

const suggestions = document.getElementById("suggestions");
if (suggestions) {
    suggestions.addEventListener("click", (e) => {
        if (e.target.classList.contains("suggestion-item")) {
            const codePart = e.target.dataset.code;
            const partName = e.target.dataset.name;

            // Isi input part yang dipilih
            const selectedPartName =
                document.getElementById("selectedPartName");
            if (selectedPartName) {
                selectedPartName.value = `${codePart} - ${partName}`;
            }
            selectedPart = codePart; // Simpan code_part yang dipilih

            // Sembunyikan div suggestions
            suggestions.style.display = "none";

            // Kosongkan input pencarian
            if (partSearch) {
                partSearch.value = "";
            }
        }
    });
}

document.querySelectorAll(".site-checkbox").forEach((checkbox) => {
    checkbox.addEventListener("change", (e) => {
        const siteInputs = document.getElementById(
            `siteInputs_${e.target.value}`
        );
        if (siteInputs) {
            siteInputs.style.display = e.target.checked ? "block" : "none";
        }
    });
});

const addPartForm = document.getElementById("addPartForm");
if (addPartForm) {
    addPartForm.addEventListener("submit", function (e) {
        e.preventDefault();

        const sites = [];
        document
            .querySelectorAll(".site-checkbox:checked")
            .forEach((checkbox) => {
                const siteId = checkbox.value;
                const minStock = document.querySelector(
                    `#minStock_${siteId}`
                )?.value;
                const maxStock = document.querySelector(
                    `#maxStock_${siteId}`
                )?.value;

                 const price = document.querySelector(
                    `#price_${siteId}`
                )?.value;

                if (minStock !== undefined && maxStock !== undefined) {
                    sites.push({
                        site_id: siteId,
                        min_stock: minStock,
                        max_stock: maxStock,
                        price: price,
                    });
                }
            });
        const payload = {
            code_part: selectedPart,
            sites: sites,
        };
        fetch("/part-group", {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                "X-CSRF-TOKEN": document.querySelector(
                    'meta[name="csrf-token"]'
                )?.content,
            },
            body: JSON.stringify(payload),
        })
            .then((response) => {
                if (!response.ok) {
                    return response.json().then((errorData) => {
                        throw new Error(
                            errorData.message || "Terjadi kesalahan pada server"
                        );
                    });
                }
                return response.json();
            })
            .then((data) => {
                if (data.success) {
                    alert(data.message); 
                    location.reload(); 
                } else {
                    throw new Error(data.message || "Terjadi kesalahan");
                }
            })
            .catch((error) => {
                console.error("Error:", error);
                alert(error.message);
            });
    });
}
