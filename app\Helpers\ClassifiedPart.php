<?php

namespace App\Helpers;

class ClassifiedPart
{
    public static function classifyWithInOut(array $stockInHistory, array $stockOutHistory, int $stockNow): string
    {

        // ==========================================================================================================================================================
        //                                                                                   TESTING NEW ALGORITMA
        // ==========================================================================================================================================================

        // ambil total
        $totalIn = array_sum($stockInHistory);
        $totalOut = array_sum($stockOutHistory);

        // jumlah mingguan yang digunakan
        $weeks = count($stockOutHistory);

        // dalam sebaran data, berapa kali data ada, jumlah sebarang tidak menentukan jumlah out.
        // [0,0,0,3,0,0,10,0] sebaran = 2, out = 13
        $sebaranout = count(array_filter($stockOutHistory, fn($v) => $v > 0));
        $sebaranin = count(array_filter($stockInHistory, fn($v) => $v > 0));

        // berapa persent data ada dalam sebaran, semakin besar maka semakin sering
        $percentout = $sebaranout/$weeks*100;
        $percentin = $sebaranin/$weeks*100;
        
        // return 1 atau 0, mengambil 4 minggu awal
        // jika minggu kelima selebihnya kosong maka return 0, artinya hanya diawal
        $isEarlyOut = (int)(($nonZeroCount = count(array_filter($s = $stockOutHistory))) <= 4 && array_sum(array_slice($s, $nonZeroCount)) === 0);

        // rata-rata keluar

        if ($totalOut == 0 && $sebaranin >= 0 &&  $stockNow > 2) {
            return 'Dormant';
        } elseif ($percentout > 50 && $percentout >= 1  &&  $totalOut >= ($totalIn*0.80) && $isEarlyOut != 1) {
            return 'High Demand';
        } elseif ($percentin > $percentout && $totalIn > $totalOut || $sebaranout  < $sebaranin) {
            return 'overstock';
        } elseif ($isEarlyOut && $percentout < 25 && $percentout < 1  && $isEarlyOut != 1) {
            return 'Low Demand';
        } else {
            return 'Uncategorized'; // default
        }

        // ==========================================================================================================================================================
        //                                                                      TESTING NEW ALGORITMA
        // ==========================================================================================================================================================

        // $weeks = count($stockOutHistory);
        // if ($weeks < 10 || count($stockInHistory) !== $weeks) {
        //     return 'Uncategorized';
        // }

        // $totalIn = array_sum($stockInHistory);
        // $totalOut = array_sum($stockOutHistory);
        // $averageOut = $totalOut > 0 ? $totalOut / $weeks : 0;

        // $usedWeeks = count(array_filter($stockOutHistory, fn($v) => $v > 0));
        // $usedPercent = $usedWeeks / $weeks;

        // $inWeeks = count(array_filter($stockInHistory, fn($v) => $v > 0));
        // $inPercent = $inWeeks / $weeks;

        // $nonZeroCount = count(array_filter($stockOutHistory));
        // $isEarlyOut = ($nonZeroCount <= 4) && (array_sum(array_slice($stockOutHistory, $nonZeroCount)) === 0;

        // if ($totalIn == 0 && $totalOut == 0 && $stockNow == 0) {
        //     return 'Uncategorized';
        // }

        // if ($stockNow == 0 && ($totalIn > 0 || $totalOut > 10)) {
        //     return 'High Demand';
        // }

        // if ($stockNow > 0 && $totalIn == 0 && $totalOut == 0) {
        //     return 'Dormant';
        // }

        // $lowActivity = ($totalOut <= 2 && $totalIn <= 2 && $usedPercent <= 0.25);
        // $earlyActivity = ($isEarlyOut && $totalOut <= 5 && $totalIn <= 5);
        // if ($stockNow > 0 && ($lowActivity || $earlyActivity)) {
        //     return 'Dormant';
        // }

        // $overstockByUsage = ($totalOut > 0 && $stockNow > ($averageOut * $weeks * 2));
        // $overstockByInput = ($totalOut == 0 && $totalIn > 0 && $stockNow > $totalIn * 2);
        // if ($overstockByUsage || $overstockByInput) {
        //     return 'Overstock';
        // }

        // $lowUsage = ($averageOut > 0 && $averageOut <= 2.5);
        // $regularUse = ($usedPercent >= 0.4 && $usedPercent <= 0.8);
        // $adequateStock = ($stockNow <= ($averageOut * 6));
        // if ($lowUsage && $regularUse && $adequateStock) {
        //     return 'Low Demand';
        // }

        // $sparseUse = ($usedPercent <= 0.3);
        // $significantUse = ($averageOut >= 2);
        // if ($sparseUse && $significantUse && $totalOut > 0) {
        //     return 'Seasonal';
        // }

        // if ($totalOut > 0) {
        //     return $averageOut <= 1 ? 'Low Demand' : 'Stable';
        // }
        // if ($totalIn > 0) {
        //     return $stockNow > $totalIn * 2 ? 'Overstock' : 'Stable';
        // }

        // return 'Uncategorized';
    }
}