<?php

namespace App\Http\Controllers\Sites;

use App\Http\Controllers\Controller;
use App\Models\LogAktivitas;
use Illuminate\Http\Request;
use App\Models\Part;
use App\Models\Site;
use App\Models\PartInventory;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Illuminate\View\View;

class partprioritasController extends Controller
{
    public function index(Request $request): View
    {
        $siteId = session('site_id');
        $sites = Site::all();
        $selectedSite = $siteId;
        $parts = PartInventory::with(['part', 'site'])
            ->where('site_id', $siteId)->orderBy('updated_at', 'desc') // Filter berdasarkan site_id yang login
            ->paginate(20);
        return view('sites.partprioritassite', compact('sites', 'parts', 'selectedSite'));
    }
    public function autocomplete(Request $request)
    {
        $search = $request->query('search');

        $parts = PartInventory::with([
            'part' => function ($query) use ($search) {
                $query->where('part_code', 'LIKE', "%$search%")
                    ->orWhere('part_name', 'LIKE', "%$search%");
            }
        ])
            ->whereHas('part', function ($query) use ($search) {
                $query->where('part_code', 'LIKE', "%$search%")
                    ->orWhere('part_name', 'LIKE', "%$search%");
            })
            ->limit(10)
            ->get();

        // Format hasil agar flat (gabungkan data dari relasi `part` dan `partinventory`)
        $result = $parts->map(function ($item) {
            return [
                'part_code' => $item->part->part_code,
                'part_name' => $item->part->part_name,
                'eum' => $item->part->eum,
                'min_stock' => $item->min_stock,
                'max_stock' => $item->max_stock,
                'oum' => $item->oum ?? $item->part->eum,
            ];
        });

        return response()->json($result);
    }

    public function filter(Request $request)
    {
        try {
            $siteId = session('site_id');
            $search = $request->query('search');
            $page = $request->query('page', 1);
            $perPage = 10;

            $query = PartInventory::with(['part', 'site'])
                ->where('site_id', $siteId);

            // Add search functionality - search in both original part name and site-specific part name
            if ($search) {
                $query->where(function ($q) use ($search) {
                    // Search in site_part_name column of part_inventories table
                    $q->where('site_part_name', 'like', "%{$search}%")
                        // Or search in part_name/part_code columns of parts table
                        ->orWhereHas('part', function ($subQ) use ($search) {
                            $subQ->where('part_name', 'like', "%{$search}%")
                                ->orWhere('part_code', 'like', "%{$search}%");
                        });
                });
            }

            $parts = $query->orderBy('updated_at', 'desc')
                ->paginate($perPage);

            return response()->json([
                'tableRows' => view('parts.partgroup-table-rows', compact('parts'))->render(),
                'pagination' => [
                    'current_page' => $parts->currentPage(),
                    'last_page' => $parts->lastPage(),
                    'total' => $parts->total(),
                    'per_page' => $parts->perPage()
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Error in filter function: ' . $e->getMessage());
            return response()->json(['message' => 'Error: ' . $e->getMessage()], 500);
        }
    }
    private function extractTableRows(string $view): string
    {
        $start = strpos($view, '<tbody');
        $end = strpos($view, '</tbody>') + 8;
        return substr($view, $start, $end - $start);
    }
    public function store(Request $request)
    {
        try {
            $data = $request->json()->all();
            $validator = Validator::make($data, [
                'part_code' => 'required|exists:parts,part_code',
                'site_part_name' => 'nullable|string|max:255',
                'min_stock' => 'required|integer|min:0',
                'max_stock' => 'required|integer|min:0',
                'oum' => 'required|string|max:225',
                'price' => 'nullable|numeric|min:0',
            ]);
            if ($validator->fails()) {
                return response()->json(['errors' => $validator->errors()], 422);
            }
            $partCode = $data['part_code'];
            $siteId = session('site_id');
            $sitePartName = $data['site_part_name'] ?? null;
            $minStock = $data['min_stock'];
            $oum = $data['oum'];
            $maxStock = $data['max_stock'];
            $price = $data['price'] ?? 0;

            PartInventory::updateOrCreate(
                ['part_code' => $partCode, 'site_id' => $siteId],
                [
                    'site_part_name' => $sitePartName,
                    'min_stock' => $minStock,
                    'max_stock' => $maxStock,
                    'oum' => $oum,
                    'price' => $price
                ]
            );

            // Log Actifitas
            $partname = Part::where('part_code', $partCode)->first()->part_name;
            $displayName = $sitePartName ? "$partname (Site Name: $sitePartName)" : $partname;

            LogAktivitas::create([
                'site_id' => session('site_id'),
                'name' => session('name'),
                'action' => 'Mengubah data Part',
                'description' => "User " . session('name') . " mengubah Data Part " . $displayName . " min = " . $minStock . " max = " . $maxStock . " oum = " . $oum . " harga = " . $price,
                'ip_address' => $request->ip(),
                'table' => "Part Inventory",
            ]);

            return response()->json(['message' => 'Data berhasil disimpan']);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Error: ' . $e->getMessage()], 500);
        }
    }

    public function destroy(PartInventory $partInventory)
    {
        try {
            // Check if there are related records in site_in_stocks or other tables
            if ($partInventory->siteInStocks()->count() > 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'Tidak dapat menghapus data karena masih digunakan pada transaksi In Stock'
                ], 400);
            }

            if ($partInventory->siteOutStocks()->count() > 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'Tidak dapat menghapus data karena masih digunakan pada transaksi Out Stock'
                ], 400);
            }

            if ($partInventory->warehouseInStocks()->count() > 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'Tidak dapat menghapus data karena masih digunakan pada transaksi Warehouse In Stock'
                ], 400);
            }

            if ($partInventory->warehouseOutStocks()->count() > 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'Tidak dapat menghapus data karena masih digunakan pada transaksi Warehouse Out Stock'
                ], 400);
            }

            $partInventory->delete();
            return response()->json([
                'success' => true,
                'message' => 'Data berhasil dihapus'
            ]);
        } catch (\PDOException $e) {
            // Check for foreign key constraint violation
            if ($e->getCode() == 23000) {
                return response()->json([
                    'success' => false,
                    'message' => 'Tidak dapat menghapus data karena masih digunakan pada tabel lain'
                ], 400);
            }

            return response()->json([
                'success' => false,
                'message' => 'Gagal menghapus data. Silahkan coba beberapa saat lagi.'
            ], 500);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal menghapus data. Silahkan coba beberapa saat lagi.'
            ], 500);
        }
    }

    public function getSites(Request $request)
    {
        $sites = Site::all();
        return response()->json($sites);
    }
    public function getPartInventoryData(Request $request)
    {
        $siteId = session('site_id');
        $page = $request->input('page', 1);
        $perPage = $request->input('per_page', 15); // Default to 15 items per page
        $searchName = $request->input('search_name');
        $searchStock = $request->input('search_stock');
        $partType = $request->input('part_type');

        $query = PartInventory::with(['part', 'site'])
            ->where('site_id', $siteId);

        // Filter by part name - search in both original part name and site-specific part name
        if ($searchName) {
            $query->where(function ($q) use ($searchName) {
                // Search in site_part_name column of part_inventories table
                $q->where('site_part_name', 'like', "%{$searchName}%")
                    // Or search in part_name/part_code columns of parts table
                    ->orWhereHas('part', function ($subQ) use ($searchName) {
                        $subQ->where('part_name', 'like', "%{$searchName}%")
                            ->orWhere('part_code', 'like', "%{$searchName}%");
                    });
            });
        }

        // Filter by stock quantity
        if ($searchStock !== null && $searchStock !== '') {
            $query->where('stock_quantity', '=', $searchStock);
        }

        // Filter by part type
        if ($partType && $partType !== 'all') {
            $query->whereHas('part', function ($q) use ($partType) {
                $q->where('part_type', $partType);
            });
        }

        $query->latest('updated_at');

        // Get total count for pagination
        $total = $query->count();

        // Get paginated data
        $parts = $query->skip(($page - 1) * $perPage)
            ->take($perPage)
            ->get();

        return response()->json([
            'data' => $parts,
            'current_page' => (int) $page,
            'per_page' => (int) $perPage,
            'last_page' => ceil($total / $perPage),
            'total' => $total
        ]);
    }
}

