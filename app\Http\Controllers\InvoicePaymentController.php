<?php

namespace App\Http\Controllers;

use App\Models\Invoice;
use App\Helpers\LogHelper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class InvoicePaymentController extends Controller
{
    /**
     * Update the payment status of an invoice
     */
    public function updatePaymentStatus(Request $request)
    {
        try {
            // Validate the request
            $request->validate([
                'invoice_id' => 'required|exists:invoices,id',
                'payment_status' => 'required|in:Lunas,Belum Lunas,Draf',
                'payment_date' => 'nullable|date',
                'payment_notes' => 'nullable|string',
                'document' => 'nullable|file|max:10240|mimes:pdf,jpg,jpeg,png,doc,docx'
            ], [
                'document.max' => 'Ukuran file tidak boleh lebih dari 10MB',
                'document.mimes' => 'Format file harus PDF, JPG, JPEG, PNG, DOC, atau DOCX'
            ]);

            // Find the invoice
            $invoice = Invoice::findOrFail($request->invoice_id);

            // Check if status is being changed to Lunas and document is required
            if ($request->payment_status === 'Lunas' && !$request->hasFile('document') && !$invoice->document_path) {
                return response()->json([
                    'success' => false,
                    'message' => 'Dokumen invoice asli harus dilampirkan saat mengubah status menjadi Lunas',
                    'errors' => ['document' => ['Dokumen invoice asli harus dilampirkan']]
                ], 422);
            }

            // Handle document upload if present
            if ($request->hasFile('document')) {
                // Delete old file if it exists
                if ($invoice->document_path && file_exists(public_path('assets/invoice_documents/' . $invoice->document_path))) {
                    unlink(public_path('assets/invoice_documents/' . $invoice->document_path));
                }

                // Upload new file
                $file = $request->file('document');
                $fileName = time() . '_' . $file->getClientOriginalName();
                $file->move(public_path('assets/invoice_documents'), $fileName);

                // Save document path as signed_document_path instead of document_path
                // This ensures it only appears on the Invoices page and not on the dashboard
                $invoice->signed_document_path = $fileName;

                // If there was a previous document_path, clear it to ensure it doesn't show on dashboard
                $invoice->document_path = null;
            }

            // Update the payment status
            $invoice->payment_status = $request->payment_status;
            $invoice->payment_date = $request->payment_date;
            $invoice->payment_notes = $request->payment_notes;

            // If status is Lunas, also update the invoice status to Selesai
            if ($request->payment_status === 'Lunas') {
                $invoice->status = 'Selesai';
            }

            $invoice->save();

            // Reload the invoice with its relationships
            $invoice = Invoice::with(['unitTransactions.unit', 'unitTransactions.parts.partInventory.part'])
                ->findOrFail($request->invoice_id);

            // Log the action
            LogHelper::createLog(
                'Update Payment Status',
                'Status pembayaran invoice ' . $invoice->no_invoice . ' diubah menjadi ' . $request->payment_status,
                'Invoice',
                $request
            );

            return response()->json([
                'success' => true,
                'message' => 'Status pembayaran berhasil diperbarui',
                'invoice' => $invoice
            ]);
        } catch (\Exception $e) {
            // Log the error
            Log::error('Error updating payment status: ' . $e->getMessage());
            Log::error($e->getTraceAsString());

            // Return error response
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ], 500);
        }
    }
}
