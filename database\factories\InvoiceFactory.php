<?php

namespace Database\Factories;

use App\Models\Invoice;
use Illuminate\Database\Eloquent\Factories\Factory;

class InvoiceFactory extends Factory
{
    protected $model = Invoice::class;

    public function definition()
    {
        return [
            'customer' => $this->faker->company,
            'customer_code' => $this->faker->regexify('[A-Z]{3}[0-9]{3}'),
            'location' => $this->faker->city,
            'no_invoice' => $this->faker->unique()->regexify('INV-[0-9]{6}'),
            'po_number' => $this->faker->regexify('PO-[0-9]{6}'),
            'sn' => $this->faker->regexify('[A-Z]{2}[0-9]{6}'),
            'trouble' => $this->faker->sentence,
            'lokasi' => $this->faker->address,
            'model_unit' => $this->faker->word,
            'hmkm' => $this->faker->numberBetween(1000, 10000),
            'tanggal_invoice' => $this->faker->date(),
            'due_date' => $this->faker->dateTimeBetween('+1 week', '+1 month'),
            'ppn' => 0.11,
            'payment_status' => $this->faker->randomElement(['Lunas', 'Belum Lunas', 'Draf']),
            'payment_date' => null,
            'payment_notes' => null,
            'notes' => $this->faker->sentence,
            'transfer_to' => null,
            'bank_account' => null,
            'bank_branch' => null,
            'signed_document_path' => null,
            'document_path' => null,
            'status' => 'Draft',
            'penawaran_id' => null,
            'site_id' => 'TST',
            'direct_subtotal' => null,
        ];
    }
}
