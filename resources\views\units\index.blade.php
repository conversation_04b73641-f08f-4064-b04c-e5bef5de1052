@extends('sites.content')
@section('contentsite')
@section('title', 'Units Management')
<meta name="csrf-token" content="{{ csrf_token() }}">
<!-- mulai content disin -->
<div class="row bgwhite page-title-box shadow-kit mb-1">
    <div class="col d-flex align-items-center" style="max-width: fit-content;">
        <button class="button-menu-mobile text-blue-500 btn-sm">
            <i style="font-size: 30px;" class="mdi mdi-menu"></i>
        </button>
        <div class="text">
            <p class="font-bold m-0">{{ session('name') }}</p>
        </div>
    </div>
    <div class="col">
        <ul class="list-unstyled topnav-menu float-right mb-0">
            <li class="dropdown notification-list">
                <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="mdi mdi-bell-outline noti-icon"></i>
                    <span class="badge badge-danger badge-pill float-right badgenotif"></span>
                </a>
                <div class="dropdown-menu dropdown-menu-end dropdown-lg">
                    <div class="dropdown-item noti-title bg-primary">
                        <h5 class="m-0 text-white d-flex justify-content-between align-items-center">
                            Notifikasi
                            <a href="javascript:void(0);" class="text-white" id="clear-all">
                                <small></small>
                            </a>
                        </h5>
                    </div>
                    <div class="noti-scroll" id="notification-list">
                        <!-- Notifications will be dynamically inserted here -->
                    </div>
                </div>
            </li>
        </ul>
    </div>
</div>
<div class="row m-3">
    <div class="col">
        <!-- Units Table -->
        <div class="bg-white p-4 rounded shadow-kit">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h1 class="text-xl font-bold">Unit Terdaftar</h1>
                <div class="d-flex">
                    <input type="text" id="unit-search" class="form-control form-control-sm" placeholder="Search unit code or type...">
                </div>
            </div>
            <hr>
            <table class="w-full">
                <thead>
                    <tr class="bg-gray-100">
                        <th class="px-4 py-2">Unit Code</th>
                        <th class="px-4 py-2">Unit Type</th>
                        <th class="px-4 py-2">Site</th>
                        <th class="px-4 py-2">Aksi</th>
                    </tr>
                </thead>
                <tbody id="unitsTable"></tbody>
            </table>
            <div id="unitsPagination" class="p-4 flex justify-center"></div>
        </div>
    </div>
    <div class="col">
        <!-- Combined Unit and Part Form -->
        <div class="bg-white p-4 rounded shadow-kit">
            <h1 class="text-xl font-bold">Units</h1>
            <hr>
            <form id="combinedUnitForm">
                @csrf
                <input type="hidden" id="unitId" name="id">
                <input type="hidden" id="site_id" name="site_id" value="{{ $site_id }}">

                <!-- Unit Information Section -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-1">
                    <div>
                        <label for="unit_code" class="block mb-2">Unit Code</label>
                        <input type="text" id="unit_code" name="unit_code" class="w-full p-2 border rounded" required>
                    </div>
                    <div>
                        <label for="unit_type" class="block mb-2">Unit Type</label>
                        <input type="text" id="unit_type" name="unit_type" class="w-full p-2 border rounded" required>
                    </div>
                </div>

                <hr class="my-4">
                <!-- Part Form -->
                <div class="mb-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="part_search" class="block mb-2">Nama Part</label>
                            <div class="relative">
                                <input type="text" id="part_search" class="w-full p-2 border rounded" placeholder="Search part by name or code">
                                <input type="hidden" id="part_inventory_id" name="part_inventory_id">
                                <div id="part_suggestions" class="absolute z-10 w-full bg-white border border-gray-300 mt-1 rounded max-h-60 overflow-y-auto hidden"></div>
                            </div>
                        </div>
                        <div>
                            <label for="quantity" class="block mb-2">Jumlah Part Dalam Units</label>
                            <input type="number" id="quantity" name="quantity" class="w-full p-2 border rounded" value="1">
                        </div>
                        <div>
                            <label for="price" class="block mb-2">Harga (Rp)</label>
                            <input type="text" id="price_display" class="w-full p-2 border rounded" placeholder="Rp 0">
                            <input type="hidden" id="price" name="price" value="0">
                        </div>
                        <div>
                            <label for="eum" class="block mb-2">EUM</label>
                            <input type="text" id="eum" name="eum" class="w-full p-2 border rounded" value="AE">
                        </div>
                    </div>
                    <div class="mt-3">
                        <button type="button" id="addPartToList" class="bg-green-500 btn-sm text-white rounded">Tambah Part</button>
                    </div>
                </div>

                <!-- Combined Parts List -->
                <div class="mb-4">
                    <h3 id="partsListTitle" class="text-md font-bold mb-2">LIST Parts</h3>
                    <div id="combinedPartsList" class="border rounded p-2 min-h-[200px]">
                        <p id="emptyPartsMessage" class="text-gray-500 text-center py-2">Belum ada part yang ditambahkan</p>

                        <!-- Existing Parts Table -->
                        <div id="existingPartsContainer" class="mb-4 hidden">
                            <h4 class="text-sm font-semibold mb-2">Parts yang sudah ada</h4>
                            <table class="w-full">
                                <thead>
                                    <tr class="bg-gray-100">
                                        <th class="px-4 py-2">Part Name</th>
                                        <th class="px-4 py-2">Jumlah</th>
                                        <th class="px-4 py-2">Harga</th>
                                        <th class="px-4 py-2">EUM</th>
                                        <th class="px-4 py-2">Aksi</th>
                                    </tr>
                                </thead>
                                <tbody id="partsTable"></tbody>
                            </table>
                            <div id="partsPagination" class="p-4 flex justify-center"></div>
                        </div>

                        <!-- New Parts Table -->
                        <div id="newPartsContainer" class="hidden">
                            <h4 class="text-sm font-semibold mb-2">Parts yang ditambahkan</h4>
                            <table id="temporaryPartsTable" class="w-full">
                                <thead>
                                    <tr class="bg-gray-100">
                                        <th class="px-4 py-2">Part Name</th>
                                        <th class="px-4 py-2">Jumlah</th>
                                        <th class="px-4 py-2">Harga</th>
                                        <th class="px-4 py-2">EUM</th>
                                        <th class="px-4 py-2">Aksi</th>
                                    </tr>
                                </thead>
                                <tbody id="temporaryPartsTableBody"></tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="mt-4">
                    <button type="submit" class="bg-blue-500 btn-sm text-white rounded">Simpan Perubahan</button>
                    <button type="button" onclick="resetCombinedForm()" class="bg-gray-500 btn-sm text-white rounded">Cancel</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
@section('resourcesite')
@vite(['resources/js/units-combined.js', 'resources/js/style.js'])
@endsection