<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Export Price List - Portal PWB</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 10px;
            margin: 0;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #333;
            padding-bottom: 10px;
        }
        
        .header h1 {
            margin: 0;
            font-size: 18px;
            color: #333;
        }
        
        .header p {
            margin: 5px 0;
            color: #666;
        }
        
        .filters {
            margin-bottom: 15px;
            padding: 10px;
            background-color: #f5f5f5;
            border-radius: 5px;
        }
        
        .filters h3 {
            margin: 0 0 10px 0;
            font-size: 12px;
            color: #333;
        }
        
        .filter-item {
            display: inline-block;
            margin-right: 20px;
            margin-bottom: 5px;
        }
        
        .filter-label {
            font-weight: bold;
            color: #555;
        }
        
        .filter-value {
            color: #333;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 6px;
            text-align: left;
            font-size: 9px;
        }
        
        th {
            background-color: #4472C4;
            color: white;
            font-weight: bold;
            text-align: center;
        }
        
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .text-center {
            text-align: center;
        }
        
        .text-right {
            text-align: right;
        }
        
        .currency {
            text-align: right;
            font-family: monospace;
        }
        
        .footer {
            margin-top: 20px;
            text-align: center;
            font-size: 8px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }
        
        .no-data {
            text-align: center;
            padding: 20px;
            color: #666;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Export Data Price List</h1>
        <p>Portal PWB - Superadmin</p>
        <p>Tanggal Export: {{ $exportDate }}</p>
    </div>

    <div class="filters">
        <h3>Filter yang Diterapkan:</h3>
        <div class="filter-item">
            <span class="filter-label">Site:</span>
            <span class="filter-value">{{ $filters['site_id'] ?: 'Semua Site' }}</span>
        </div>
        <div class="filter-item">
            <span class="filter-label">Tipe Part:</span>
            <span class="filter-value">{{ $filters['part_type'] == 'all' ? 'Semua Tipe' : $filters['part_type'] }}</span>
        </div>
        <div class="filter-item">
            <span class="filter-label">Pencarian:</span>
            <span class="filter-value">{{ $filters['search'] ?: 'Tidak ada' }}</span>
        </div>
    </div>

    @if(count($priceList) > 0)
        <table>
            <thead>
                <tr>
                    <th style="width: 10%;">Site</th>
                    <th style="width: 15%;">Part Code</th>
                    <th style="width: 30%;">Part Name</th>
                    <th style="width: 15%;">Type</th>
                    <th style="width: 15%;">Harga Beli</th>
                    <th style="width: 15%;">Harga Jual</th>
                </tr>
            </thead>
            <tbody>
                @foreach($priceList as $part)
                    <tr>
                        <td class="text-center">{{ $part['site_id'] }}</td>
                        <td>{{ $part['part_code'] }}</td>
                        <td>{{ $part['part_name'] }}</td>
                        <td>{{ $part['part_type'] }}</td>
                        <td class="currency">Rp {{ number_format($part['purchase_price'], 0, ',', '.') }}</td>
                        <td class="currency">Rp {{ number_format($part['price'], 0, ',', '.') }}</td>
                    </tr>
                @endforeach
            </tbody>
        </table>
        
        <div style="margin-top: 15px; font-size: 10px;">
            <strong>Total Data: {{ count($priceList) }} part</strong>
        </div>
    @else
        <div class="no-data">
            <p>Tidak ada data price list yang ditemukan dengan filter yang diterapkan.</p>
        </div>
    @endif

    <div class="footer">
        <p>Dokumen ini dibuat secara otomatis oleh sistem Portal PWB</p>
        <p>© {{ date('Y') }} Portal PWB - PT. Panca Wira Buana</p>
    </div>
</body>
</html>
