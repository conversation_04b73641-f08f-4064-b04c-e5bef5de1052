import $ from "jquery";

$(document).ready(function () {
    fetchMidlifeData();
    
    // Real-time filtering with debounce
    let debounceTimer;
    const debounceTime = 500;
    
    $("#partFilter, #unitFilter").on('input', function() {
        clearTimeout(debounceTimer);
        $("#loading-spinner").show();
        debounceTimer = setTimeout(() => {
            fetchMidlifeData();
        }, debounceTime);
    });
});

function fetchMidlifeData() {
    const partFilter = $("#partFilter").val();
    const unitFilter = $("#unitFilter").val();

    $.ajax({
        url: "/midlife/getadatamidlife",
        method: "GET",
        data: {
            part: partFilter,
            unit: unitFilter,
        },
        dataType: "json",
        success: function (response) {
            const container = $("#sectioncards");
            container.empty();

            if (response.length === 0) {
                container.append(
                    '<div class="alert alert-info">Tidak ada data yang ditemukan.</div>'
                );
                return;
            }

            response.forEach((group, index) => {
                const hue = (index * 80) % 360;
                const bgColor = `hsl(${hue}, 90%, 89%)`;

                const rows = group.items
                    .map(
                        (item) => `
                    <tr>
                        <td>${formatTanggalIndonesia(item.tanggalin)}</td>  
                        <td>${item.hm}</td>
                        <td>${item.unit}</td>
                        <td>${item.problem}</td>
                    </tr>`
                    )
                    .join("");

                const card = `
                    <div class="shadow-kit mb-3 card-table mr-3">
                         <div style="background-color: ${bgColor};" class="px-4 pt-3 pb-2">
                            <h5 class="text-dark font-bold">${group.part_name}</h5>
                            <h5 class="text-dark">${group.code_part}</h5>
                        </div>
                        <div class="px-3">
                            <table class="table table-sm table-striped table-sortable mt-3" data-index="${index}" data-items='${JSON.stringify(group.items)}'>
                                <thead>
                                    <tr>
                                        <th class="py-2 px-1" data-key="tanggalin">Tanggal Masuk <span class="mdi mdi-sort-descending pl-3 pr-2 "></span></th>
                                        <th class="py-2 px-1" data-key="hm">HM <span class="mdi mdi-sort-descending pl-3 pr-2"></span></th>
                                        <th class="py-2 px-1" data-key="unit">UNIT <span class="mdi mdi-sort-descending pl-3 pr-2"></span></th>
                                        <th class="py-2 px-1" data-key="problem">Problem <span class="mdi mdi-sort-descending pl-3 pr-2"></span></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${rows}
                                </tbody>
                            </table>
                        </div>
                    </div>`;

                container.append(card);

                $(`table.table-sortable[data-index="${index}"] thead th`)
                    .off("click")
                    .on("click", function () {
                        const th = $(this);
                        const key = th.data("key");
                        const table = th.closest("table");
                        let items = JSON.parse(table.attr("data-items"));

                        const asc = th.data("sort") !== "asc";
                        th.data("sort", asc ? "asc" : "desc");
                        th.siblings().removeAttr("data-sort");

                        items.sort((a, b) => {
                            let valA = a[key];
                            let valB = b[key];

                            if (key === "tanggalin") {
                                valA = new Date(valA);
                                valB = new Date(valB);
                            }

                            if (valA < valB) return asc ? -1 : 1;
                            if (valA > valB) return asc ? 1 : -1;
                            return 0;
                        });

                        table.attr("data-items", JSON.stringify(items));

                        const tbody = table.find("tbody");
                        const newRows = items
                            .map(
                                (item) => `
                        <tr>
                            <td>${formatTanggalIndonesia(item.tanggalin)}</td>
                            <td>${item.hm}</td>
                            <td>${item.unit}</td>
                            <td>${item.problem}</td>
                        </tr>`
                            )
                            .join("");
                        tbody.html(newRows);
                    });
            });
        },
        error: function (error) {
            console.error("Error fetching midlife data:", error);
            $("#sectioncards").html(
                '<div class="alert alert-danger">Gagal memuat data.</div>'
            );
        },
        complete: function() {
            $("#loading-spinner").hide();
        }
    });
}

function formatTanggalIndonesia(tanggal) {
    if (!tanggal) return "N/A";

    const bulanIndonesia = [
        "Januari",
        "Februari",
        "Maret",
        "April",
        "Mei",
        "Juni",
        "Juli",
        "Agustus",
        "September",
        "Oktober",
        "November",
        "Desember",
    ];

    const dateObj = new Date(tanggal);
    if (isNaN(dateObj)) return "N/A";

    const tanggalNum = dateObj.getDate();
    const bulan = bulanIndonesia[dateObj.getMonth()];
    const tahun = dateObj.getFullYear();

    return `${tanggalNum} ${bulan} ${tahun}`;
}

function groupBy(array, key) {
    return array.reduce((result, currentValue) => {
        (result[currentValue[key]] = result[currentValue[key]] || []).push(
            currentValue
        );
        return result;
    }, {});
}