/**
 * Dashboard Unit Site JavaScript
 * Handles AJAX functionality for unit dashboard
 */

class DashboardUnitSite {
    constructor() {
        this.selectedUnit = null;
        this.charts = {};
        this.searchTimeout = null;
        this.init();
    }

    init() {
        this.bindEvents();
        console.log(reload);
        
        this.initializeComponents();
    }

    bindEvents() {
        // Unit search events
        $('#unit-search-input').on('input', (e) => this.handleSearchInput(e));
        $('#unit-search-input').on('focus', (e) => this.handleSearchFocus(e));
        $('#unit-search-input').on('blur', (e) => this.handleSearchBlur(e));
        $('#unit-search-input').on('keydown', (e) => this.handleSearchKeydown(e));
        $(document).on('click', '.unit-search-result', (e) => this.selectUnit(e));
        $(document).on('mouseenter', '.unit-search-result', (e) => this.highlightResult(e));

        // Change unit button
        $('#change-unit-btn').on('click', () => this.showUnitSearch());

        // Daily report table row click
        $(document).on('click', '.daily-report-row', (e) => this.showDailyReportDetail(e));

        // Close search results when clicking outside
        $(document).on('click', (e) => {
            if (!$(e.target).closest('#unit-search-input, .unit-search-dropdown').length) {
                this.hideSearchResults();
            }
        });

        // Prevent page scroll on modal interactions
        $(document).on('show.bs.modal', '.modal', function(e) {
            $('body').addClass('modal-open');
        });

        $(document).on('hidden.bs.modal', '.modal', function(e) {
            $('body').removeClass('modal-open');
        });
    }

    initializeComponents() {
        // Initialize tooltips
        $('[data-bs-toggle="tooltip"]').tooltip();
        
        // Show empty state initially
        this.showEmptyState();
    }

    handleSearchInput(e) {
        const query = e.target.value.trim();

        // Clear previous timeout
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }

        if (query.length >= 1) {
            // Show loading indicator
            this.showSearchLoading();

            // Debounce search with shorter delay for better UX
            this.searchTimeout = setTimeout(() => {
                this.searchUnits(query);
            }, 200);
        } else {
            this.hideSearchResults();
            this.hideSearchLoading();
        }
    }

    handleSearchFocus(e) {
        const query = e.target.value.trim();
        if (query.length >= 1) {
            // Show existing results if available
            if ($('#unit-search-results .unit-search-result').length > 0) {
                this.showSearchResults();
            }
        }
    }

    handleSearchBlur(e) {
        // Delay hiding to allow clicking on results
        setTimeout(() => {
            if (!$('#unit-search-results:hover').length) {
                this.hideSearchResults();
            }
        }, 150);
    }

    async searchUnits(query) {
        try {
            const response = await fetch('/dashboard-unit-site/search-units', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                body: JSON.stringify({ query })
            });

            this.hideSearchLoading();

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (data.success) {
                this.displaySearchResults(data.data);
            } else {
                this.displaySearchResults([]);
                console.warn('Search failed:', data.message);
            }
        } catch (error) {
            console.error('Search error:', error);
            this.hideSearchLoading();
            this.displaySearchResults([]);
            this.showError('Terjadi kesalahan saat mencari unit');
        }
    }

    displaySearchResults(units) {
        const resultsContainer = $('#unit-search-results');

        if (units.length === 0) {
            resultsContainer.html('<div class="no-results">Tidak ada unit ditemukan</div>');
        } else {
            const html = units.map(unit => `
                <div class="unit-search-result" data-unit-id="${unit.id}">
                    <div class="unit-code">${unit.unit_code}</div>
                    <div class="unit-details">${unit.unit_model} - ${unit.unit_type}</div>
                    <div class="unit-site">Site: ${unit.site_id}</div>
                </div>
            `).join('');

            resultsContainer.html(html);
        }

        this.showSearchResults();
    }

    showSearchResults() {
        $('#unit-search-results').show();
    }

    hideSearchResults() {
        $('#unit-search-results').hide();
    }

    showSearchLoading() {
        $('#search-loading').show();
        $('.search-icon').hide();
    }

    hideSearchLoading() {
        $('#search-loading').hide();
        $('.search-icon').show();
    }

    handleSearchKeydown(e) {
        const results = $('.unit-search-result');
        const highlighted = $('.unit-search-result.highlighted');

        switch(e.key) {
            case 'ArrowDown':
                e.preventDefault();
                if (results.length > 0) {
                    if (highlighted.length === 0) {
                        results.first().addClass('highlighted');
                    } else {
                        const next = highlighted.next('.unit-search-result');
                        highlighted.removeClass('highlighted');
                        if (next.length > 0) {
                            next.addClass('highlighted');
                        } else {
                            results.first().addClass('highlighted');
                        }
                    }
                }
                break;

            case 'ArrowUp':
                e.preventDefault();
                if (results.length > 0) {
                    if (highlighted.length === 0) {
                        results.last().addClass('highlighted');
                    } else {
                        const prev = highlighted.prev('.unit-search-result');
                        highlighted.removeClass('highlighted');
                        if (prev.length > 0) {
                            prev.addClass('highlighted');
                        } else {
                            results.last().addClass('highlighted');
                        }
                    }
                }
                break;

            case 'Enter':
                e.preventDefault();
                if (highlighted.length > 0) {
                    this.selectUnit({ currentTarget: highlighted[0], preventDefault: () => {} });
                }
                break;

            case 'Escape':
                this.hideSearchResults();
                $('#unit-search-input').blur();
                break;
        }
    }

    highlightResult(e) {
        $('.unit-search-result').removeClass('highlighted');
        $(e.currentTarget).addClass('highlighted');
    }

    async selectUnit(e) {
        e.preventDefault();
        const unitId = $(e.currentTarget).data('unit-id');
        const unitCode = $(e.currentTarget).find('.unit-code').text();

        this.hideSearchResults();
        $('#unit-search-input').val(unitCode);

        await this.loadDashboardData(unitId);

        return false;
    }

    async loadDashboardData(unitId) {
        try {
            this.showLoading();
            
            const response = await fetch('/dashboard-unit-site/dashboard-data', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                body: JSON.stringify({ unit_id: unitId })
            });

            const data = await response.json();
            
            if (data.success) {
                this.selectedUnit = data.data.unit;
                this.renderDashboard(data.data);
                this.showDashboard();
            } else {
                this.showError('Gagal memuat data dashboard: ' + data.message);
                this.showEmptyState();
            }
        } catch (error) {
            console.error('Dashboard load error:', error);
            this.showError('Terjadi kesalahan saat memuat dashboard');
            this.showEmptyState();
        }
    }

    renderDashboard(data) {
        // Update unit header
        $('#selected-unit-title').text(`${data.unit.unit_code} - ${data.unit.unit_model}`);
        $('#selected-unit-info').text(`${data.unit.unit_type} | Site: ${data.unit.site_id}`);
        
        // Render daily reports table
        this.renderDailyReportsTable(data.daily_reports);
        
        // Render latest report
        this.renderLatestReport(data.latest_report);
        
        // Render open backlogs
        this.renderOpenBacklogs(data.open_backlogs);
        
        // Render part usage stats
        this.renderPartUsageStats(data.part_usage_stats);
        
        // Render charts
        this.renderServiceComponentChart(data.service_component_stats);
        this.renderMonthlyUsageChart(data.monthly_usage_stats);
    }

    renderDailyReportsTable(reports) {
        const tbody = $('#daily-reports-table tbody');
        
        if (reports.length === 0) {
            tbody.html('<tr><td colspan="5" class="text-center text-muted">Tidak ada data daily report</td></tr>');
            return;
        }
        
        const html = reports.map(report => `
            <tr class="daily-report-row" data-report-id="${report.daily_report_id}" style="cursor: pointer;">
                <td>${report.date_in}</td>
                <td>${report.hm}</td>
                <td>
                    <span class="badge bg-${this.getProblemBadgeColor(report.problem)}">${report.problem}</span>
                </td>
                <td>${report.shift || '-'}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary">
                        <i class="mdi mdi-eye"></i> Detail
                    </button>
                </td>
            </tr>
        `).join('');
        
        tbody.html(html);
    }

    renderLatestReport(report) {
        const container = $('#latest-report-content');
        
        if (!report) {
            container.html('<p class="text-muted">Tidak ada data report</p>');
            return;
        }
        
        const dtTimeText = report.dt_time ? `${report.dt_time} menit` : 'Tidak tersedia';
        
        const html = `
            <div class="latest-report-card">
                <div class="row mb-3">
                    <div class="col-6">
                        <small class="text-muted">Tanggal</small>
                        <div class="fw-bold">${report.date_in}</div>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">HM</small>
                        <div class="fw-bold">${report.hm}</div>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-12">
                        <small class="text-muted">Problem</small>
                        <div>
                            <span class="badge bg-${this.getProblemBadgeColor(report.problem)}">${report.problem}</span>
                        </div>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-6">
                        <small class="text-muted">Jam Masuk</small>
                        <div>${report.hour_in || '-'}</div>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">Jam Keluar</small>
                        <div>${report.hour_out || '-'}</div>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-12">
                        <small class="text-muted">Downtime</small>
                        <div class="fw-bold text-warning">${dtTimeText}</div>
                    </div>
                </div>
                ${report.problem_description ? `
                <div class="row mb-3">
                    <div class="col-12">
                        <small class="text-muted">Deskripsi</small>
                        <div class="text-wrap">${report.problem_description}</div>
                    </div>
                </div>
                ` : ''}
                <div class="row">
                    <div class="col-12">
                        <small class="text-muted">Jobs (${report.jobs.length})</small>
                        <div class="mt-1">
                            ${report.jobs.map(job => `<span class="badge bg-secondary me-1 mb-1">${job}</span>`).join('')}
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        container.html(html);
    }

    renderOpenBacklogs(backlogs) {
        const container = $('#open-backlogs-content');
        
        if (backlogs.length === 0) {
            container.html('<p class="text-muted">Tidak ada backlog OPEN</p>');
            return;
        }
        
        const html = backlogs.map(backlog => `
            <div class="backlog-item mb-3 p-3 border rounded">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <h6 class="mb-0">${backlog.backlog_job}</h6>
                    <small class="text-muted">${backlog.created_at}</small>
                </div>
                <p class="text-muted mb-2">${backlog.problem_description}</p>
                <div class="row">
                    <div class="col-6">
                        <small class="text-muted">HM Found</small>
                        <div>${backlog.hm_found}</div>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">Plan HM</small>
                        <div>${backlog.plan_hm || '-'}</div>
                    </div>
                </div>
                ${backlog.parts.length > 0 ? `
                <div class="mt-2">
                    <small class="text-muted">Parts:</small>
                    <div class="mt-1">
                        ${backlog.parts.map(part => `
                            <span class="badge bg-info me-1">${part.part_code} (${part.quantity})</span>
                        `).join('')}
                    </div>
                </div>
                ` : ''}
            </div>
        `).join('');
        
        container.html(html);
    }

    renderPartUsageStats(stats) {
        const container = $('#part-usage-content');
        
        if (stats.length === 0) {
            container.html('<p class="text-muted">Tidak ada data penggunaan part</p>');
            return;
        }
        
        const html = stats.map(stat => `
            <div class="d-flex justify-content-between align-items-center mb-2">
                <div>
                    <div class="fw-bold">${stat.part_code}</div>
                    <small class="text-muted">${stat.part_name}</small>
                </div>
                <span class="badge bg-primary">${stat.total_quantity}x</span>
            </div>
        `).join('');
        
        container.html(html);
    }

    renderServiceComponentChart(data) {
        const ctx = document.getElementById('service-component-chart');
        
        // Destroy existing chart
        if (this.charts.serviceComponent) {
            this.charts.serviceComponent.destroy();
        }
        
        if (data.length === 0) {
            ctx.getContext('2d').clearRect(0, 0, ctx.width, ctx.height);
            return;
        }
        
        this.charts.serviceComponent = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: data.map(item => item.component),
                datasets: [{
                    data: data.map(item => item.count),
                    backgroundColor: [
                        '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
                        '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    renderMonthlyUsageChart(data) {
        const ctx = document.getElementById('monthly-usage-chart');
        
        // Destroy existing chart
        if (this.charts.monthlyUsage) {
            this.charts.monthlyUsage.destroy();
        }
        
        if (data.length === 0) {
            ctx.getContext('2d').clearRect(0, 0, ctx.width, ctx.height);
            return;
        }
        
        this.charts.monthlyUsage = new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.map(item => item.period),
                datasets: [{
                    label: 'Jumlah Report',
                    data: data.map(item => item.count),
                    borderColor: '#36A2EB',
                    backgroundColor: 'rgba(54, 162, 235, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    }

    async showDailyReportDetail(e) {
        e.preventDefault();
        const reportId = $(e.currentTarget).data('report-id');
        
        try {
            const response = await fetch('/dashboard-unit-site/daily-report-detail', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                body: JSON.stringify({ report_id: reportId })
            });

            const data = await response.json();
            
            if (data.success) {
                this.renderDailyReportModal(data.data);
                $('#daily-report-modal').modal('show');
            } else {
                this.showError('Gagal memuat detail report: ' + data.message);
            }
        } catch (error) {
            console.error('Report detail error:', error);
            this.showError('Terjadi kesalahan saat memuat detail report');
        }
        
        return false;
    }

    renderDailyReportModal(report) {
        const dtTimeText = report.dt_time ? `${report.dt_time} menit` : 'Tidak tersedia';
        
        const html = `
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label class="form-label">Unit Code</label>
                    <div class="form-control-plaintext">${report.unit_code}</div>
                </div>
                <div class="col-md-6 mb-3">
                    <label class="form-label">Tanggal</label>
                    <div class="form-control-plaintext">${report.date_in}</div>
                </div>
                <div class="col-md-6 mb-3">
                    <label class="form-label">HM</label>
                    <div class="form-control-plaintext">${report.hm}</div>
                </div>
                <div class="col-md-6 mb-3">
                    <label class="form-label">Shift</label>
                    <div class="form-control-plaintext">${report.shift || '-'}</div>
                </div>
                <div class="col-md-4 mb-3">
                    <label class="form-label">Jam Masuk</label>
                    <div class="form-control-plaintext">${report.hour_in || '-'}</div>
                </div>
                <div class="col-md-4 mb-3">
                    <label class="form-label">Jam Keluar</label>
                    <div class="form-control-plaintext">${report.hour_out || '-'}</div>
                </div>
                <div class="col-md-4 mb-3">
                    <label class="form-label">Downtime</label>
                    <div class="form-control-plaintext text-warning fw-bold">${dtTimeText}</div>
                </div>
                <div class="col-12 mb-3">
                    <label class="form-label">Problem</label>
                    <div class="form-control-plaintext">
                        <span class="badge bg-${this.getProblemBadgeColor(report.problem)}">${report.problem}</span>
                    </div>
                </div>
                ${report.problem_component ? `
                <div class="col-12 mb-3">
                    <label class="form-label">Problem Component</label>
                    <div class="form-control-plaintext">${report.problem_component}</div>
                </div>
                ` : ''}
                ${report.problem_description ? `
                <div class="col-12 mb-3">
                    <label class="form-label">Problem Description</label>
                    <div class="form-control-plaintext">${report.problem_description}</div>
                </div>
                ` : ''}
                <div class="col-12 mb-3">
                    <label class="form-label">Jobs</label>
                    <div class="form-control-plaintext">
                        ${report.jobs.map(job => `
                            <span class="badge bg-secondary me-1 mb-1">${job.job_description}</span>
                        `).join('')}
                    </div>
                </div>
                ${report.plan_fix ? `
                <div class="col-12 mb-3">
                    <label class="form-label">Plan Fix</label>
                    <div class="form-control-plaintext">${report.plan_fix}</div>
                </div>
                ` : ''}
                ${report.plan_rekomen ? `
                <div class="col-12 mb-3">
                    <label class="form-label">Plan Rekomendasi</label>
                    <div class="form-control-plaintext">${report.plan_rekomen}</div>
                </div>
                ` : ''}
            </div>
        `;
        
        $('#daily-report-modal-content').html(html);
    }

    showUnitSearch() {
        this.showEmptyState();
        $('#unit-search-input').val('').focus();
    }

    showLoading() {
        $('#empty-state').hide();
        $('#dashboard-content').hide();
        $('#loading-state').show();
    }

    showEmptyState() {
        $('#loading-state').hide();
        $('#dashboard-content').hide();
        $('#empty-state').show();
    }

    showDashboard() {
        $('#loading-state').hide();
        $('#empty-state').hide();
        $('#dashboard-content').show();
    }

    getProblemBadgeColor(problem) {
        switch (problem?.toLowerCase()) {
            case 'schedule': return 'success';
            case 'unschedule': return 'danger';
            case 'hm': return 'warning';
            default: return 'secondary';
        }
    }

    showError(message) {
        // You can implement a toast notification here
        alert(message);
    }
}

// Initialize when document is ready
$(document).ready(function() {
    new DashboardUnitSite();
});
