<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <title><?php echo $__env->yieldContent('title', 'DASHBOARD Site'); ?></title>
    <link rel="shortcut icon" href="<?php echo e(asset('assets/images/logo-small.png')); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <!-- CSS Files -->
    <?php echo app('Illuminate\Foundation\Vite')('resources/assets/css/bootstrap.min.css'); ?>
    <?php echo app('Illuminate\Foundation\Vite')('resources/assets/css/icons.min.css'); ?>
    <?php echo app('Illuminate\Foundation\Vite')('resources/assets/css/app.min.css'); ?>
    <?php echo app('Illuminate\Foundation\Vite')('resources/css/app.css'); ?>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jQuery-slimScroll/1.3.8/jquery.slimscroll.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/metisMenu/3.0.7/metisMenu.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</head>

<body>
    <div id="wrapper">
        <div class="left-side-menu" style="top: 0 !important;">
            <div class="slimscroll-menu">
                <div id="sidebar-menu">
                <ul class="metismenu" id="side-menu">
                        <li class="menu-title">
                            Login sebagai <?php echo e(session('role')); ?>

                            <?php if(session('access_mode')): ?>
                            <br><small>Mode: <?php echo e(session('access_mode') == 'inventory' ? 'Inventori' : 'Monthly Report'); ?></small>
                            <?php endif; ?>
                        </li>
                        
                        <?php if(!session('access_mode') || session('access_mode') == 'inventory'): ?>
                        <li>
                            <a href="<?php echo e(route(name: 'sites.dashboard')); ?>">
                                <i class="mdi mdi-home-analytics"></i>
                                <span> Dashboard </span>
                            </a>
                        </li>

                        <!-- Inventory Mode Menu Items -->
                        <li>
                            <a href="<?php echo e(route(name: 'sites.inventory-card')); ?>">
                                <i class="mdi mdi-card-bulleted-outline"></i>
                                <span> Inventory Card </span>
                            </a>
                        </li>
                        <li>
                            <a href="<?php echo e(route(name: 'pengajuan.index')); ?>">
                                <i class="mdi mdi-send-circle"></i>
                                <span> Ajukan Part </span>
                            </a>
                        </li>
                         <li>
                            <a href="<?php echo e(route(name: 'partgroupsite.index')); ?>">
                                <i class="mdi mdi-tune"></i>
                                <span> Ubah Min dan Max</span>
                            </a>
                        </li>
                        <li>
                            <a href="<?php echo e(route(name: 'sites.instock.index')); ?>">
                                <i class="mdi mdi-package-variant"></i>
                                <span class="badge badge-success badge-pill float-right" id="jumlahinsite"></span>
                                <span> In Stock </span>
                            </a>
                        </li>
                        <li>
                            <a href="<?php echo e(route(name: 'sites.outstock.index')); ?>">
                                <i class="mdi mdi-package-down"></i>
                                <span> Out Stock </span>
                            </a>
                        </li>

                        <li>
                            <a href="<?php echo e(route(name: 'units.index')); ?>">
                                <i class="mdi mdi-package-variant"></i>
                                <span> Unit </span>
                            </a>
                        </li>
                        <li>
                            <a href="<?php echo e(route(name: 'unit-transactions.index')); ?>">
                                <i class="mdi mdi-swap-horizontal"></i>
                                <span> Unit Transaksi </span>
                            </a>
                        </li>
                        <li>
                            <a href="<?php echo e(route(name: 'site.transactions.history')); ?>">
                                <i class="mdi mdi-history"></i>
                                <span> Riwayat Transaksi </span>
                            </a>
                        </li>
                        <li>
                            <a href="<?php echo e(route(name: 'site.equipment.index')); ?>">
                                <i class="mdi mdi-wrench"></i>
                                <span> Perlengkapan</span>
                            </a>
                        </li>
                        <!-- <li>
                            <a href="<?php echo e(route(name: 'part-merge.site.index')); ?>">
                                <i class="mdi mdi-source-merge"></i>
                                <span> Merge Part</span>
                            </a>
                        </li> -->
                        <li>
                            <a href="<?php echo e(route(name: 'sites.part-analysis')); ?>">
                                <i class="mdi mdi-chart-line"></i>
                                <span> Part Analysis</span>
                            </a>
                        </li>
                        <li>
                            <a href="<?php echo e(route(name: 'returnpart.site')); ?>">
                                <i class="mdi mdi-backup-restore"></i>
                                <span> Return Part HO </span>
                            </a>
                        </li>
                        <?php endif; ?>
                        <?php if(!session('access_mode') || session('access_mode') == 'daily_report'): ?>
                        <!-- Daily Report Mode Menu Items -->
                         <li>
                            <a href="<?php echo e(route(name: 'sites.dashboardunit')); ?>">
                                <i class="mdi mdi-home-analytics"></i>
                                <span> Dashboard Unit</span>
                            </a>
                        </li>

                        <li>
                            <a href="<?php echo e(route('daily-reports.index')); ?>">
                                <i class="mdi mdi-file-document-edit"></i>
                                <span> Daily Reports</span>
                            </a>
                        </li>
                        <li>
                            <a href="<?php echo e(route('unit-recap.index')); ?>">
                                <i class="mdi mdi-chart-bar"></i>
                                <span> Unit Recap</span>
                            </a>
                        </li>
                        <li>
                            <a href="<?php echo e(route('backlogs.index')); ?>">
                                <i class="mdi mdi-clipboard-list"></i>
                                <span> Backlog Management</span>
                            </a>
                        </li>
                        <li>
                            <a href="<?php echo e(route('unit-schedule.index')); ?>">
                                <i class="mdi mdi-calendar-clock"></i>
                                <span> Unit Schedule</span>
                            </a>
                        </li>
                         <li>
                            <a href="<?php echo e(route('midlife.index')); ?>">
                                <i class="mdi mdi-camera-timer"></i>
                                <span> Midlife</span>
                            </a>
                        </li>
                        <?php endif; ?>

                        <hr>
                        <li class="menu-title mt-2">User Options</li>

                        <?php if(session('access_mode')): ?>
                        <li>
                            <a href="<?php echo e(route(name: 'adminsite.mode.select')); ?>">
                                <i class="mdi mdi-swap-horizontal"></i>
                                <span> Ganti Mode </span>
                            </a>
                        </li>
                        <?php endif; ?>

                        <li>
                            <a href="<?php echo e(route(name: 'logsite.index')); ?>">
                                <i class="mdi mdi-clock-outline"></i>
                                <span> History Log </span>
                            </a>
                        </li>
                        <li>
                            <a href="<?php echo e(route(name: 'password.reset')); ?>">
                                <i class="mdi mdi-lock-reset"></i>
                                <span> Reset Password</span>
                            </a>
                        </li>
                        <li>
                            <a href="/preview-alur">
                                <i class="mdi mdi-chart-timeline-variant"></i>
                                <span> Alur Sistem </span>
                            </a>
                        </li>
                        <li>
                            <a href="<?php echo e(route(name: 'logout')); ?>">
                                <i class="mdi mdi-logout-variant"></i>
                                <span> Logout</span>
                            </a>
                        </li>
                    </ul>
                </div>
                <div class="clearfix"></div>
            </div>
        </div>

        <div class="content-page pt-0 mt-0 p-0 mr-0">
            <div class="content">
                <div class="container-fluid pr-3 mr-0">
                    <!-- ======================================================================================================================= -->
                    <!-- ======================================================================================================================= -->
                    <?php echo $__env->yieldContent('contentsite'); ?>
                    <!-- ======================================================================================================================= -->
                    <!-- ======================================================================================================================= -->
                </div>
            </div>
            <footer class="footer">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-md-12">
                            2025 <a href="">PWB</a>
                        </div>
                    </div>
                </div>
            </footer>
        </div>
    </div>
</body>
<script src="https://cdn.jsdelivr.net/npm/library-name@version/dist/library.min.js"></script>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jQuery-slimScroll/1.3.8/jquery.slimscroll.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/metisMenu/3.0.7/metisMenu.min.js"></script>

<?php echo app('Illuminate\Foundation\Vite')("resources/assets/js/vendor.min.js"); ?>
<?php echo app('Illuminate\Foundation\Vite')("resources/assets/js/app.min.js"); ?>
<?php echo $__env->yieldContent('resourcesite'); ?>
<?php echo app('Illuminate\Foundation\Vite')(['resources/js/notifikasi.js', 'resources/js/site/notifications.js']); ?>

</html><?php /**PATH C:\xampp\htdocs\portalpwb\resources\views/sites/content.blade.php ENDPATH**/ ?>