import Swal from "sweetalert2";

// Global variables for pagination
let currentPage = 1;
const itemsPerPage = 15; // 15 items per page for withdrawals table

document.addEventListener("DOMContentLoaded", function () {
    const fromSiteSelect = document.getElementById("from_site_id");
    const partNameDisplay = document.getElementById("part_name_display");
    const maxstock = document.getElementById("maxstock");
    const requestedQuantityInput = document.getElementById("requested_quantity");

    let allWithdrawals = [];
    const csrfToken = document.querySelector('meta[name="csrf-token"]').content;

    // Function to create pagination item
    function createPaginationItem(page, text, isActive = false) {
        const li = document.createElement('li');
        li.className = `page-item ${isActive ? 'active' : ''}`;

        const a = document.createElement('a');
        a.className = 'page-link';
        a.href = '#';
        a.textContent = text;
        a.dataset.page = page;

        li.appendChild(a);
        return li;
    }

    // Function to render pagination
    function renderPagination(data) {
        try {
            const paginationContainer = document.getElementById('withdrawals-pagination');
            if (!paginationContainer) {
                console.error('Pagination container not found');
                return;
            }

            paginationContainer.innerHTML = '';

            if (data.last_page > 1) {
                const pagination = document.createElement('ul');
                pagination.className = 'pagination pagination-rounded  justify-content-center';

                // Previous button
                if (data.current_page > 1) {
                    pagination.appendChild(createPaginationItem(data.current_page - 1, '\u00ab'));
                }

                // Page numbers
                for (let i = 1; i <= data.last_page; i++) {
                    pagination.appendChild(createPaginationItem(i, i, i === data.current_page));
                }

                // Next button
                if (data.current_page < data.last_page) {
                    pagination.appendChild(createPaginationItem(data.current_page + 1, '\u00bb'));
                }

                paginationContainer.appendChild(pagination);

                // Add event listeners to pagination links
                paginationContainer.querySelectorAll('.page-link').forEach(link => {
                    link.addEventListener('click', function(e) {
                        e.preventDefault();
                        const page = parseInt(this.dataset.page);
                        currentPage = page;
                        const status = document.getElementById('statusFilter').value;
                        const site = document.getElementById('siteFilter').value;
                        withdrawalFunctions.loadWithdrawals(page, status, site);
                    });
                });
            }

            // Update global pagination data
            window.withdrawalsPaginationData = data;
        } catch (error) {
            console.error('Error rendering pagination:', error);
        }
    }

    const withdrawalFunctions = {
        editWithdrawal: function (id) {
            const withdrawal = allWithdrawals.find(
                (w) => w.withdrawal_id === id
            );
            if (withdrawal) {
                document.getElementById("withdrawal_id_edit").value =
                    withdrawal.withdrawal_id;
                document.getElementById("part_code").value =
                    withdrawal.part_code;
                document.getElementById("from_site_id").value =
                    withdrawal.from_site_id;
                document.getElementById("requested_quantity").value =
                    withdrawal.requested_quantity;
                document.getElementById("withdrawal_reason").value =
                    withdrawal.withdrawal_reason || "";
                document.getElementById("notes").value = withdrawal.notes || "";
                const submitButton = document.getElementById("submit-button");
                submitButton.textContent = "Update Request";
            }
        },

        editStatus: function (id) {
            const withdrawal = allWithdrawals.find(
                (w) => w.withdrawal_id === id
            );

            if (withdrawal) {
                // Populate the edit status modal
                document.getElementById("edit_withdrawal_id").value = withdrawal.withdrawal_id;
                document.getElementById("edit_part_name").textContent = withdrawal.part.part_name;
                document.getElementById("edit_status").value = withdrawal.status;
                document.getElementById("edit_notes").value = withdrawal.notes || "";

                // Show the modal
                const modal = document.getElementById('editStatusModal');
                modal.style.display = 'block';
                modal.classList.add('show');
                document.body.classList.add('modal-open');
            }
        },

        updateStatus: function () {
            const form = document.getElementById("edit-status-form");
            const withdrawalId = document.getElementById("edit_withdrawal_id").value;
            const status = document.getElementById("edit_status").value;
            const notes = document.getElementById("edit_notes").value;

            fetch(`/withdrawals/${withdrawalId}`, {
                method: "PUT",
                headers: {
                    "Content-Type": "application/json",
                    "X-CSRF-TOKEN": csrfToken,
                },
                body: JSON.stringify({
                    status: status,
                    notes: notes
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Close the modal
                    const modal = document.getElementById('editStatusModal');
                    modal.style.display = 'none';
                    modal.classList.remove('show');
                    document.body.classList.remove('modal-open');

                    // Show success message
                    Swal.fire({
                        icon: "success",
                        title: "Berhasil!",
                        text: "Status return berhasil diperbarui!",
                    }).then(() => {
                        // Reload the withdrawals
                        withdrawalFunctions.loadWithdrawals();
                    });
                } else {
                    Swal.fire({
                        icon: "error",
                        title: "Gagal!",
                        text: data.message || "Gagal memperbarui status return.",
                    });
                }
            })
            .catch(error => {
                console.error("Error:", error);
                Swal.fire({
                    icon: "error",
                    title: "Gagal!",
                    text: "Terjadi kesalahan saat memperbarui status.",
                });
            });
        },

        deleteWithdrawal: function (id) {
            Swal.fire({
                title: "Apakah Anda yakin?",
                text: "Anda tidak akan dapat mengembalikan ini!",
                icon: "warning",
                showCancelButton: true,
                confirmButtonColor: "#3085d6",
                cancelButtonColor: "#d33",
                confirmButtonText: "Ya, hapus!",
                cancelButtonText: "Batal",
            }).then((result) => {
                if (result.isConfirmed) {
                    fetch(`/withdrawals/${id}`, {
                        method: "DELETE",
                        headers: {
                            "Content-Type": "application/json",
                            "X-CSRF-TOKEN": csrfToken,
                        },
                    })
                        .then((response) => response.json())
                        .then((data) => {
                            if (data.success) {
                                Swal.fire(
                                    "Terhapus!",
                                    "Data berhasil dihapus.",
                                    "success"
                                ).then(() => {
                                    withdrawalFunctions.loadWithdrawals();
                                });
                            } else {
                                Swal.fire(
                                    "Gagal!",
                                    "Terjadi kesalahan saat menghapus data.",
                                    "error"
                                );
                            }
                        })
                        .catch((error) => {
                            Swal.fire(
                                "Gagal!",
                                "Terjadi kesalahan saat menghapus data.",
                                "error"
                            );
                        });
                }
            });
        },

        createWithdrawal: function () {
            const form = document.getElementById("withdrawal-form");
            const formData = new FormData(form);
            const withdrawalId =
                document.getElementById("withdrawal_id_edit").value;
            const submitButton = document.getElementById("submit-button");

            let url = "/withdrawals";
            let method = "POST";

            if (withdrawalId) {
                url = `/withdrawals/${withdrawalId}`;
                method = "PUT";
            }

            fetch(url, {
                method: method,
                body: new URLSearchParams(formData),
                headers: {
                    "Content-Type": "application/x-www-form-urlencoded",
                    "X-CSRF-TOKEN": csrfToken,
                },
            })
                .then((response) => response.json())
                .then((data) => {
                    if (data.success) {
                        Swal.fire({
                            icon: "success",
                            title: "Berhasil!",
                            text: "Permintaan penarikan berhasil dibuat/diperbarui!",
                        }).then(() => {
                            // Reset to page 1 after creating/updating a withdrawal
                            withdrawalFunctions.loadWithdrawals(1);
                            form.reset();
                            document.getElementById(
                                "withdrawal_id_edit"
                            ).value = "";
                            submitButton.textContent = "Create Request";
                            partNameDisplay.textContent = "";
                        });
                    } else {
                        Swal.fire({
                            icon: "error",
                            title: "Gagal!",
                            text: data.message,
                        });
                    }
                })
                .catch((error) => {
                    Swal.fire({
                        icon: "error",
                        title: "Gagal!",
                        text: "Gagal membuat permintaan penarikan.",
                    });
                });
        },

        showDetail: function (id) {
            fetch(`/withdrawals/${id}`, {
                method: "GET",
                headers: {
                    "Content-Type": "application/json",
                    "X-CSRF-TOKEN": csrfToken,
                },
            })
            .then(response => response.json())
            .then(data => {
                const detailBody = document.getElementById("detailModalBody");
                const fragment = document.createDocumentFragment();
                const table = document.createElement("table");
                table.style.borderCollapse = "collapse";
                table.style.width = "100%";

                const createRow = (label, value) => {
                    const tr = document.createElement("tr");

                    const tdLabel = document.createElement("td");
                    tdLabel.style.padding = "4px 8px";
                    tdLabel.innerHTML = `<strong>${label}</strong>`;

                    const tdValue = document.createElement("td");
                    tdValue.textContent = value ?? '-';

                    tr.appendChild(tdLabel);
                    tr.appendChild(tdValue);
                    return tr;
                };

                const formatDate = (dateString) => {
                    return new Date(dateString).toLocaleDateString('id-ID', { day: 'numeric', month: 'long', year: 'numeric' });
                };

                table.appendChild(createRow("Code part", data.part_code));
                table.appendChild(createRow("Nama part", data.part?.part_name));
                table.appendChild(createRow("Return dari site", data.from_site.site_name));
                table.appendChild(createRow("Jumlah Yang diajukan", data.requested_quantity));
                table.appendChild(createRow("Jumlah Disetujui", data.approved_quantity));
                table.appendChild(createRow("Status Pengajuan", data.status));
                table.appendChild(createRow("Keterangan return", data.withdrawal_reason));
                table.appendChild(createRow("Update", data.notes));
                table.appendChild(createRow("Created At", formatDate(data.created_at)));
                table.appendChild(createRow("Updated At", formatDate(data.updated_at)));

                fragment.appendChild(table);
                detailBody.innerHTML = '';
                detailBody.appendChild(fragment);

                // Replace jQuery modal with vanilla JS
                const detailModal = document.getElementById('detailModal');
                detailModal.style.display = 'block';
                detailModal.classList.add('show');
                document.body.classList.add('modal-open');
            })
            .catch(error => {
                console.error("Error fetching data:", error);
            });
        },

        loadWithdrawals: function (page = 1, status = null, site = null) {
            currentPage = page;

            // If status and site are not provided, get them from the filters
            if (status === null) {
                status = document.getElementById("statusFilter").value;
            }
            if (site === null) {
                site = document.getElementById("siteFilter").value;
            }

            // Show loading indicator in the table
            const tableBody = document.querySelector("#withdrawalsTable tbody");
            if (tableBody) {
                tableBody.innerHTML = '<tr><td colspan="7" class="text-center">.</td></tr>';
            }

            fetch(`/withdrawals/get?page=${page}&per_page=${itemsPerPage}&status=${status}&site=${site}`, {
                method: "GET",
                headers: {
                    "Content-Type": "application/json",
                    "X-CSRF-TOKEN": csrfToken,
                },
            })
                .then((response) => response.json())
                .then((data) => {
                    allWithdrawals = data.data;
                    populateTable(data.data);

                    // Render pagination
                    renderPagination({
                        current_page: data.current_page,
                        per_page: data.per_page,
                        last_page: data.last_page,
                        total: data.total
                    });
                })
                .catch((error) => {
                    if (tableBody) {
                        tableBody.innerHTML = '<tr><td colspan="7" class="text-center text-danger">Failed to load data. Please try again.</td></tr>';
                    }
                    Swal.fire({
                        icon: "error",
                        title: "Gagal!",
                        text: "Gagal memuat penarikan.",
                    });
                });
        },

        getPartSuggestions: function (query) {
            const suggestionsDiv = document.getElementById("partsuggestions");
            const suggestionsList = suggestionsDiv.querySelector("ul");

            // const query = this.value;

            if (query.length < 3) {
                suggestionsDiv.style.display = "none";
                partNameDisplay.textContent = "";
                requestedQuantityInput.max = "";
                return;
            }

            fetch(`/parts/suggestions?query=${query}`, {
                method: "GET",
                headers: {
                    "Content-Type": "application/json",
                    "X-CSRF-TOKEN": csrfToken,
                },
            })
                .then((response) => response.json())
                .then((data) => {
                    suggestionsList.innerHTML = "";
                    if (data.length > 0) {
                        data.forEach((part) => {
                            const li = document.createElement("li");

                            li.textContent = `${part.part_name} | Site : ${part.site_name}, Stock : ${part.stock_quantity}`;

                            li.addEventListener("click", () => {
                                document.getElementById("part_code").value = part.part_code;
                                fromSiteSelect.value = part.site_id;
                                maxstock.value = part.stock_quantity;
                                partNameDisplay.textContent = part.part_name + " dari " + part.site_name;
                                requestedQuantityInput.max = part.stock_quantity;
                                requestedQuantityInput.min = 1;
                                suggestionsList.innerHTML = "";
                                suggestionsDiv.style.display = "none";
                            });
                            suggestionsList.appendChild(li);
                        });
                        suggestionsDiv.style.display = "block";
                    } else {
                        suggestionsDiv.style.display = "none";
                        partNameDisplay.textContent = "";
                        maxstock.value = '';
                    }
                })
                .catch((error) => console.error("Error:", error));
        },

        confirmReceipt: function (id) {
            Swal.fire({
                title: "Konfirmasi Penerimaan?",
                text: "Apakah Anda yakin telah menerima part ini?",
                icon: "question",
                showCancelButton: true,
                confirmButtonColor: "#3085d6",
                cancelButtonColor: "#d33",
                confirmButtonText: "Ya, Konfirmasi!",
                cancelButtonText: "Batal"
            }).then((result) => {
                if (result.isConfirmed) {
                    fetch(`/withdrawals/confirm/${id}`, {
                        method: "POST",
                        headers: {
                            "Content-Type": "application/json",
                            "X-CSRF-TOKEN": csrfToken,
                        },
                    })
                        .then((response) => response.json())
                        .then((data) => {
                            if (data.success) {
                                Swal.fire(
                                    "Dikonfirmasi!",
                                    "Penerimaan part berhasil dikonfirmasi.",
                                    "success"
                                ).then(() => {
                                    withdrawalFunctions.loadWithdrawals();
                                });
                            } else {
                                Swal.fire(
                                    "Gagal!",
                                    "Terjadi kesalahan saat mengkonfirmasi penerimaan.",
                                    "error"
                                );
                            }
                        })
                        .catch((error) => {
                            Swal.fire(
                                "Gagal!",
                                "Terjadi kesalahan saat mengkonfirmasi penerimaan.",
                                "error"
                            );
                        });
                }
            });
        }
    };

    function populateTable(data) {
        const tableBody = document.querySelector("#withdrawals-table tbody");
        tableBody.innerHTML = "";
        partNameDisplay.textContent = "";
        let i = 1;
        data.forEach((item) => {
            const row = document.createElement("tr");
            let actions = '';

            if (item.status === "Pending") {
                actions += `
                    <button class="btn btn-sm btn-primary edit-withdrawal-button" data-id="${item.withdrawal_id}">Edit Data</button>
                    <button class="btn btn-sm btn-danger delete-withdrawal-button" data-id="${item.withdrawal_id}">Hapus</button>
                    `;
            }

            actions += `<button class="btn btn-sm btn-info detail-withdrawal-button" data-id="${item.withdrawal_id}">Detail</button>`;

            if (item.status === "In Transit") {
                actions += `
                    <button class="btn btn-sm btn-success confirm-receipt-button ml-2" data-id="${item.withdrawal_id}">Konfirmasi Terima</button>
                    <button class="btn btn-sm btn-secondary edit-status-button ml-2" data-id="${item.withdrawal_id}">Edit Status</button>
                `;
            }
            row.innerHTML = `
                <td>${i++}</td>
                <td>${item.part.part_name}</td>
                <td>${item.from_site.site_name}</td>
                <td>${item.withdrawal_reason}</td>
                <td>${item.requested_quantity}</td>
                <td>${item.status}</td>
                <td class="text-right">
                    ${actions}
                </td>
            `;
            tableBody.appendChild(row);
        });
    }

    document
        .getElementById("withdrawals-table")
        .addEventListener("click", function (event) {
            if (event.target.classList.contains("edit-withdrawal-button")) {
                const id = parseInt(event.target.dataset.id);
                withdrawalFunctions.editWithdrawal(id);
            } else if (event.target.classList.contains("edit-status-button")) {
                const id = parseInt(event.target.dataset.id);
                withdrawalFunctions.editStatus(id);
            } else if (
                event.target.classList.contains("delete-withdrawal-button")
            ) {
                const id = parseInt(event.target.dataset.id);
                withdrawalFunctions.deleteWithdrawal(id);
            } else if (event.target.classList.contains("detail-withdrawal-button")) {
                const id = parseInt(event.target.dataset.id);
                withdrawalFunctions.showDetail(id);
            } else if (event.target.classList.contains("confirm-receipt-button")) {
                const id = parseInt(event.target.dataset.id);
                withdrawalFunctions.confirmReceipt(id);
            }
        });

    // Add event listener for the update status button
    document.getElementById("update-status-btn").addEventListener("click", function() {
        withdrawalFunctions.updateStatus();
    });

    // Add event listeners for modal close buttons
    document.querySelectorAll('[data-dismiss="modal"]').forEach(button => {
        button.addEventListener('click', () => {
            const modal = button.closest('.modal');
            modal.style.display = 'none';
            modal.classList.remove('show');
            document.body.classList.remove('modal-open');
        });
    });

    document.getElementById("submit-button").addEventListener("click", (e) => {
        e.preventDefault();
        withdrawalFunctions.createWithdrawal();
    });

    document.getElementById("part_code").addEventListener("input", function () {
        withdrawalFunctions.getPartSuggestions(this.value);
    });

    document
        .getElementById("statusFilter")
        .addEventListener("change", function () {
            // Reset to page 1 when filter changes
            withdrawalFunctions.loadWithdrawals(1);
        });

    document
        .getElementById("siteFilter")
        .addEventListener("change", function () {
            // Reset to page 1 when filter changes
            withdrawalFunctions.loadWithdrawals(1);
        });

    // Modal close functionality
    document.querySelectorAll('[data-dismiss="modal"]').forEach(button => {
        button.addEventListener('click', () => {
            const modal = button.closest('.modal');
            modal.style.display = 'none';
            modal.classList.remove('show');
            document.body.classList.remove('modal-open');
        });
    });

    // Initial load with page 1
    withdrawalFunctions.loadWithdrawals(1);
});
