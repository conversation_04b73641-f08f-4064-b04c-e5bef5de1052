// Common JavaScript for sales pages
// This file intentionally does not include notification functionality

// Global utility functions for modal management
window.SalesModalUtils = {
    // Function to clean up modals and reset scroll behavior
    cleanupModalsAndResetScroll: function() {
        // Remove all modal backdrops
        document.querySelectorAll(".modal-backdrop").forEach(function (backdrop) {
            backdrop.remove();
        });

        // Remove modal-open class from body
        document.body.classList.remove("modal-open");

        // Reset body overflow and padding
        document.body.style.overflow = "";
        document.body.style.paddingRight = "";

        // Ensure body can scroll
        document.documentElement.style.overflow = "";
        document.documentElement.style.height = "";

        // Force scroll reset
        window.scrollTo(window.scrollX, window.scrollY);
    },

    // Function to properly close modal and cleanup
    closeModalProperly: function(modalElement) {
        if (!modalElement) return;

        modalElement.classList.remove('show');
        modalElement.style.display = 'none';

        // Use cleanup function
        this.cleanupModalsAndResetScroll();
    },

    // Function to setup modal close event listeners
    setupModalCloseListeners: function(modalElement) {
        if (!modalElement) return;

        const self = this;

        // Close buttons
        const closeButtons = modalElement.querySelectorAll('[data-dismiss="modal"], [data-bs-dismiss="modal"], .btn-secondary, .close');
        closeButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                self.closeModalProperly(modalElement);
                return false;
            });
        });

        // Backdrop click
        modalElement.addEventListener('click', function(e) {
            if (e.target === modalElement) {
                self.closeModalProperly(modalElement);
            }
        });

        // Escape key
        modalElement.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                self.closeModalProperly(modalElement);
            }
        });
    },

    // Function to prevent scroll to top on pagination and other actions
    preventScrollToTop: function(element, callback) {
        if (!element) return;

        element.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // Store current scroll position
            const currentScrollY = window.scrollY;

            // Execute callback
            if (callback && typeof callback === 'function') {
                callback.call(this, e);
            }

            // Restore scroll position after a short delay
            setTimeout(() => {
                window.scrollTo(0, currentScrollY);
            }, 100);

            return false;
        });
    }
};

document.addEventListener('DOMContentLoaded', function() {
    // Clean up any leftover modal backdrops on page load
    window.SalesModalUtils.cleanupModalsAndResetScroll();

    // Initialize Bootstrap components
    const dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
    dropdownElementList.map(function (dropdownToggleEl) {
        return new bootstrap.Dropdown(dropdownToggleEl);
    });

    // Setup modal close listeners for all modals on the page
    document.querySelectorAll('.modal').forEach(modal => {
        window.SalesModalUtils.setupModalCloseListeners(modal);
    });

    // Remove any neumorphism class if it was previously applied
    document.body.classList.remove('neumorphism-active');

    // Clear any stored neumorphism preference for sales pages
    if (localStorage.getItem('neumorphismActive')) {
        localStorage.removeItem('neumorphismActive');
    }
});
