<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Export Invoices - Portal PWB</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 10px;
            margin: 0;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #333;
            padding-bottom: 10px;
        }
        
        .header h1 {
            margin: 0;
            font-size: 18px;
            color: #333;
        }
        
        .header p {
            margin: 5px 0;
            color: #666;
        }
        
        .filters {
            margin-bottom: 15px;
            padding: 10px;
            background-color: #f5f5f5;
            border-radius: 5px;
        }
        
        .filters h3 {
            margin: 0 0 10px 0;
            font-size: 12px;
            color: #333;
        }
        
        .filter-item {
            display: inline-block;
            margin-right: 20px;
            margin-bottom: 5px;
        }
        
        .filter-label {
            font-weight: bold;
            color: #555;
        }
        
        .filter-value {
            color: #333;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 6px;
            text-align: left;
            font-size: 9px;
        }
        
        th {
            background-color: #4472C4;
            color: white;
            font-weight: bold;
            text-align: center;
        }
        
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .text-center {
            text-align: center;
        }
        
        .text-right {
            text-align: right;
        }
        
        .currency {
            text-align: right;
            font-family: monospace;
        }
        
        .status-badge {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 8px;
            font-weight: bold;
            text-align: center;
        }
        
        .status-lunas {
            background-color: #d4edda;
            color: #155724;
        }
        
        .status-belum-lunas {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .status-jatuh-tempo {
            background-color: #fff3cd;
            color: #856404;
        }
        
        .footer {
            margin-top: 20px;
            text-align: center;
            font-size: 8px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }
        
        .no-data {
            text-align: center;
            padding: 20px;
            color: #666;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Export Data Invoice</h1>
        <p>Portal PWB - Superadmin</p>
        <p>Tanggal Export: {{ $exportDate }}</p>
    </div>

    <div class="filters">
        <h3>Filter yang Diterapkan:</h3>
        <div class="filter-item">
            <span class="filter-label">Pencarian:</span>
            <span class="filter-value">{{ $filters['search'] ?: 'Semua' }}</span>
        </div>
        <div class="filter-item">
            <span class="filter-label">Status:</span>
            <span class="filter-value">{{ $filters['status'] ?: 'Semua Status' }}</span>
        </div>
        <div class="filter-item">
            <span class="filter-label">Site:</span>
            <span class="filter-value">{{ $filters['site_id'] ?: 'Semua Site' }}</span>
        </div>
        <div class="filter-item">
            <span class="filter-label">Periode:</span>
            <span class="filter-value">
                @if($filters['start_date'] && $filters['end_date'])
                    {{ \Carbon\Carbon::parse($filters['start_date'])->format('d-m-Y') }} s/d {{ \Carbon\Carbon::parse($filters['end_date'])->format('d-m-Y') }}
                @else
                    Semua Periode
                @endif
            </span>
        </div>
    </div>

    @if(count($invoices) > 0)
        <table>
            <thead>
                <tr>
                    <th style="width: 5%;">No</th>
                    <th style="width: 15%;">No. Invoice</th>
                    <th style="width: 12%;">Site</th>
                    <th style="width: 15%;">Customer</th>
                    <th style="width: 10%;">Tanggal Invoice</th>
                    <th style="width: 12%;">Nilai Invoice</th>
                    <th style="width: 10%;">Nilai Pajak</th>
                    <th style="width: 12%;">Total Setelah Pajak</th>
                    <th style="width: 9%;">Status</th>
                </tr>
            </thead>
            <tbody>
                @foreach($invoices as $index => $invoice)
                    <tr>
                        <td class="text-center">{{ $index + 1 }}</td>
                        <td>{{ $invoice['no_invoice'] }}</td>
                        <td>{{ $invoice['site_name'] }}</td>
                        <td>{{ $invoice['customer'] }}</td>
                        <td class="text-center">{{ $invoice['tanggal_invoice'] }}</td>
                        <td class="currency">Rp {{number_format($invoice['invoice_value'], 0, ',', '.')}}</td>
                        <td class="currency">Rp {{number_format($invoice['tax_value'], 0, ',', '.') }}</td>
                        <td class="currency">Rp {{number_format($invoice['after_tax_total'], 0, ',', '.')}}</td>
                        <td class="text-center">
                            <span class="status-badge 
                                @if($invoice['invoice_status'] == 'Lunas') status-lunas
                                @elseif($invoice['invoice_status'] == 'Belum Lunas') status-belum-lunas
                                @else status-jatuh-tempo
                                @endif">
                                {{ $invoice['invoice_status'] }}
                            </span>
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
        
        <div style="margin-top: 15px; font-size: 10px;">
            <strong>Total Data: {{ count($invoices) }} invoice</strong>
        </div>
    @else
        <div class="no-data">
            <p>Tidak ada data invoice yang ditemukan dengan filter yang diterapkan.</p>
        </div>
    @endif

    <div class="footer">
        <p>Dokumen ini dibuat secara otomatis oleh sistem Portal PWB</p>
        <p>© {{ date('Y') }} Portal PWB - PT. Panca Wira Buana</p>
    </div>
</body>
</html>
