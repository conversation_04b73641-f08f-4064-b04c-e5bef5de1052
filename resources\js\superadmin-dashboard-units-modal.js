/**
 * Superadmin Dashboard Units Modal
 *
 * Handles the display of unit transactions in a modal when clicking on status cards
 */

document.addEventListener("DOMContentLoaded", function () {
    // Add click event listeners to status cards
    const belumPoCards = document.querySelectorAll(".status-card:first-child");
    const prosesInvoiceCards = document.querySelectorAll(
        ".status-card:last-child"
    );

    belumPoCards.forEach((card) => {
        card.style.cursor = "pointer";
        card.addEventListener("click", function () {
            const siteCard = this.closest("[data-site-id]");
            const siteId = siteCard
                ? siteCard.getAttribute("data-site-id")
                : null;
            const siteName = siteCard
                ? siteCard
                      .querySelector(".card-title")
                      .textContent.replace("Pencapaian Target ", "")
                : "Semua Site";
            openUnitsModal("belum-po", siteId, siteName);
        });
    });

    prosesInvoiceCards.forEach((card) => {
        card.style.cursor = "pointer";
        card.addEventListener("click", function () {
            const siteCard = this.closest("[data-site-id]");
            const siteId = siteCard
                ? siteCard.getAttribute("data-site-id")
                : null;
            const siteName = siteCard
                ? siteCard
                      .querySelector(".card-title")
                      .textContent.replace("Pencapaian Target ", "")
                : "Semua Site";
            openUnitsModal("proses-invoice", siteId, siteName);
        });
    });

    /**
     * Open the units modal and load data via AJAX
     *
     * @param {string} status - The status to filter units by ('belum-po' or 'proses-invoice')
     * @param {string|null} siteId - The site ID to filter by, or null for all sites
     * @param {string} siteName - The name of the site for display in the modal title
     */
    function openUnitsModal(status, siteId, siteName) {
        try {
            // Get the selected date range inputs
            const startDateInput = document.getElementById("start-date");
            const endDateInput = document.getElementById("end-date");

            // Get the selected date range or fallback to current month
            let startDate = "";
            let endDate = "";

            if (startDateInput && startDateInput.value) {
                startDate = startDateInput.value;
            } else {
                // Fallback to current month start
                const now = new Date();
                const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
                startDate = firstDay.toISOString().split("T")[0];
            }

            if (endDateInput && endDateInput.value) {
                endDate = endDateInput.value;
            } else {
                // Fallback to current date
                const now = new Date();
                endDate = now.toISOString().split("T")[0];
            }

            // Set modal title based on status and site
            let modalTitle = "";
            let url = "";
            if (status === "belum-po") {
                url = `/superadmin/pending-po-site?site_id=${siteId}&start_date=${startDate}&end_date=${endDate}`;
                modalTitle = `Daftar Unit Belum PO - ${siteName}`;
            } else if (status === "proses-invoice") {
                modalTitle = `Daftar Invoice - ${siteName}`;
                url = `/superadmin/ready-invoice-site?site_id=${siteId}&start_date=${startDate}&end_date=${endDate}`;
            }

            const modalTitleElement =
                document.getElementById("unitsModalLabel");
            if (modalTitleElement) {
                modalTitleElement.textContent = modalTitle;
            }

            // Get modal elements
            const modalElement = document.getElementById("unitsModal");
            if (!modalElement) {
                return;
            }

            // Show loading spinner, hide content
            const loadingSpinner =
                modalElement.querySelector(".spinner-border")?.parentElement;
            const contentContainer =
                document.getElementById("unitsModalContent");
            const tableBody = document.getElementById("unitsTableBody");

            if (!tableBody) {
                return;
            }

            if (loadingSpinner) loadingSpinner.style.display = "block";
            if (contentContainer) contentContainer.style.display = "none";

            // Show the modal
            const modal = new bootstrap.Modal(modalElement);
            modal.show();

            // Build the URL with query parameters
            if (siteId) {
                url += `&site_id=${siteId}`;
            }

            // Fetch units data via AJAX
            fetch(url)
                .then((response) => {
                    if (!response.ok) {
                        throw new Error("Network response was not ok");
                    }
                    return response.json();
                })
                .then((data) => {
                    // Clear existing table rows
                    tableBody.innerHTML = "";

                    // Check if there are transactions to display
                    if (data.units && data.units.length > 0) {
                        // Render the units table
                        const thead = document.querySelector("#unitsModalContent table thead");
                        if (status === "belum-po") {
                            // berat sekali pengcodingan ini
                            data.units.forEach((units, index) => {
                                thead.innerHTML = `
                                <tr>
                                    <th>No</th>
                                    <th>Kode Unit</th>
                                    <th>MR Date</th>
                                    <th>Status</th>
                                    <th>Total</th>
                                </tr>`;

                                const row = document.createElement("tr");
                                // Safely access properties with fallbacks
                                const mr_date = units.mr_date || "N/A";
                                const unit_code = units.unit_code || "-";
                                // const unit_type = units.unit_type || "-";
                                const status = units.status || "-";
                                const totalAmount = units.total_amount || 0;
                                row.innerHTML = `
                                            <td>${index + 1}</td>
                                            <td>${unit_code}</td>
                                            <td>${formatTanggalIndonesia(mr_date)}</td>
                                            <td>${status}</td>
                                            <td>Rp ${formatNumber(totalAmount)}</td>
                                        `;
                                tableBody.appendChild(row);
                                // Optional: attach event for part detail
                                const detailButton =
                                    row.querySelector(".detail-parts-btn");
                                if (detailButton) {
                                    detailButton.addEventListener(
                                        "click",
                                        function () {
                                            showPartsDetailModal(units);
                                        }
                                    );
                                }
                            });
                        } else if (status === "proses-invoice") {

                            thead.innerHTML = `
                                <tr>
                                    <th>No</th>
                                    <th>Kode Unit</th>
                                    <th>Type Unit</th>
                                    <th>Invoice Number</th>
                                    <th>Tanggal PO</th>
                                    <th>Tanggal Invoice</th>
                                    <th>Total</th>
                                </tr>
                            `;


                            data.units.forEach((units, index) => {
                                const row = document.createElement("tr");
                                // Safely access properties with fallbacks
                                const unitCode = units.unit_code || "N/A";
                                const unitName = units.unit_name || "N/A";
                                const invoiceNumber = units.no_invoice || "-";
                                const invoiceDate =
                                    units.tanggal_invoice || "-"; 
                                const podate =
                                    units.po_date || "-";
                                const totalAmount = units.total_amount || 0;
                                const documentPath =
                                    units.signed_document_path || "-";

                                    //  <td>
                                    //             <a href="#" class="invoice-link" data-id="${units.idinv}" ${documentPath? `data-document="${documentPath}"`: ""
                                    //                 }>${invoiceNumber || "-"}</a>
                                    //         </td>

                                row.innerHTML = `
                                            <td>${index + 1}</td>
                                            <td>${unitCode}</td>
                                            <td>${unitName}</td>
                                             <td>${invoiceNumber || "-"}</a></td>
                                             <td>${podate}</td>
                                             <td>${invoiceDate}</td>
                                            <td>Rp ${formatNumber(totalAmount)}</td>
                                        `;
                                tableBody.appendChild(row);

                                // Optional: attach event for part detail
                                const detailButton =
                                    row.querySelector(".detail-parts-btn");
                                if (detailButton) {
                                    detailButton.addEventListener(
                                        "click",
                                        function () {
                                            showPartsDetailModal(units);
                                        }
                                    );
                                }
                            });
                        }
                    } else {
                        // Display a message if no transactions found
                        const row = document.createElement("tr");
                        row.innerHTML = `
                            <td colspan="8" class="text-center">Tidak ada data unit yang ditemukan</td>
                        `;
                        tableBody.appendChild(row);
                    }

                    // Hide loading spinner, show content
                    if (loadingSpinner) loadingSpinner.style.display = "none";
                    if (contentContainer)
                        contentContainer.style.display = "block";
                })
                .catch((error) => {
                    tableBody.innerHTML = `
                        <tr>
                            <td colspan="8" class="text-center text-danger">
                                <i class="mdi mdi-alert-circle-outline me-2"></i>
                                Gagal memuat data. Silakan coba lagi.
                            </td>
                        </tr>
                    `;
                    if (loadingSpinner) loadingSpinner.style.display = "none";
                    if (contentContainer)
                        contentContainer.style.display = "block";
                });
        } catch (error) {
            alert("Terjadi kesalahan saat membuka modal. Silakan coba lagi.");
        }
    }

    function formatTanggalIndonesia(tanggal) {
        if (!tanggal) return "N/A";

        const bulanIndonesia = [
            "Januari",
            "Februari",
            "Maret",
            "April",
            "Mei",
            "Juni",
            "Juli",
            "Agustus",
            "September",
            "Oktober",
            "November",
            "Desember",
        ];

        const dateObj = new Date(tanggal);
        if (isNaN(dateObj)) return "N/A";

        const tanggalNum = dateObj.getDate();
        const bulan = bulanIndonesia[dateObj.getMonth()];
        const tahun = dateObj.getFullYear();

        return `${tanggalNum} ${bulan} ${tahun}`;
    }

    /**
     * Show document viewer in a modal
     */
    // Letakkan ini di luar fetch (hanya sekali jalan)
    // document.addEventListener("click", function (e) {
    //     if (e.target && e.target.classList.contains("invoice-link")) {
    //         e.preventDefault();
    //         console.log("trigger");

    //         const invoiceId = e.target.getAttribute("data-id");
    //         const documentPath = e.target.getAttribute("data-document");

    //         if (documentPath) {
    //             showDocumentViewer(documentPath);
    //         } else {
    //             fetch(`/superadmin/invoices/${invoiceId}`)
    //                 .then((response) => response.json())
    //                 .then((data) => {
    //                     const documentPath = data.success
    //                         ? data.invoice.signed_document_path ||
    //                           data.invoice.document_path
    //                         : null;

    //                     if (documentPath) {
    //                         showDocumentViewer(documentPath);
    //                     } else {
    //                         alert("Tidak ada lampiran invoice yang tersedia.");
    //                     }
    //                 })
    //                 .catch((error) => {
    //                     alert("Gagal mengambil data lampiran invoice.");
    //                 });
    //         }
    //     }
    // });

    // function showDocumentViewer(documentPath) {
    //     const viewerContent = document.getElementById(
    //         "document-viewer-content"
    //     );
    //     const documentUrl = `/assets/invoice_documents/${documentPath}`;

    //     // Set the document viewer content with 100% width
    //     viewerContent.innerHTML = `
    //         <iframe src="${documentUrl}" style="width: 100%; height: 100%; border: none; display: block;"></iframe>
    //     `;

    //     // Show the modal
    //     const modal = new bootstrap.Modal(
    //         document.getElementById("document-viewer-modal")
    //     );
    //     modal.show();
    // }

    /**
     * Format a number as currency (with thousands separator)
     *
     * @param {number} number - The number to format
     * @return {string} The formatted number
     */
    function formatNumber(number) {
        return new Intl.NumberFormat("id-ID").format(number);
    }

    /**
     * Get the appropriate badge class for a status
     *
     * @param {string} status - The status value
     * @return {string} The CSS class for the badge
     */
    function getStatusBadgeClass(status) {
        switch (status) {
            case "On Process":
                return "bg-primary";
            case "MR":
                return "bg-info";
            case "Pending":
                return "bg-warning text-dark";
            case "Ready WO":
                return "bg-success";
            case "Ready PO":
                return "bg-success";
            case "Selesai":
                return "bg-dark";
            case "perbaikan":
                return "bg-danger";
            default:
                return "bg-secondary";
        }
    }

    /**
     * Show the parts detail modal for a transaction
     *
     * @param {Object} transaction - The transaction object containing parts data
     */
    function showPartsDetailModal(transaction) {
        try {
            // Set transaction details in the modal
            const detailUnitCode = document.getElementById("detail-unit-code");
            const detailUnitName = document.getElementById("detail-unit-name");
            const detailStatus = document.getElementById("detail-status");
            const detailNotes = document.getElementById("detail-notes");
            const partsDetailModalLabel = document.getElementById(
                "partsDetailModalLabel"
            );

            if (detailUnitCode)
                detailUnitCode.textContent = transaction.unit_code || "N/A";
            if (detailUnitName)
                detailUnitName.textContent = transaction.unit_name || "N/A";
            if (detailStatus)
                detailStatus.innerHTML = `<span class="badge ${getStatusBadgeClass(
                    transaction.status
                )}">${transaction.status || "Unknown"}</span>`;
            if (detailNotes) detailNotes.textContent = transaction.notes || "-";

            // Update modal title
            if (partsDetailModalLabel) {
                partsDetailModalLabel.textContent = `Detail Part Unit: ${
                    transaction.unit_code || "N/A"
                } - ${transaction.unit_name || "N/A"}`;
            }

            // Clear existing table rows
            const tableBody = document.getElementById("partsDetailTableBody");
            if (!tableBody) {
                return;
            }

            tableBody.innerHTML = "";

            // Check if there are parts to display
            if (transaction.parts && transaction.parts.length > 0) {
                let totalPrice = 0;

                // Render the parts table
                transaction.parts.forEach((part, index) => {
                    const row = document.createElement("tr");

                    // Safely access properties with fallbacks
                    const partCode = part.part_code || "N/A";
                    const partName = part.part_name || "N/A";
                    const quantity = part.quantity || 0;
                    const price = part.price || 0;
                    const total = part.total || 0;

                    row.innerHTML = `
                        <td>${index + 1}</td>
                        <td>${partCode}</td>
                        <td>${partName}</td>
                        <td>${quantity}</td>
                        <td>Rp ${formatNumber(price)}</td>
                        <td>Rp ${formatNumber(total)}</td>
                    `;
                    tableBody.appendChild(row);

                    // Add to total price
                    totalPrice += total;
                });

                // Update total price
                const partsTotalPrice =
                    document.getElementById("parts-total-price");
                if (partsTotalPrice) {
                    partsTotalPrice.textContent = `Rp ${formatNumber(
                        totalPrice
                    )}`;
                }
            } else {
                // Display a message if no parts found
                const row = document.createElement("tr");
                row.innerHTML = `
                    <td colspan="6" class="text-center">Tidak ada data part yang ditemukan</td>
                `;
                tableBody.appendChild(row);

                // Reset total price
                const partsTotalPrice =
                    document.getElementById("parts-total-price");
                if (partsTotalPrice) {
                    partsTotalPrice.textContent = "Rp 0";
                }
            }

            // Show the modal
            const modalElement = document.getElementById("partsDetailModal");
            if (!modalElement) {
                return;
            }

            const modal = new bootstrap.Modal(modalElement);
            modal.show();
        } catch (error) {
            alert(
                "Terjadi kesalahan saat menampilkan detail part. Silakan coba lagi."
            );
        }
    }
});
