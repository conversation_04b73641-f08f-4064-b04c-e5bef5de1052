<?php

namespace App\Http\Controllers;

use App\Models\Part;
use App\Models\PartInventory;
use App\Models\PartMerge as PartMergeModel; // Alias the PartMerge model to avoid naming conflicts
use App\Models\SiteOutStock;
use App\Helpers\LogHelper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PartmergeController extends Controller
{
    public function index()
    {
        return view('part_merge.index');
    }
    public function indexsite()
    {
        return view('part_merge.indexsite');
    }

    public function checkParts()
    {
        $parts = Part::all();
        $partInventories = PartInventory::all();

        return response()->json([
            'parts_count' => $parts->count(),
            'part_inventories_count' => $partInventories->count(),
            'parts' => $parts->take(10),
            'part_inventories' => $partInventories->take(10)
        ]);
    }

    public function showMergedParts(Request $request)
    {
        $siteId = session('site_id'); // Use session site_id or default to 1
        $perPage = $request->input('per_page', 15); // Default to 15 items per page
        $page = $request->input('page', 1); // Default to page 1
        $partType = $request->input('part_type', ''); // Filter by part type

        $query = DB::table('part_merges as pm')
            ->join('parts as p_old', 'pm.part_code_old', '=', 'p_old.part_code')
            ->join('parts as p_new', 'pm.part_code_new', '=', 'p_new.part_code')
            ->select(
                'pm.part_merge_id',
                'p_new.part_name as nama_part_hasil',
                'p_old.part_name as nama_sub_part',
                'pm.quantity as jumlah_sub_part',
                'pm.merge_date as tanggal',
                'pm.notes as catatan',
                'p_old.part_type'
            )
            ->orderBy('tanggal', 'DESC');

        // Apply part type filter if specified
        if (!empty($partType) && $partType !== 'All') {
            $query->where('p_old.part_type', $partType);
        }

        // Get total count for pagination
        $total = $query->count();

        // Get paginated results
        $mergedParts = $query->skip(($page - 1) * $perPage)
                            ->take($perPage)
                            ->get();

        return response()->json([
            'data' => $mergedParts,
            'current_page' => (int)$page,
            'per_page' => (int)$perPage,
            'last_page' => ceil($total / $perPage),
            'total' => $total
        ]);
    }

    public function combinedNameAutocomplete(Request $request)
    {
        $term = $request->input('term');
        $siteId = session('site_id', 1); // Use session site_id or default to 1
        $partType = $request->input('part_type', ''); // Optional part type filter

        // First try with join to part_inventories
        $query = Part::select('parts.*')
            ->join('part_inventories', 'parts.part_code', '=', 'part_inventories.part_code')
            ->where('part_inventories.site_id', $siteId)
            ->where(function($q) use ($term) {
                $q->where('parts.part_name', 'LIKE', "%{$term}%")
                  ->orWhere('parts.part_code', 'LIKE', "%{$term}%");
            })
            ->limit(10);

        // Apply part type filter if specified
        if (!empty($partType) && $partType !== 'All') {
            $query->where('parts.part_type', $partType);
        }

        $parts = $query->get();

        // If no results, try a more lenient query without the join
        if ($parts->isEmpty()) {
            $query = Part::select('parts.*')
                ->where(function($q) use ($term) {
                    $q->where('parts.part_name', 'LIKE', "%{$term}%")
                      ->orWhere('parts.part_code', 'LIKE', "%{$term}%");
                })
                ->limit(10);

            // Apply part type filter if specified
            if (!empty($partType) && $partType !== 'All') {
                $query->where('parts.part_type', $partType);
            }

            $parts = $query->get();
        }

        $results = [];
        foreach ($parts as $part) {
            $results[] = [
                'id' => $part->part_code,
                'value' => $part->part_name,
                'type' => $part->part_type
            ];
        }

        return response()->json($results);
    }
    public function autocompletePart(Request $request)
    {
        $siteId = session('site_id', 1); // Use session site_id or default to 1
        $term = $request->get('term');
        $partType = $request->get('part_type', ''); // Optional part type filter

        // First try with stock filter
        $query = Part::select('parts.*')
            ->join('part_inventories', 'parts.part_code', '=', 'part_inventories.part_code')
            ->where('part_inventories.site_id', $siteId)
            ->where(function($q) use ($term) {
                $q->where('parts.part_name', 'LIKE', "%{$term}%")
                  ->orWhere('parts.part_code', 'LIKE', "%{$term}%");
            })
            ->limit(10);

        // Apply part type filter if specified
        if (!empty($partType) && $partType !== 'All') {
            $query->where('parts.part_type', $partType);
        }

        $parts = $query->get();

        // If no results, try a more lenient query without the join
        if ($parts->isEmpty()) {
            $query = Part::select('parts.*')
                ->where(function($q) use ($term) {
                    $q->where('parts.part_name', 'LIKE', "%{$term}%")
                      ->orWhere('parts.part_code', 'LIKE', "%{$term}%");
                })
                ->limit(10);

            // Apply part type filter if specified
            if (!empty($partType) && $partType !== 'All') {
                $query->where('parts.part_type', $partType);
            }

            $parts = $query->get();
        }

        $results = [];
        foreach ($parts as $part) {
            // Get stock quantity safely
            $stockQty = 0;
            $inventory = $part->partInventories->where('site_id', $siteId)->first();
            if ($inventory) {
                $stockQty = $inventory->stock_quantity;
            }

            $results[] = [
                'id' => $part->part_code,
                'value' => $part->part_name,
                'type' => $part->part_type,
                'stock' => $stockQty
            ];
        }

        return response()->json($results);
    }

    public function submitCombinedItem(Request $request)
    {
        // Get user information from request or session
        $userRole = $request->input('user_role', session('role'));
        $employeeId = $request->input('employee_id', session('employee_id', 0));
        $requestSiteId = $request->input('site_id');

        // Determine site ID based on role
        $siteId = ($userRole === 'adminho' || $userRole === 'superadmin') ? 'WH01' : ($requestSiteId ?: session('site_id'));

        // Check if site ID is available
        if (!$siteId) {
            return response()->json(['message' => 'Site ID tidak ditemukan. Silakan login ulang.'], 400);
        }

        // Log debug information
        Log::info('Part Merge Request', [
            'user_role' => $userRole,
            'employee_id' => $employeeId,
            'site_id' => $siteId,
            'new_part_code' => $request->input('new_part_code'),
            'new_part_name' => $request->input('new_part_name'),
        ]);

        $oldParts = $request->input('old_parts'); // Array code_part
        $newPartName = $request->input('new_part_name');
        $newPartCode = $request->input('new_part_code');
        $notes = $request->input('notes');

        DB::beginTransaction();
        try {
            // 1. Verify that the target part exists
            $targetPart = Part::where('part_code', $newPartCode)->first();
            if (!$targetPart) {
                return response()->json(['message' => 'Part target tidak ditemukan!'], 400);
            }

            // 2. Verify that the site exists
            $site = DB::table('sites')->where('site_id', $siteId)->first();
            if (!$site) {
                // If site doesn't exist, create it (for development/testing purposes)
                if ($siteId === 'WH01') {
                    DB::table('sites')->insert([
                        'site_id' => 'WH01',
                        'site_name' => 'Warehouse',
                        'created_at' => now(),
                        'updated_at' => now()
                    ]);
                    Log::info('Created default warehouse site WH01');
                } else {
                    return response()->json(['message' => "Site dengan ID {$siteId} tidak ditemukan!"], 400);
                }
            }

            // 3. Create PartMerge entries
            foreach ($oldParts as $oldPart) {
                $quantityUsed = $oldPart['jumlah'];
                PartMergeModel::create([  // Use the alias here
                    'part_code_old' => $oldPart['code'],
                    'part_code_new' => $newPartCode,
                    'quantity' => $quantityUsed,
                    'merge_date' => now(),
                    'site_id' => $siteId,
                    'part_merge_name' => $newPartName,
                    'notes' => $notes
                ]);

                // 4. Find the part inventory
                $partInventory = PartInventory::where('part_code', $oldPart['code'])
                    ->where('site_id', $siteId)
                    ->first();

                if (!$partInventory) {
                    // If part inventory doesn't exist, create it (for development/testing purposes)
                    try {
                        $partInventory = PartInventory::create([
                            'part_code' => $oldPart['code'],
                            'site_id' => $siteId,
                            'priority' => false,
                            'min_stock' => 0,
                            'max_stock' => 100,
                            'stock_quantity' => $quantityUsed
                        ]);
                        Log::info("Created part inventory for {$oldPart['code']} in site {$siteId}");
                    } catch (\Exception $e) {
                        DB::rollback();
                        return response()->json(['message' => "Gagal membuat part inventory: {$e->getMessage()}"], 400);
                    }
                }

                // 5. Create appropriate OutStock entries based on user role
                if ($userRole === 'adminho' || $userRole === 'superadmin') {
                    // For warehouse admin, create WarehouseOutStock
                    DB::table('warehouse_out_stocks')->insert([
                        'part_inventory_id' => $partInventory->part_inventory_id,
                        'employee_id' => $employeeId,
                        'date_out' => now(),
                        'quantity' => $quantityUsed,
                        'status' => 'Merged',
                        'notes' => "Part Digabung ke {$newPartName}",
                        'created_at' => now(),
                        'updated_at' => now()
                    ]);
                } else {
                    // For site admin, create SiteOutStock
                    SiteOutStock::create([
                        'part_inventory_id' => $partInventory->part_inventory_id,
                        'site_id' => $siteId,
                        'employee_id' => $employeeId,
                        'date_out' => now(),
                        'quantity' => $quantityUsed,
                        'status' => 'Merged',
                        'notes' => "Part Digabung ke {$newPartName}"
                    ]);
                }

                // 6. Update inventory stock
                try {
                    $partInventory->stock_quantity -= $quantityUsed;
                    $partInventory->save();

                    // Get part name for logging
                    $oldPartName = Part::where('part_code', $oldPart['code'])->first()->part_name;

                    // Log the part merge operation for each part
                    LogHelper::createLog(
                        'Menggabungkan Part',
                        "User " . session('name') . " menggabungkan part {$oldPartName} sebanyak {$quantityUsed} ke part {$newPartName}",
                        'Part Merge',
                        $request
                    );
                } catch (\Throwable $th) {
                    DB::rollback();
                    return response()->json(['message' => "Terjadi kesalahan saat mengubah stock: {$th->getMessage()}"], 500);
                }
            }

            DB::commit();
            return response()->json(['message' => 'Part berhasil digabung!']);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['message' => "Terjadi kesalahan: {$e->getMessage()}"], 500);
        }
    }
}
