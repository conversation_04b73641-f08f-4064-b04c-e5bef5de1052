import Swal from "sweetalert2";

document.addEventListener("DOMContentLoaded", function () {
    loadLastActivities();

    // Add event listener to the date filter form
    const dateFilterForm = document.querySelector('.page-title-box form');
    if (dateFilterForm) {
        // Add event listeners to automatically submit form when inputs change
        const radioButtons = dateFilterForm.querySelectorAll('input[type="radio"][name="group_by"]');
        const dateInputs = dateFilterForm.querySelectorAll('input[type="date"]');

        // Add change event listeners to radio buttons
        radioButtons.forEach(radio => {
            radio.addEventListener('change', function() {
                submitFilterForm(dateFilterForm);
            });
        });

        // Add change event listeners to date inputs
        dateInputs.forEach(dateInput => {
            dateInput.addEventListener('change', function() {
                submitFilterForm(dateFilterForm);
            });
        });

        // Form submit event
        dateFilterForm.addEventListener('submit', function(e) {
            e.preventDefault(); // Prevent the default form submission
            submitFilterForm(this);
        });
    }

    // Function to handle form submission
    function submitFilterForm(form) {
        // Get form data
        const formData = new FormData(form);
        const startDate = formData.get('start_date');
        const endDate = formData.get('end_date');
        const groupBy = formData.get('group_by');

        // Update the dashboard data via AJAX
        updateDashboardData(startDate, endDate, groupBy);
    }

    // Function to update dashboard data via AJAX
    function updateDashboardData(startDate, endDate, groupBy) {
        // Show loading indicators
        document.getElementById('totalInStock').innerHTML = '<div class="spinner-border text-primary" role="status"><span class="sr-only">.</span></div>';
        document.getElementById('totalOutStock').innerHTML = '<div class="spinner-border text-primary" role="status"><span class="sr-only">.</span></div>';
        document.getElementById('totalUnitTransactions').innerHTML = '<div class="spinner-border text-primary" role="status"><span class="sr-only">.</span></div>';
        document.getElementById('totalUnitTransactionsbeforetax').innerHTML = '<div class="spinner-border text-primary" role="status"><span class="sr-only">.</span></div>';
        document.getElementById('mrtotalUnitTransactions').innerHTML = '<div class="spinner-border text-primary" role="status"><span class="sr-only">.</span></div>';
        document.getElementById('mrtotalUnitTransactionsbeforetax').innerHTML = '<div class="spinner-border text-primary" role="status"><span class="sr-only">.</span></div>';

        // Show loading indicator for chart
        const chartContainer = document.querySelector('.chart-container');
        if (chartContainer) {
            chartContainer.innerHTML = '<div class="d-flex justify-content-center align-items-center" style="height: 400px;"><div class="spinner-border text-primary" role="status"><span class="sr-only">.</span></div></div>';
        }

        // Make AJAX request to get updated data
        fetch(`/sites/dashboard/get-stock-totals?start_date=${startDate}&end_date=${endDate}&group_by=${groupBy}`)
            .then(response => response.json())
            .then(data => {
                // Update the dashboard with new data
                document.getElementById('totalInStock').textContent = data.totalInStock;
                document.getElementById('totalOutStock').textContent = data.totalOutStock;
                document.getElementById('totalUnitTransactions').textContent = data.formattedTotalUnitTransactions;
                document.getElementById('totalUnitTransactionsbeforetax').textContent = data.formattedTotalUnitTransactionsbeforetax;
                document.getElementById('mrtotalUnitTransactions').textContent = data.mrformattedTotalUnitTransactions;
                document.getElementById('mrtotalUnitTransactionsbeforetax').textContent = data.mrformattedTotalUnitTransactionsbeforetax;

                // Update date range text
                const dateRangeText = document.querySelector('#totalUnitTransactions + small');
                if (dateRangeText) {
                    dateRangeText.textContent = `Berdasarkan Tanggal : ${data.dateRange.start} s/d ${data.dateRange.end}`;
                }

                // Update chart data if available
                if (data.chartData) {
                    // Recreate chart container
                    if (chartContainer) {
                        chartContainer.innerHTML = '<canvas id="inOutStockChart"></canvas>';

                        // Update global chart data
                        window.chartData = data.chartData;
                        window.groupBy = groupBy;

                        // Reinitialize chart
                        const event = new Event('chartDataUpdated');
                        document.dispatchEvent(event);
                    }
                }

                // Update inventory data if available
                if (data.inventoryData) {
                    updateInventoryData(data.inventoryData);
                }
            })
            .catch(error => {
                document.getElementById('totalInStock').textContent = '0';
                document.getElementById('totalOutStock').textContent = '0';
                document.getElementById('totalUnitTransactions').textContent = 'Rp 0';
                document.getElementById('totalUnitTransactionsbeforetax').textContent = 'Rp 0';
                document.getElementById('mrtotalUnitTransactions').textContent = 'Rp 0';
                document.getElementById('mrtotalUnitTransactionsbeforetax').textContent = 'Rp 0';

                // Reset chart container
                if (chartContainer) {
                    chartContainer.innerHTML = '<canvas id="inOutStockChart"></canvas>';
                }
            });
    }

    const forms = {
        in: {
            form: document.getElementById("increate-form"),
            partCode: document.getElementById("inpart_code"),
            suggestions: document.getElementById("inpart_code_suggestions"),
            quantity: document.getElementById("inquantity"),
            date: document.getElementById("indate_in"),
            notes: document.getElementById("innotes"),
            url: "/sites/instock/create",
            fetchUrl: "/sites/instock/getParts",
        },
        out: {
            form: document.getElementById("outcreate-form"),
            partCode: document.getElementById("outpart_code"),
            suggestions: document.getElementById("outpart_code_suggestions"),
            quantity: document.getElementById("outquantity"),
            date: document.getElementById("outdate_out"),
            notes: document.getElementById("outnotes"),
            url: "/sites/outstock/create",
            fetchUrl: "/sites/outstock/getParts",
        },
    };

    // const handleSubmit = (type) => (event) => {
    //     event.preventDefault();
    //     const { partCode, quantity, date, notes } = forms[type];
    //     if (!partCode.value || !quantity.value || !date.value) {
    //         Swal.fire({
    //             icon: "error",
    //             title: "Error!",
    //             text: "Please fill all required fields.",
    //         });
    //         return;
    //     }
    //     const data = {
    //         part_code: partCode.value,
    //         quantity: quantity.value,
    //         [`date_${type}`]: date.value,
    //         notes: notes.value,
    //     };
    //     createItem(data, forms[type].url, type);
    // };

    const createItem = (data, url, type) => {
        Swal.fire({
            title: `Are you sure you want to create this ${type} item?`,
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: "Yes, create it!",
        }).then((result) => {
            if (result.isConfirmed) {
                fetch(url, {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                        "X-CSRF-TOKEN": document
                            .querySelector('meta[name="csrf-token"]')
                            .getAttribute("content"),
                    },
                    body: JSON.stringify(data),
                })
                    .then((response) => response.json())
                    .then((data) => {
                        if (data.success)
                            Swal.fire("Success!", data.success, "success");
                        else if (data.error)
                            Swal.fire("Error!", data.error, "error");
                    })
                    .catch((error) => {
                        Swal.fire({
                            icon: "error",
                            title: "Error!",
                            text: "Failed to save data.",
                        });
                    });
            }
        });
    };

    // const autocomplete = (input, suggestionBox, fetchUrl) => {
    //     input.addEventListener("input", function () {
    //         const searchTerm = this.value;
    //         if (!searchTerm) {
    //             suggestionBox.innerHTML = "";
    //             suggestionBox.style.display = "none";
    //             return;
    //         }
    //         fetch(`${fetchUrl}?search=${searchTerm}`)
    //             .then((response) => response.json())
    //             .then((data) => {
    //                 suggestionBox.innerHTML = "";
    //                 if (data.length > 0) {
    //                     data.forEach((part) => {
    //                         const suggestion = document.createElement("div");
    //                         suggestion.textContent = `${part.part_name} (${part.part_code})`;
    //                         suggestion.style.cursor = "pointer";
    //                         suggestion.addEventListener("click", function () {
    //                             input.value = part.part_code;
    //                             suggestionBox.innerHTML = "";
    //                             suggestionBox.style.display = "none";
    //                         });
    //                         suggestionBox.appendChild(suggestion);
    //                     });
    //                     suggestionBox.style.display = "block";
    //                 } else {
    //                     suggestionBox.style.display = "none";
    //                 }
    //             })
    //             .catch((error) =>
    //                 console.error("Error fetching parts:", error)
    //             );
    //     });

    //     document.addEventListener("click", (event) => {
    //         if (
    //             !input.contains(event.target) &&
    //             !suggestionBox.contains(event.target)
    //         ) {
    //             suggestionBox.innerHTML = "";
    //             suggestionBox.style.display = "none";
    //         }
    //     });
    // };

    function loadLastActivities() {
        fetch("/last-activities")
            .then((response) => response.json())
            .then((data) => {
                const ul = document.getElementById("activityList");

                if (!ul) {
                    return;
                }
                ul.innerHTML = "";
                data.forEach((activity) => {
                    const li = document.createElement("li");
                    const a = document.createElement("a");
                    a.href = "#";
                    a.textContent = activity.description;
                    li.appendChild(a);
                    ul.appendChild(li);
                });
            })
            .catch((error) => {
                const ul = document.getElementById("activityList");
                if (ul) {
                    ul.innerHTML = "<li>Error loading activities.</li>";
                }
            });
    }

    // forms.in.form.addEventListener("submit", handleSubmit("in"));
    // forms.out.form.addEventListener("submit", handleSubmit("out"));
    // autocomplete(forms.in.partCode, forms.in.suggestions, forms.in.fetchUrl);
    // autocomplete(forms.out.partCode, forms.out.suggestions, forms.out.fetchUrl);

    // Function to update inventory data
    function updateInventoryData(inventoryData) {
        if (!inventoryData || !Array.isArray(inventoryData)) return;

        // Clear existing inventory sections
        const inventorySections = document.querySelectorAll('.pl-2.pr-4.shadow-kit.bgwhite.p-4.pb-0.mt-2');
        inventorySections.forEach(section => section.remove());

        // Get the parent container to append new inventory sections
        const parentContainer = document.querySelector('.col.p-0');
        if (!parentContainer) return;

        // Create and append new inventory sections
        inventoryData.forEach((data, index) => {
            if ((data.not_ready_parts && data.not_ready_parts.length > 0) ||
                (data.medium_parts && data.medium_parts.length > 0)) {

                // Create inventory section
                const section = document.createElement('div');
                section.className = 'pl-2 pr-4 shadow-kit bgwhite p-4 pb-0 mt-2';

                // Add title
                const title = document.createElement('h5');
                title.className = 'text-uppercase text-bold';
                title.textContent = `Status Part di ${data.site_name}`;
                section.appendChild(title);

                // Add separator
                const hr = document.createElement('hr');
                section.appendChild(hr);

                // Add not ready parts table if available
                if (data.not_ready_parts && data.not_ready_parts.length > 0) {
                    const notReadyDiv = createPartsTable(data.not_ready_parts, 'Part Not Ready', `not-ready-table-${index}`, 'danger');
                    section.appendChild(notReadyDiv);
                }

                // Add medium parts table if available
                if (data.medium_parts && data.medium_parts.length > 0) {
                    const mediumDiv = createPartsTable(data.medium_parts, 'Part Hampir Habis (Stock mendekati Minimum)', `medium-table-${index}`, 'warning');
                    section.appendChild(mediumDiv);
                }

                // Append to parent container
                parentContainer.appendChild(section);

                // Setup pagination for tables
                if (data.not_ready_parts && data.not_ready_parts.length > 0) {
                    setupPagination(`not-ready-table-${index}`, `not-ready-pagination-${index}`, 5);
                }
                if (data.medium_parts && data.medium_parts.length > 0) {
                    setupPagination(`medium-table-${index}`, `medium-pagination-${index}`, 5);
                }
            }
        });

        // Update pie charts if they exist
        updatePieCharts(inventoryData);
    }

    // Helper function to create parts table
    function createPartsTable(parts, title, tableId, statusClass) {
        const div = document.createElement('div');
        div.className = 'table-responsive';

        const h6 = document.createElement('h6');
        h6.textContent = title;
        div.appendChild(h6);

        const table = document.createElement('table');
        table.className = 'table table-bordered w-100';
        table.id = tableId;

        // Create table header
        const thead = document.createElement('thead');
        thead.className = 'table-dark text-white';
        const headerRow = document.createElement('tr');

        const headers = ['Nama Part', 'Min', 'Max', 'Stock Tersisa'];
        headers.forEach(headerText => {
            const th = document.createElement('th');
            th.className = 'p-2';
            th.textContent = headerText;
            headerRow.appendChild(th);
        });

        thead.appendChild(headerRow);
        table.appendChild(thead);

        // Create table body
        const tbody = document.createElement('tbody');
        parts.forEach(part => {
            const row = document.createElement('tr');
            row.className = part.status === statusClass ? `table-${statusClass}` : '';

            const nameTd = document.createElement('td');
            nameTd.textContent = part.part_name;
            row.appendChild(nameTd);

            const minTd = document.createElement('td');
            minTd.textContent = part.min_stock;
            row.appendChild(minTd);

            const maxTd = document.createElement('td');
            maxTd.textContent = part.max_stock;
            row.appendChild(maxTd);

            const stockTd = document.createElement('td');
            stockTd.textContent = part.stock_quantity;
            row.appendChild(stockTd);

            tbody.appendChild(row);
        });

        table.appendChild(tbody);
        div.appendChild(table);

        // Add pagination container
        const paginationDiv = document.createElement('div');
        paginationDiv.id = tableId.replace('table', 'pagination');
        paginationDiv.className = 'pagination-container mt-3';
        div.appendChild(paginationDiv);

        return div;
    }

    // Function to setup pagination for tables
    function setupPagination(tableId, paginationId, itemsPerPage = 5) {
        const table = document.getElementById(tableId);
        if (!table) return;

        const tbody = table.querySelector('tbody');
        const rows = tbody.querySelectorAll('tr');
        const totalPages = Math.ceil(rows.length / itemsPerPage);

        let currentPage = 1;

        // Function to show the appropriate rows for the current page
        function showPage(page) {
            const start = (page - 1) * itemsPerPage;
            const end = start + itemsPerPage;

            // Hide all rows
            rows.forEach((row, index) => {
                row.style.display = (index >= start && index < end) ? '' : 'none';
            });

            // Update pagination UI
            updatePagination();
        }

        // Function to create pagination controls
        function updatePagination() {
            const paginationContainer = document.getElementById(paginationId);
            if (!paginationContainer) return;

            paginationContainer.innerHTML = '';

            if (totalPages <= 1) return; // Don't show pagination if only one page

            const ul = document.createElement('ul');
            ul.className = 'pagination pagination-rounded ';

            // Previous button
            const prevLi = document.createElement('li');
            prevLi.className = currentPage === 1 ? 'disabled' : '';
            const prevA = document.createElement('a');
            prevA.href = '#';
            prevA.textContent = '«';
            prevA.addEventListener('click', function(e) {
                e.preventDefault();
                if (currentPage > 1) {
                    currentPage--;
                    showPage(currentPage);
                }
            });
            prevLi.appendChild(prevA);
            ul.appendChild(prevLi);

            // Page numbers
            for (let i = 1; i <= totalPages; i++) {
                const li = document.createElement('li');
                li.className = i === currentPage ? 'active' : '';
                const a = document.createElement('a');
                a.href = '#';
                a.textContent = i;
                a.addEventListener('click', function(e) {
                    e.preventDefault();
                    currentPage = i;
                    showPage(currentPage);
                });
                li.appendChild(a);
                ul.appendChild(li);
            }

            // Next button
            const nextLi = document.createElement('li');
            nextLi.className = currentPage === totalPages ? 'disabled' : '';
            const nextA = document.createElement('a');
            nextA.href = '#';
            nextA.textContent = '»';
            nextA.addEventListener('click', function(e) {
                e.preventDefault();
                if (currentPage < totalPages) {
                    currentPage++;
                    showPage(currentPage);
                }
            });
            nextLi.appendChild(nextA);
            ul.appendChild(nextLi);

            paginationContainer.appendChild(ul);
        }

        // Initialize pagination
        showPage(currentPage);
    }

    // Function to update pie charts
    function updatePieCharts(inventoryData) {
        if (!inventoryData || !Array.isArray(inventoryData)) return;

        inventoryData.forEach(data => {
            const canvasId = `pieChart${data.site_name.replace(/\s+/g, '').toLowerCase()}`;
            const canvas = document.getElementById(canvasId);

            if (canvas) {
                // If Chart.js is available, update the pie chart
                if (window.Chart) {
                    // Check if there's an existing chart instance
                    const existingChart = Chart.getChart(canvas);
                    if (existingChart) {
                        existingChart.destroy();
                    }

                    // Create new chart
                    new Chart(canvas, {
                        type: 'pie',
                        data: {
                            labels: ['Ready', 'Not Ready'],
                            datasets: [{
                                data: [data.ready_percentage, data.not_ready_percentage],
                                backgroundColor: ['#28a745', '#dc3545'],
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    position: 'bottom'
                                },
                                tooltip: {
                                    callbacks: {
                                        label: function(context) {
                                            return `${context.label}: ${context.raw.toFixed(2)}%`;
                                        }
                                    }
                                }
                            }
                        }
                    });
                }
            }
        });
    }
});
