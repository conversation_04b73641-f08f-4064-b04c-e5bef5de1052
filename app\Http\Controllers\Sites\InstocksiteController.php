<?php

namespace App\Http\Controllers\Sites;

use App\Http\Controllers\Controller;
use App\Models\LogAktivitas;
use App\Models\Part;
use App\Models\PartInventory;
use App\Models\Requisition;
use App\Models\RequisitionDetail;
use App\Models\SiteInStock;
use App\Models\StockTransaction;
use App\Models\WarehouseOutStock;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class InstocksiteController extends Controller
{
    public function index()
    {
        $site_id = session('site_id');

        // Get yesterday's date for default filtering
        $startDate = Carbon::yesterday()->format('Y-m-d');
        $endDate = Carbon::tomorrow()->format('Y-m-d');

        // Query for paginated data to display
        $siteInStocks = SiteInStock::query()
            ->join('part_inventories', 'site_in_stocks.part_inventory_id', '=', 'part_inventories.part_inventory_id')
            ->join('parts', 'part_inventories.part_code', '=', 'parts.part_code')
            ->where('part_inventories.site_id', $site_id)
            ->whereBetween('date_in', [$startDate, $endDate])
            ->select('site_in_stocks.*', 'parts.part_name', 'parts.part_code')
            ->paginate(5);

        // Calculate total quantity for all records for yesterday
        $totalQuantity = SiteInStock::query()
            ->join('part_inventories', 'site_in_stocks.part_inventory_id', '=', 'part_inventories.part_inventory_id')
            ->where('part_inventories.site_id', $site_id)
            ->whereBetween('date_in', [$startDate, $endDate])
            ->sum('quantity');

        return view('sites.instocksite', compact('site_id', 'siteInStocks', 'totalQuantity'));
    }

    public function loadData(Request $request)
    {
        $site_id = session('site_id');
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');
        $searchTerm = $request->input('search');
        $page = $request->input('page', 1);
        $perPage = $request->input('per_page', 5); // Default to 5 items per page

        $query = SiteInStock::join('part_inventories', 'site_in_stocks.part_inventory_id', '=', 'part_inventories.part_inventory_id')
            ->join('parts', 'part_inventories.part_code', '=', 'parts.part_code')
            ->where('part_inventories.site_id', $site_id)
            ->select('site_in_stocks.*', 'parts.part_name', 'parts.part_code')
            ->orderBy('date_in', 'desc');

        if ($startDate && $endDate) {
            $query->whereBetween('date_in', [$startDate, $endDate]);
        }

        if ($searchTerm) {
            $query->where(function ($q) use ($searchTerm) {
                $q->where('parts.part_name', 'like', '%' . $searchTerm . '%')
                    ->orWhere('parts.part_code', 'like', '%' . $searchTerm . '%');
            });
        }

        // Calculate total quantity for all matching records
        $totalQuantity = $query->sum('quantity');

        // Get total count for pagination
        $total = $query->count();

        // Get paginated data
        $siteInStocks = $query->skip(($page - 1) * $perPage)
            ->take($perPage)
            ->get();

        return response()->json([
            'data' => $siteInStocks,
            'current_page' => (int)$page,
            'per_page' => (int)$perPage,
            'last_page' => ceil($total / $perPage),
            'total' => $total,
            'total_quantity' => $totalQuantity
        ]);
    }

    public function gettransactionsite(Request $request)
    {
        $page = $request->input('page', 1);
        $perPage = $request->input('per_page', 5); // Default to 5 items per page

        $query = StockTransaction::where('destination_siteid', session('site_id'))
            ->whereNotIn('status', ['selesai', 'return'])
            ->with('part', 'site')
            ->orderBy('created_at', 'desc');

        // Get total count for pagination
        $total = $query->count();

        // Get paginated data
        $transactions = $query->skip(($page - 1) * $perPage)
            ->take($perPage)
            ->get();

        return response()->json([
            'data' => $transactions,
            'current_page' => (int)$page,
            'per_page' => (int)$perPage,
            'last_page' => ceil($total / $perPage),
            'total' => $total
        ]);
    }

    public function getTransactionCount()
    {
        try {
            $count = StockTransaction::where('destination_siteid', session('site_id'))
                ->whereNotIn('status', ['selesai', 'return'])
                ->count();

            return response()->json(['count' => $count]);
        } catch (\Exception $e) {
            Log::error('Error getting transaction count: ' . $e->getMessage());
            return response()->json(['count' => 0]);
        }
    }

    public function create(Request $request)
    {
        $site_id = session('site_id');

        $validator = Validator::make($request->all(), [
            'part_code' => 'required|string',
            'quantity' => 'required|numeric|min:0.1',
            'date_in' => 'required|date',
            'notes' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $partInventory = PartInventory::where('part_code', $request->input('part_code'))
            ->where('site_id', $site_id)
            ->first();
        if (!$partInventory) {
            return response()->json(['error' => 'Silahkan Pilih Part Dengan Benar!'], 400);
        }

        DB::beginTransaction();
        try {
            $siteInStock = new SiteInStock();
            $siteInStock->part_inventory_id = $partInventory->part_inventory_id;
            $siteInStock->employee_id = session('employee_id');
            $siteInStock->quantity = $request->input('quantity');
            $siteInStock->date_in = $request->input('date_in');
            $siteInStock->notes = $request->input('notes');
            $siteInStock->save();

            $partInventory->stock_quantity += $request->input('quantity');
            $partInventory->save();

            $partname = Part::where('part_code', $request->input('part_code'))->first()->part_name;

            // Create log activity directly
            LogAktivitas::create([
                'site_id' => session('site_id'),
                'name' => session('name'),
                'action' => 'Menambahkan In Part',
                'description' => "User " . session('name') . " menambahkan " . $partname . " sebanyak " . $request->input('quantity'),
                'table' => "In stock",
                'ip_address' => $request->ip(),
            ]);

            DB::commit();
            return response()->json(['success' => 'In-stock record created successfully.']);
        } catch (\Exception $e) {
            DB::rollback();
            Log::error($e);
            return response()->json(['error' => 'Failed to create in-stock record: ' . $e->getMessage()], 500);
        }
    }

    public function delete(Request $request)
    {
        DB::beginTransaction();
        try {
            $siteInStockId = $request->input('site_in_stock_id');
            $siteInStock = SiteInStock::find($siteInStockId);
            if (!$siteInStock) {
                throw new \Exception('In-stock record not found.');
            }

            $partInventoryId = $siteInStock->part_inventory_id;
            $partInventory = PartInventory::find($partInventoryId);
            if (!$partInventory) {
                throw new \Exception('Part inventory not found.');
            }

            if ($partInventory->stock_quantity < $siteInStock->quantity) {
                throw new \Exception('Gagal menghapus! Stok tidak mencukupi karena beberapa part sudah keluar dari inventory');
            }

            $part_code = PartInventory::where('part_inventory_id', $partInventoryId)->first()->part_code;
            $partname = Part::where('part_code', $part_code)->first()->part_name;
            $quantity = SiteInStock::where('part_inventory_id', $partInventoryId)->first()->quantity;

            $partInventory->stock_quantity -= $siteInStock->quantity;
            $partInventory->save();
            $siteInStock->delete();

            // Create log activity directly
            LogAktivitas::create([
                'site_id' => session('site_id'),
                'name' => session('name'),
                'action' => 'Menghapus In Part',
                'description' => "User " . session('name') . " menghapus " . $partname . " sebanyak " . $quantity,
                'table' => "In stock",
                'ip_address' => $request->ip(),
            ]);

            DB::commit();
            return response()->json(['success' => 'In-stock record deleted successfully.']);
        } catch (\Exception $e) {
            DB::rollback();
            Log::error($e);
            return response()->json(['error' => 'Failed to delete in-stock record: ' . $e->getMessage()], 500);
        }
    }

    public function getParts(Request $request)
    {
        $site_id = session('site_id');
        $searchTerm = $request->input('search');
        $parts = Part::join('part_inventories', 'parts.part_code', '=', 'part_inventories.part_code')
            ->where('part_inventories.site_id', $site_id)
            ->where('parts.part_name', 'like', '%' . $searchTerm . '%')
            ->select('parts.part_code', 'parts.part_name', 'parts.part_type')
            ->distinct()
            ->get();
        return response()->json($parts);
    }


    // proses adjust penerimaan dari warehouse
    public function processAdjustment(Request $request, $transactionId)
    {
        DB::beginTransaction();
        try {
            $transaction = StockTransaction::findOrFail($transactionId);
            if ($request->input('quantity_received') <= $transaction->quantity_sent) {
                $partInventory = PartInventory::where('site_id', $transaction->destination_siteid)
                    ->where('part_code', $transaction->part_code)
                    ->first();

                if ($transaction->requisition_details_id) {
                    $this->updateRequisitionConfirmation(
                        $transaction->requisition_details_id,
                        $request->input('quantity_received')
                    );
                }

                if (!$partInventory) {
                    throw new \Exception("PartInventory tidak ditemukan di destination site.");
                }

                // Create all the necessary records
                $siteInStock = new SiteInStock();
                $siteInStock->part_inventory_id = $partInventory->part_inventory_id;
                $siteInStock->employee_id = session('employee_id');
                $siteInStock->quantity = $request->input('quantity_received');
                $siteInStock->date_in = Carbon::now();
                $siteInStock->notes = 'In stock hasil konfirmasi sebagian dari HO';
                $siteInStock->save();

                // Get part name for logging
                $partname = Part::where('part_code', $transaction->part_code)->first()->part_name;

                // Get warehouse part inventory for the same part
                $warehouseInventory = PartInventory::where('part_code', $transaction->part_code)
                    ->where('site_id', 'WHO')
                    ->first();

                if (!$warehouseInventory) {
                    throw new \Exception("Warehouse inventory not found for part code: {$transaction->part_code}");
                }

                // Create out stock record for warehouse
                WarehouseOutStock::create([
                    'part_inventory_id' => $warehouseInventory->part_inventory_id,
                    'date_out' => $transaction->created_at,
                    'employee_id' => session('employee_id'),
                    'quantity' => $request->input('quantity_received'),
                    'status' => 'selesai',
                    'notes' => 'Site Menerima Out Stock Sebagian',
                    'destination_type' => 'site',
                    'destination_id' => session('site_id'),
                ]);

                // Log the warehouse out stock activity
                LogAktivitas::create([
                    'site_id' => 'WHO',
                    'name' => session('name'),
                    'action' => 'Warehouse Out Stock Sebagian',
                    'description' => "Part " . $partname . " sebanyak " . $request->input('quantity_received') . " dikirim ke site " . session('site_id'),
                    'table' => "Warehouse Out Stock",
                    'ip_address' => $request->ip(),
                ]);

                $addInventory = PartInventory::where('part_code', $transaction->part_code)
                    ->where('site_id', $transaction->destination_siteid)
                    ->first();

                if (!$addInventory) {
                    throw new \Exception("PartInventory tidak ditemukan di destination site saat update.");
                }

                $addInventory->stock_quantity += $request->input('quantity_received');
                $addInventory->save();

                // Create log activity
                $partname = Part::where('part_code', $transaction->part_code)->first()->part_name;
                LogAktivitas::create([
                    'site_id' => session('site_id'),
                    'name' => session('name'),
                    'action' => 'Konfirmasi In Stock sebagian',
                    'description' => "User " . session('name') . " menambahkan " . $partname . " sebanyak " . $request->input('quantity_received'),
                    'table' => "In stock",
                    'ip_address' => $request->ip(),
                ]);

                // Update transaction
                $transaction->quantity_received = $request->input('quantity_received');
                $transaction->discrepancy_reason = $request->input('discrepancy_reason');
                $transaction->notes = $request->input('notes');
                $transaction->quantity_discrepancy = $transaction->quantity_sent - $request->input('quantity_received');
                $transaction->updated_at = Carbon::now();
                $transaction->discrepancy_type = $request->input('discrepancy_type');
                $transaction->status = ($transaction->quantity_discrepancy == 0) ? 'selesai' : 'pending';
                $transaction->save();

                DB::commit();
                return response()->json(['success' => true, 'message' => 'Penyesuaian berhasil disimpan!']);
            } else {
                DB::rollback();
                return response()->json(['success' => false, 'message' => 'Maaf quota yang dimasukka melebihi pengiriman !']);
            }
        } catch (\Exception $e) {
            DB::rollback();
            Log::error($e);
            return response()->json(['success' => false, 'message' => 'Terjadi kesalahan: ' . $e->getMessage()]);
        }
    }

    public function processConfirmation(Request $request, string $id)
    {
        try {
            DB::beginTransaction();
            $transaction = StockTransaction::findOrFail($id);

            // Check if this transaction is related to a requisition
            if ($transaction->requisition_details_id) {
                $this->updateRequisitionConfirmation(
                    $transaction->requisition_details_id,
                    $transaction->quantity_sent
                );
            }
            // If no requisition_details_id, it's a direct warehouse shipment - continue processing

            // Cari ParInventory di DESTINATION site untuk penerimaan
            $partInvtentory = PartInventory::where('site_id', $transaction->destination_siteid)
                ->where('part_code', $transaction->part_code)
                ->first();

            if (!$partInvtentory) {
                throw new \Exception("PartInventory tidak ditemukan di destination site.");
            }

            $siteInStock = new SiteInStock();
            $siteInStock->part_inventory_id = $partInvtentory->part_inventory_id;
            $siteInStock->employee_id = session('employee_id');
            $siteInStock->quantity = $transaction->quantity_sent;
            $siteInStock->date_in = Carbon::now();
            $siteInStock->notes = 'Konfirmasi Pengiriman HO';
            $siteInStock->save();

            // Cari PartInventory di DESTINATION site untuk UPDATE
            $addInventory = PartInventory::where('part_code', $transaction->part_code)
                ->where('site_id', $transaction->destination_siteid) //Perbaiki: destination siteid
                ->first();

            if ($addInventory) {
                $addInventory->stock_quantity += $transaction->quantity_sent;
                $addInventory->save();

                // Get part name for logging
                $partname = Part::where('part_code', $transaction->part_code)->first()->part_name;

                // Get warehouse part inventory for the same part
                $warehouseInventory = PartInventory::where('part_code', $transaction->part_code)
                    ->where('site_id', 'WHO')
                    ->first();

                if (!$warehouseInventory) {
                    throw new \Exception("Warehouse inventory not found for part code: {$transaction->part_code}");
                }

                // Create out stock record for warehouse
                WarehouseOutStock::create([
                    'part_inventory_id' => $warehouseInventory->part_inventory_id,
                    'date_out' => $transaction->created_at,
                    'employee_id' => session('employee_id'),
                    'quantity' => $transaction->quantity_sent,
                    'status' => 'selesai',
                    'notes' => 'Konfirmasi site',
                    'destination_type' => 'site',
                    'destination_id' => session('site_id'),
                ]);

                // Log the warehouse out stock activity
                LogAktivitas::create([
                    'site_id' => 'WHO',
                    'name' => session('name'),
                    'action' => 'Warehouse Out Stock',
                    'description' => "Part " . $partname . " sebanyak " . $transaction->quantity_sent . " dikirim ke site " . session('site_id'),
                    'table' => "Warehouse Out Stock",
                    'ip_address' => $request->ip(),
                ]);
            } else {
                throw new \Exception("PartInventory tidak ditemukan di destination site saat update."); // Pesan error lebih spesifik
            }

            // Log activity for site in stock
            LogAktivitas::create([
                'site_id' => session('site_id'),
                'name' => session('name'),
                'action' => 'Konfirmasi In Stock',
                'description' => "User " . session('name') . " menambahkan " . $partname . " sebanyak " . $transaction->quantity_sent,
                'table' => "In stock",
                'ip_address' => $request->ip(),
            ]);
            $transaction->quantity_received = $transaction->quantity_sent;
            $transaction->quantity_returned = 0;
            $transaction->updated_at = Carbon::now();
            $transaction->status = 'selesai'; // Menggunakan status yang valid sesuai dengan definisi enum
            $transaction->save();

            DB::commit();

            return response()->json(['success' => true, 'message' => 'Konfirmasi berhasil.']);
        } catch (\Exception $e) {
            DB::rollback();
            Log::error($e);
            return response()->json(['success' => false, 'message' => 'Terjadi kesalahan: ' . $e->getMessage()]);
        }
    }

    // INi digunakan jika proses input berasal dari request untuk melakukan follwup
    private function updateRequisitionConfirmation($requisitionDetailsId, $quantityReceived)
    {
        DB::beginTransaction();
        try {
            $requisitionDetail = RequisitionDetail::where('requisition_details_id', $requisitionDetailsId)
                ->first();

            if (!$requisitionDetail) {
                throw new \Exception('Requisition detail not found');
            }
            $requisition = Requisition::with('requisitionDetails')
                ->where('requisition_id', $requisitionDetail->requisition_id)
                ->first();

            if (!$requisition) {
                throw new \Exception('Parent requisition not found');
            }

            $totalConfirmed = $requisitionDetail->quantity_confirm + $quantityReceived;
            $requisitionDetail->quantity_confirm = $totalConfirmed;

            if ($totalConfirmed < $requisitionDetail->quantity_send) {
                $unconfirmedQuantity = $requisitionDetail->quantity_send - $totalConfirmed;
                $requisitionDetail->quantity += $unconfirmedQuantity;
                $requisitionDetail->quantity_send = $totalConfirmed;

                $requisitionDetail->status_details = 'dikirim sebagian';
            } else if ($requisitionDetail->quantity == 0 && $requisitionDetail->quantity_send == $totalConfirmed) {
                $requisitionDetail->status_details = 'selesai';
            }

            $requisitionDetail->save();

            $allDetails = $requisitionDetail->requisition->requisitionDetails;
            $allComplete = $allDetails->every(function ($detail) {
                return $detail->status_details === 'selesai';
            });

            if ($allComplete) {
                $requisitionDetail->requisition->status = 'selesai';
            } else {
                $requisitionDetail->requisition->status = 'pending';
            }
            $requisitionDetail->requisition->save();

            Log::info('update selesai');
            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Error updating requisition confirmation: ' . $e->getMessage());
            throw $e;
        }
    }
}
