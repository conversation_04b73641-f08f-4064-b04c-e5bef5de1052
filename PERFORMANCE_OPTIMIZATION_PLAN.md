# Performance Optimization Plan - Division Filter Implementation

## Current Performance Analysis

### Query Performance Issues Identified

1. **Missing Database Indexes**
   - `tanggal_invoice` - heavily used in date range queries
   - `payment_status` - used in filtering operations
   - `part_type` - critical for division filtering
   - `site_id` - used in site filtering

2. **N+1 Query Problems**
   - Multiple eager loading relationships in single queries
   - Deep nested relationships (4-5 levels deep)
   - Potential for excessive database calls

3. **Large Dataset Processing**
   - Processing entire invoice collections in memory
   - No pagination for internal calculations
   - Repeated calculations for same data

## Recommended Optimizations

### 1. Database Indexes (High Priority)

Create indexes for frequently queried columns:

```sql
-- Invoice table indexes
CREATE INDEX idx_invoices_tanggal_invoice ON invoices(tanggal_invoice);
CREATE INDEX idx_invoices_payment_status ON invoices(payment_status);
CREATE INDEX idx_invoices_penawaran_id ON invoices(penawaran_id);
CREATE INDEX idx_invoices_site_id ON invoices(site_id);

-- Parts table indexes  
CREATE INDEX idx_parts_part_type ON parts(part_type);

-- Unit transactions indexes
CREATE INDEX idx_unit_transactions_site_id ON unit_transactions(site_id);

-- Composite indexes for common query patterns
CREATE INDEX idx_invoices_date_status ON invoices(tanggal_invoice, payment_status);
CREATE INDEX idx_invoices_date_site ON invoices(tanggal_invoice, site_id);
```

### 2. Query Optimization (Medium Priority)

#### A. Selective Eager Loading
Instead of loading all relationships, load only what's needed:

```php
// Current (loads everything)
$query = Invoice::with(['unitTransactions.parts.partInventory.part', 'penawaran.items.partInventory.part', 'manualInvoiceParts.part']);

// Optimized (conditional loading)
$with = ['unitTransactions.parts.partInventory.part'];
if ($hasPenawaran) {
    $with[] = 'penawaran.items.partInventory.part';
}
if ($hasManualParts) {
    $with[] = 'manualInvoiceParts.part';
}
$query = Invoice::with($with);
```

#### B. Database-Level Filtering
Move division filtering to database level where possible:

```php
// Current (application-level filtering)
foreach ($invoices as $invoice) {
    $total = $this->calculateInvoiceTotal($invoice, $divisionFilter);
}

// Optimized (database-level filtering)
if ($divisionFilter) {
    $query->where(function($q) use ($divisionFilter) {
        $q->whereHas('unitTransactions.parts.partInventory.part', function($sq) use ($divisionFilter) {
            $sq->where('part_type', $divisionFilter);
        })
        ->orWhereHas('penawaran.items.partInventory.part', function($sq) use ($divisionFilter) {
            $sq->where('part_type', $divisionFilter);
        })
        ->orWhereHas('manualInvoiceParts.part', function($sq) use ($divisionFilter) {
            $sq->where('part_type', $divisionFilter);
        });
    });
}
```

### 3. Caching Strategy (Medium Priority)

#### A. Dashboard Data Caching
Cache expensive calculations for frequently accessed data:

```php
public function getCachedMonthlyInvoiceData($year, $divisionFilter = null, $siteFilter = null)
{
    $cacheKey = "monthly_invoice_data_{$year}_{$divisionFilter}_{$siteFilter}";
    
    return Cache::remember($cacheKey, 3600, function() use ($year, $divisionFilter, $siteFilter) {
        return $this->getMonthlyInvoiceData($year, $divisionFilter, $siteFilter);
    });
}
```

#### B. Part Type Caching
Cache part types to avoid repeated queries:

```php
public function getCachedPartTypes()
{
    return Cache::remember('part_types', 86400, function() {
        return Part::distinct()->pluck('part_type')->toArray();
    });
}
```

### 4. Memory Optimization (Low Priority)

#### A. Chunked Processing
For large datasets, process in chunks:

```php
Invoice::whereBetween('tanggal_invoice', [$startDate, $endDate])
    ->chunk(100, function($invoices) use ($divisionFilter) {
        foreach ($invoices as $invoice) {
            // Process invoice
        }
    });
```

#### B. Lazy Loading
Use lazy collections for memory-efficient processing:

```php
$invoices = Invoice::whereBetween('tanggal_invoice', [$startDate, $endDate])
    ->cursor(); // Returns LazyCollection
```

## Implementation Priority

### Phase 1: Critical (Immediate)
1. ✅ **Database Indexes** - Create missing indexes
2. ✅ **Query Optimization** - Optimize most expensive queries

### Phase 2: Important (Next Sprint)
1. **Caching Implementation** - Add strategic caching
2. **Selective Loading** - Optimize relationship loading

### Phase 3: Enhancement (Future)
1. **Memory Optimization** - Implement chunked processing
2. **Advanced Caching** - Redis/Memcached integration

## Performance Metrics

### Current Performance (Estimated)
- Dashboard load time: 2-5 seconds
- Monthly chart generation: 1-3 seconds
- Division filter application: 0.5-2 seconds

### Target Performance (After Optimization)
- Dashboard load time: 0.5-1.5 seconds (70% improvement)
- Monthly chart generation: 0.2-0.8 seconds (75% improvement)
- Division filter application: 0.1-0.5 seconds (80% improvement)

## Monitoring and Measurement

### Key Metrics to Track
1. **Query Execution Time**
   - Dashboard load queries
   - Filter application queries
   - Monthly chart queries

2. **Memory Usage**
   - Peak memory during calculations
   - Memory growth patterns

3. **Cache Hit Rates**
   - Dashboard data cache hits
   - Part type cache hits

### Tools for Monitoring
- Laravel Telescope (query monitoring)
- Laravel Debugbar (performance profiling)
- Database query logs
- Application performance monitoring (APM)

## Risk Assessment

### Low Risk Optimizations
- ✅ Database indexes (no code changes)
- ✅ Caching (fallback to original logic)

### Medium Risk Optimizations
- Query restructuring (requires testing)
- Relationship loading changes (potential breaking changes)

### High Risk Optimizations
- Major algorithm changes
- Database schema modifications

## Implementation Status

- ✅ **Analysis Complete**: Performance bottlenecks identified
- ⏳ **Database Indexes**: Ready to implement
- ⏳ **Query Optimization**: Design phase
- ⏳ **Caching Strategy**: Planning phase

---

**Next Steps**: Implement Phase 1 optimizations (database indexes and critical query optimizations)
