<!DOCTYPE html>
<html lang="id">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TAR Daily Report - {{ $dailyReport->unit->unit_code ?? 'N/A' }}</title>
    <style>
        @page {
            margin: 0;
            padding: 0;
            size: A4 portrait;
        }

        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            font-size: 10px;
            line-height: 1.2;
            font-weight: bold;
            color: #000;
            position: relative;
        }

        .template-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 210mm;
            height: 297mm;
            z-index: 1;
        }

        .template-background img {
            width: 100%;
            height: 100%;
            object-fit: fill;
        }

        .content-overlay {
            position: relative;
            z-index: 2;
            width: 210mm;
            height: 297mm;
            padding: 0;
            margin: 0;
        }

        /* Header Right Form Fields */
        .header-form {
            position: absolute;
            top: 17.5mm;
            right: 15mm;
        }

        .form-field {
            font-size: 12px;
            margin-bottom: 2mm;
            color: #000;

        }

        /* Data Unit Fields */
        .data-fields {
            position: absolute;
            top: 35mm;
            left: 22mm;
            right: 15mm;
        }

        .field-left {
            position: absolute;
            left: 0;
            width: 85mm;
        }

        .field-right {
            position: absolute;
            right: 0;
            width: 100mm;
        }

        .field-item {
            font-size: 10px;
            font-weight: bold;
            color: #000;
        }

        .field-value {
            display: inline-block;
            margin-left: 2mm;
            font-weight: normal;
        }

        /* Problem Issue Section */
        .problem-section {
            position: absolute;
            top: 145mm;
            left: 15mm;
            right: 15mm;
            height: 25mm;
        }

        .problem-content {
            font-size: 9px;
            color: #000;
            padding: 2mm;
            line-height: 1.3;
        }

        /* Parts Failure Analysis Section */
        .analysis-section {
            position: absolute;
            top: 175mm;
            left: 15mm;
            right: 15mm;
            height: 30mm;
        }

        .analysis-content {
            font-size: 9px;
            color: #000;
            padding: 2mm;
            line-height: 1.3;
        }


        .parts-table {
            width: 100%;
            border-collapse: collapse;
        }

        .parts-table td {
            text-align: center;
            justify-content: center;
            align-items: center;
            color: #000;
            font-size: 8px;
        }

        /* Picture Component Failure Section */
        .picture-section {
            top: 138mm;
            left: 15mm;
        }

        .picture-label {
            font-size: 6px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 1mm;
            color: #000;
        }

        .picture-content img {
            border: 1px solid #ccc;
        }

        /* Correction Action Section */
        .correction-section {
            position: absolute;
            top: 265mm;
            left: 15mm;
            right: 15mm;
            height: 12mm;
        }

        .correction-content {
            font-size: 8px;
            color: #000;
            padding: 1mm;
            line-height: 1.2;
        }

        /* Recommendation Section */
        .recommendation-section {
            position: absolute;
            top: 280mm;
            left: 15mm;
            right: 15mm;
            height: 12mm;
        }

        .recommendation-content {
            font-size: 8px;
            color: #000;
            padding: 1mm;
            line-height: 1.2;
        }

        table td {
            border: rgba(155, 252, 0, 0) solid 2.4px;
        }
    </style>
</head>

<body>
    <!-- Background Template Image -->
    <div class="template-background">
        <img src="{{ public_path('assets/images/tartemplate.png') }}" alt="TAR Template">
    </div>

    <!-- Content Overlay -->
    <div class="content-overlay">

        <!-- Header Form Fields (Top Right) -->
        <div class="header-form">
            <div class="form-field">PT. PUTERA WIBOWO BORNEO</div>
            <div class="form-field">AC UNIT</div>
        </div>

        <!-- Data Unit Fields -->
        <div class="data-fields">
            <table style="font-size: 10px; font-weight: bold; border:rgba(155, 252, 0, 0) solid 2px;">
                <tr>
                    <td style="width: 82px;height: 18px; padding: 2px; align-items: center;">{{ $tarData['unit_code'] ?? '' }}</td>
                    <td style="width: 65px;height: 18px; padding: 2px;"></td>
                    <td style="width: 325px;height: 20px;"></td>
                    <td style="width: 200px;height: 20px;">{{ $tarData['problem_component'] ?? '' }}</td>
                </tr>
                <tr>
                    <td>{{ $tarData['typeunit'] ?? $tarData['typeunit'] ?? '' }}</td>
                    <td>{{ $tarData['technician_1'] ?? '' }}</td>
                    <td>{{ $tarData['technician_3'] ?? '' }}</td>
                    <td>{{ $tarData['lifetime_component'] ?? '' }}</td>
                </tr>
                <tr>
                    <td style="padding-top: 4px;">{{ $tarData['hm'] ?? '' }}</td>
                    <td style="padding-top: 4px;">{{ $tarData['technician_2'] ?? '' }}</td>
                    <td style="padding-top: 4px;">{{ $tarData['technician_4'] ?? '' }}</td>
                    <td style="padding-top: 4px;">{{ $tarData['date_in'] ?? '' }}</td>
                </tr>
            </table>
        </div>
        <div style="left: 8mm; top: 63mm; position: absolute;">
            <table>
                <tr>
                    <td>{{ $tarData['problem_description'] ?? '' }}</td>
                </tr>
            </table>
        </div>

        <div style="left: 8mm; top: 77mm; position: absolute;">
            <table>
                <tr>
                    <td>{{ $tarData['component_failure'] ?? '' }}</td>
                </tr>
            </table>
        </div>

        <!-- Main Parts Problem Table -->
        <div style=" position: absolute;
            top: 110.5mm;
            left: 5mm;
            right: 15mm;
            height: 20mm;">
            <table class="parts-table">
                <tbody>
                    <tr>
                        <td style="width: 155px; position: relative; height: 12px;">{{ $tarData['part_name_1'] }}</td>
                        <td style="width: 210px;">{{ $tarData['code_part_1'] }}</td>
                        <td style="width: 50px;">{{ $tarData['quantity_1'] }}</td>
                        <td style="width: 260px;">{{ $tarData['remarks_1'] }}</td>
                    </tr>
                    <tr>
                        <td style="width: 155px; position: relative; height: 12px;">{{ $tarData['part_name_2'] }}</td>
                        <td style="width: 210px;">{{ $tarData['code_part_2'] }}</td>
                        <td style="width: 50px;">{{ $tarData['quantity_2'] }}</td>
                        <td style="width: 260px;">{{ $tarData['remarks_2'] }}</td>
                    </tr>
                    <tr>
                        <td style="width: 155px; position: relative; height: 13px;">{{ $tarData['part_name_3'] }}</td>
                        <td style="width: 210px;">{{ $tarData['code_part_3'] }}</td>
                        <td style="width: 50px;">{{ $tarData['quantity_3'] }}</td>
                        <td style="width: 260px;">{{ $tarData['remarks_3'] }}</td>
                    </tr>
                    <tr>
                        <td style="width: 155px; position: relative; height: 13px;">{{ $tarData['part_name_4'] }}</td>
                        <td style="width: 210px;">{{ $tarData['code_part_4'] }}</td>
                        <td style="width: 50px;">{{ $tarData['quantity_4'] }}</td>
                        <td style="width: 260px;">{{ $tarData['remarks_4'] }}</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Picture Component Failure Section -->
        <div class="picture-section">
            <div style="top: 140mm; left: 10mm; position: absolute; width: 100%; align-items: center; text-align: center;">
                <table style="align-items: center; justify-content: center;">
                    <tr>
                        @if(isset($tarData['image_1']) && $tarData['image_1'])
                        <td style="max-width: 200px; padding-right: 50px; align-items: flex-start;">
                            <div>
                                <div style="text-align: center; font-size: 16px;">Gambar Sebelum</div>
                                <img style="width: 200px; height: 270px;" src="{{ $tarData['image_1'] }}" alt="Gambar Sebelum">
                            </div>
                        </td>
                        @endif
                        @if(isset($tarData['image_2']) && $tarData['image_2'])
                        <td style="max-width: 200px; padding-right: 50px; align-items: flex-start;">
                            <div>
                                <div style="text-align: center; font-size: 16px;">Gambar Sesudah</div>
                                <img style="width: 200px; height: 270px;" src="{{ $tarData['image_2'] }}" alt="Gambar Sesudah">
                            </div>
                        </td>
                        @endif
                        @if(isset($tarData['image_3']) && $tarData['image_3'])
                        <td style="max-width: 200px; padding-right: 50px; align-items: flex-start;">
                            <div>
                                <div style="text-align: center; font-size: 16px;">Gambar Unit</div>
                                <img style="width: 200px; height: 270px;" src="{{ $tarData['image_3'] }}" alt="Gambar Unit">
                            </div>
                        </td>
                        @endif
                    </tr>
                </table>
            </div>
        </div>

        <!-- Plan Fix Section -->
        <table style="top: 233mm; left: 8mm; position: absolute;font-size: 12px;">
            <tr>
                <td style="width: 100%; height: 10px;">{{ $tarData['plan_fix'] ?? '' }}</td>
            </tr>
        </table>

        <!-- Plan Rekomen Section -->
        <table style="top: 252mm; left: 8mm; position: absolute;font-size: 12px;">
            <tr>
                <td style="width: 100%; height: 10px;">{{ $tarData['plan_rekomen'] ?? '' }}</td>
            </tr>
        </table>


        <!-- Recommendation Preventive Action Section -->
        <div class="recommendation-section">
            <div class="recommendation-content">
                <!-- This section can be filled manually or from additional data -->
            </div>
        </div>

        <!-- PJO -->
        <div style="top: 280mm; left: 96mm; position: absolute; width: 25mm; height: 4.5mm; font-size: 9px; font-weight: bold; padding-top: 2px;">
            {{ $tarData['pjo'] ?? '' }}
        </div>
    </div>
</body>

</html>