<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Backlog extends Model
{
    use HasFactory;

    protected $fillable = [
        'unit_code',
        'hm_found',
        'problem_description',
        'backlog_job',
        'plan_hm',
        'status',
        'plan_pull_date',
        'notes',
    ];

    protected $casts = [
        'hm_found' => 'float',
        'plan_hm' => 'float',
        'plan_pull_date' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the unit that owns the backlog.
     */
    public function unit()
    {
        return $this->belongsTo(Unit::class, 'unit_code', 'unit_code');
    }

    /**
     * Get the parts associated with the backlog.
     */
    public function backlogParts()
    {
        return $this->hasMany(BacklogPart::class);
    }

    /**
     * Get the parts associated with the backlog through the pivot table.
     */
    public function parts()
    {
        return $this->hasManyThrough(Part::class, BacklogPart::class, 'backlog_id', 'part_code', 'id', 'part_code');
    }

    /**
     * Scope to get only open backlogs
     */
    public function scopeOpen($query)
    {
        return $query->where('status', 'OPEN');
    }

    /**
     * Scope to get backlogs for a specific unit
     */
    public function scopeForUnit($query, $unitCode)
    {
        return $query->where('unit_code', $unitCode);
    }

    /**
     * Get formatted created date
     */
    public function getFormattedCreatedDateAttribute()
    {
        return $this->created_at->format('d/m/Y');
    }

    /**
     * Get formatted plan pull date
     */
    public function getFormattedPlanPullDateAttribute()
    {
        return $this->plan_pull_date?->format('d/m/Y H:i');
    }

    /**
     * Get status badge class
     */
    public function getStatusBadgeClassAttribute()
    {
        return match($this->status) {
            'OPEN' => 'bg-warning text-dark',
            'CLOSED' => 'bg-success',
            default => 'bg-secondary'
        };
    }

    /**
     * Get available status options
     */
    public static function getStatusOptions()
    {
        return [
            'OPEN' => 'Open',
            'CLOSED' => 'Closed'
        ];
    }
}
