<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>Store Slip</title>
    <style>
        body {
            font-family: 'Times New Roman', Times, serif;
            font-size: 11px;
            line-height: 1.4;
            margin: 20px;
        }

        /* Header styles */
        .header-container {
            width: 100%;
            margin-bottom: 15px;
        }

        .logo-left {
            float: left;
            width: 20%;
        }

        .title-center {
            float: left;
            width: 60%;
            text-align: center;
        }

        .number-right {
            float: right;
            width: 20%;
            text-align: right;
        }

        .header-title {
            background-color: #1a3c5a;
            color: white;
            padding: 8px;
            font-size: 14px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 15px;
        }

        /* Customer info table */
        .customer-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }

        .customer-table td {
            padding: 3px;
            border: 1px solid #000;
        }

        .customer-table .label {
            font-weight: bold;
            width: 20%;
            background-color: #f2f2f2;
        }

        /* Main table styles */
        .main-table {
            width: 100%;
            border-collapse: collapse;
        }

        .main-table th,
        .main-table td {
            border: 1px solid #000;
            padding: 1px;
            text-align: center;
            font-size: 10px;
        }

        .main-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }

        .main-table .text-left {
            text-align: left;
        }

        .main-table .text-right {
            text-align: right;
        }

        /* Total table */
        .total-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }

        .total-table td {
            padding: 5px;
            border: 1px solid #000;
            text-align: right;
        }

        .total-table .total-label {
            font-weight: bold;
        }

        /* Signature section */
        .signature-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 0;
            padding-top: 0;
        }

        .signature-table td {
            width: 50%;
            text-align: center;
            vertical-align: top;
            font-weight: bold;
            font-size: 10px;
        }

        .signature-stamp {
            width: 100px;
            height: 100px;
            margin: 0 auto;
            position: relative;
        }
        .clearfix::after {
            content: "";
            clear: both;
            display: table;
        }
        /* Adjust for portrait orientation */
        @page {
            size: portrait;
        }
    </style>
</head>
<body>
    <!-- Header with logo, title and number -->
    <!-- Header Table -->
    <table style="width: 100%; border-collapse: collapse; border: 1px solid #000;">
        <tr>
            <!-- Logo and Title Cell -->
            <td style="width: 15%; border: 1px solid #000;  vertical-align: top;">
                <table style="width: 100%; margin-bottom: 0; padding-bottom: 0;">
                    <tr style="margin-bottom: 0; padding-bottom: 0;">
                        <td style="width: 20px; vertical-align: top; margin-bottom: 0; padding-bottom: 0;">
                            <img src="{{ public_path('assets/images/logo-nom.png') }}" alt="PWB LOGO" style="width: 35px;">
                        </td>
                        <td style="vertical-align: top; padding-left: 5px; margin-bottom: 0; padding-bottom: 0;">
                            <div style="font-weight:900; font-size: 20px; display: serif; line-height: 1; text-align: center;">
                                STORE<br>
                                <p  style="font-weight: bold; font-size: 30px; line-height: 0.8; text-align: center; padding: 0; margin: 0;">SLIP</p>
                            </div>
                        </td>
                    </tr>
                </table>
                <div style="text-align: center; font-size: 8px; font-weight: bold; color:rgb(9, 61, 112); margin-top: 2px; letter-spacing: -0.4px; margin-top: 0; padding-top: 0;">
                    PT. PUTERA WIBOWO BORNEO
                </div>
            </td>



            <!-- Date and Customer Info -->
            <td style="width: 35%; border: 1px solid #000; padding: 5px; vertical-align: top;">
                <table style="width: 100%; border-collapse: collapse;">
                    <tr>
                        <td style="width: 30%; font-weight: bold;">DATE</td>
                        <td>: {{ \Carbon\Carbon::parse($transactions[0]->updated_at ?? \Carbon\Carbon::now())->format('d F Y') }}</td>
                    </tr>
                    <tr>
                        <td style="width: 30%; font-weight: bold;">CUSTOMER</td>
                        <td>: {{ $transactions[0]->customer }}</td>
                    </tr>
                    <tr>
                        <td style="width: 30%; font-weight: bold;">SITE</td>
                        <td>: {{ $transactions[0]->sitework }}</td>
                    </tr>
                </table>
            </td>

            <!-- Unit Info -->
            <td style="width: 35%; border: 1px solid #000; padding: 5px; vertical-align: top;">
                <table style="width: 100%; border-collapse: collapse;">
                    <tr>
                        <td style="width: 30%; font-weight: bold;">WO/PO</td>
                        <td>:  {{ $transactions[0]->wo_number ?? '' }}{{ isset($transactions[0]->po_number) ? '/' . $transactions[0]->po_number : '' }}</td>
                    </tr>
                    <tr>
                        <td style="width: 30%; font-weight: bold;">NO. UNIT</td>
                        <td>: {{ $transactions[0]->unit->unit_code ?? '-' }}</td>
                    </tr>
                    <tr>
                        <td style="width: 30%; font-weight: bold;">MODEL</td>
                        <td>: {{ $transactions[0]->unit->unit_type ?? '-' }}</td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>

    <!-- Main Content Table -->
    <table class="main-table">
        <thead>
            <tr>
                <th style="width:5%;">NO.</th>
                <th style="width:35%;">PART NAME</th>
                <th style="width:15%;">BRAND</th>
                <th style="width:20%;">PART NUMBER</th>
                <th style="width:10%;">QTY ORDER</th>
                <th style="width:15%;">DESCRIPTION</th>
            </tr>
        </thead>
        <tbody>
            @if(count($transactions) > 0)
            @php
            $no = 1;
            @endphp

            @foreach($transactions as $transaction)
            @php
            // Sort parts by ID
            $sortedParts = $transaction->parts->sortBy('id');
            @endphp
            @foreach($sortedParts as $part)
            <tr>
                <td>{{ $no++ }}</td>
                <td class="text-left">{{ $part->partInventory->part->part_name }}</td>
                <td>{{ explode('-', $part->partInventory->part->part_code)[0] ?? '-' }}</td>
                <td>{{ $part->partInventory->part->part_code }}</td>
                <td>{{ $part->quantity }} {{ $part->partInventory->oum ?? $part->eum ?? 'EA' }}</td>
                <td>{{ $transaction->remarks ?? '-' }}</td>
            </tr>
            @endforeach
            @endforeach

            <!-- Add empty rows to make the table look complete -->
            @for($i = $no; $i <= 15; $i++)
                <tr>
                <td>{{ $i }}</td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                </tr>
                @endfor
                @else
                @for($i = 1; $i <= 15; $i++)
                    <tr>
                    <td>{{ $i }}</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    </tr>
                    @endfor
                    @endif
                <tr>
                    <td colspan="6" style="text-align: left; padding-left: 5px;"><strong>NOTE:</strong></td>
                </tr><tr>
                    <td colspan="6" style="text-align: left; padding: 7px;"></td>
                </tr><tr>
                    <td colspan="6" style="text-align: left; padding: 7px;"></td>
                </tr><tr>
                    <td colspan="6" style="text-align: left; padding: 7px;"></td>
                </tr>
        </tbody>
    </table>

    <!-- Signature Section -->
    <div class="clearfix">
        <table class="signature-table" style="border: 1px solid #000; border-top: 0;">
            <tr>
                <td style="width: 50%; border-right: 1px solid #000;">
                    <div>DIORDER OLEH</div>
                    <div style="padding-top: 50px;">Nama: ........................</div>
                </td>
                <td style="width: 50%; border-right: 1px solid #000;">
                    <div>DISETUJUI OLEH</div>
                    <div style="padding-top: 50px;">Nama: ........................</div>
                </td>
                <td style="width: 50%; border-right: 1px solid #000;">
                    <div>DIKELUARKAN OLEH</div>
                    <div style="padding-top: 50px;">Nama: ........................</div>
                </td>
                <td style="width: 50%; ">
                    <div>DITERIMA OLEH</div>
                    <div style="padding-top: 50px;">Nama: ........................</div>
                </td>
            </tr>
        </table>
    </div>
</body>

</html>