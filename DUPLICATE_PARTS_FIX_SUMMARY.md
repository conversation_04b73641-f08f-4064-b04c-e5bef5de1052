# Fix for Duplicate Part Codes in Invoice Creation Form

## Problem Description

The invoice creation form had a logic error when handling multiple parts with the same part code but different details (names, descriptions, prices). The system incorrectly handled duplicates by:

1. **Frontend Issue**: When updating temporary part IDs with real database IDs after saving, only the first part with a matching `part_code` would get updated, leaving subsequent parts with the same code with temporary IDs.

2. **Backend Issue**: The update logic tried to find existing parts by `part_code` only, which meant it would update the first part with that code instead of creating separate entries for parts with the same code but different specifications.

## Expected Behavior

- Allow multiple parts with the same part code to be added to the invoice
- Each part should maintain its own unique name, description, and price even if the part code is identical  
- Both parts should remain in the parts table without being merged or having their details overwritten

## Root Cause Analysis

### Frontend Issue (resources/views/sales/invoices/create.blade.php, lines 1263-1264)
```javascript
// OLD PROBLEMATIC CODE:
const tempPartIndex = selectedParts.findIndex(p =>
    p.isNew && p.part_code === savedPart.part_code);
```

This used `findIndex` which returns only the **first** matching element, so when there were multiple parts with the same `part_code`, only the first one would get its temporary ID updated with the real database ID.

### Backend Issue (app/Http/Controllers/ManualInvoiceController.php, lines 400-402)
```php
// OLD PROBLEMATIC CODE:
$existingPart = ManualInvoicePart::where('invoice_id', $invoice->id)
    ->where('part_code', $partCode)
    ->first();
```

This would find the first existing part with the same `part_code` and update it, instead of creating a new separate entry for parts with the same code but different specifications.

## Solution Implemented

### 1. Frontend Fix (JavaScript)

**File**: `resources/views/sales/invoices/create.blade.php`

**Change**: Replaced the simple `findIndex` logic with a more sophisticated matching algorithm that considers multiple criteria and tracks which saved parts have been used.

```javascript
// NEW FIXED CODE:
const savedPartsUsed = new Set();

selectedParts.forEach((selectedPart, selectedIndex) => {
    if (selectedPart.isNew) {
        // Find a matching saved part that hasn't been used yet
        const matchingSavedPart = data.invoice.manualInvoiceParts.find((savedPart, savedIndex) => {
            return !savedPartsUsed.has(savedIndex) &&
                   savedPart.part_code === selectedPart.part_code &&
                   savedPart.part_name === selectedPart.part_name &&
                   parseFloat(savedPart.price) === parseFloat(selectedPart.price) &&
                   parseInt(savedPart.quantity) === parseInt(selectedPart.quantity);
        });
        
        if (matchingSavedPart) {
            // Mark this saved part as used
            const savedPartIndex = data.invoice.manualInvoiceParts.indexOf(matchingSavedPart);
            savedPartsUsed.add(savedPartIndex);
            
            // Update the selected part with the real ID
            selectedParts[selectedIndex].id = matchingSavedPart.id;
            selectedParts[selectedIndex].isNew = false;
        }
    }
});
```

**Key Improvements**:
- Uses a `Set` to track which saved parts have been matched to avoid double-matching
- Matches parts based on multiple criteria (part_code, part_name, price, quantity) instead of just part_code
- Ensures each saved part is only matched to one selected part

### 2. Backend Fix (PHP)

**File**: `app/Http/Controllers/ManualInvoiceController.php`

**Change**: Simplified the update logic to always create fresh entries instead of trying to update existing ones.

```php
// NEW FIXED CODE:
// Delete all existing parts for this invoice to avoid conflicts with duplicates
ManualInvoicePart::where('invoice_id', $invoice->id)->delete();

// Create all parts fresh from the submitted data
foreach ($request->parts as $partData) {
    if (!empty($partData['part_code']) && !empty($partData['part_name'])) {
        // Always create new part - this allows multiple parts with same part_code
        ManualInvoicePart::create([
            'invoice_id' => $invoice->id,
            'part_code' => $partCode,
            'part_name' => $partName,
            'quantity' => $quantity,
            'price' => $price,
            'eum' => $eum,
            'total' => $quantity * $price,
        ]);
    }
}
```

**Key Improvements**:
- Removes the complex logic that tried to match and update existing parts
- Always creates fresh entries from the submitted data
- Allows multiple parts with the same `part_code` but different specifications

## Test Scenario

The fix now properly handles this scenario:

1. **Part 1**: Code "TIRE001", Name "Tire Service - Front Left", Price 150,000
2. **Part 2**: Code "TIRE001", Name "Tire Service - Front Right", Price 175,000

**Before Fix**: Only one part would be saved correctly, the other would be lost or have incorrect data.

**After Fix**: Both parts are saved with their unique names and prices, even though they share the same part code.

## Files Modified

1. `resources/views/sales/invoices/create.blade.php` - Fixed frontend ID matching logic
2. `app/Http/Controllers/ManualInvoiceController.php` - Fixed backend duplicate handling logic

## Testing Recommendations

1. Create an invoice with two parts that have the same part code but different names and prices
2. Save the invoice and verify both parts are saved correctly
3. Edit the invoice and verify both parts maintain their individual properties
4. Verify that the parts table displays both entries correctly without merging or overwriting
