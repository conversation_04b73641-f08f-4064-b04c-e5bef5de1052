@extends('sites.content')
@section('title', 'Midlife')

@section('resourcesite')
@vite(['resources/js/units/midlife.js'])
<style>
    table thead tr th:hover {
        background-color: rgb(232, 243, 231);
        border-radius: 4px;
        cursor: pointer;
    }

    .card-table {
        max-width: 100%;
        flex: 0 0 auto;
        border-radius: 8px;
        overflow: hidden;
        background-color: white;
        border: 1px solid #ccc;
    }
    
    /* Add loading spinner */
    #loading-spinner {
        display: none;
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 9999;
    }
</style>
@endsection
@section('contentsite')
<div id="loading-spinner" class="spinner-border text-primary" role="status">
    <span class="visually-hidden">.</span>
</div>

<div class="container-fluid">
    <!-- Real-time Filters -->
    <div class="row mb-3 mt-3">
        <div class="col-md-3 d-none">
            <input type="text" id="partFilter" class="form-control" placeholder="Cari Part" autocomplete="off">
        </div>
        <div class="col-md-3">
            <input type="text" id="unitFilter" class="form-control" placeholder="Cari Unit" autocomplete="off">
        </div>
    </div>

    <!-- container data-->
    <div class="overflow-x-auto pb-3" style="white-space: nowrap;">
        <div class="d-flex flex-nowrap gap-3" id="sectioncards" style="min-width: max-content;">
            <!-- Data will appear here via JS -->
        </div>
    </div>
</div>
@endsection