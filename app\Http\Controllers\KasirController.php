<?php

namespace App\Http\Controllers;

use App\Models\CashierTransaction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class KasirController extends Controller
{


    /**
     * Display the cashier dashboard
     */
    public function dashboard(Request $request)
    {
        $user = Auth::user();

        // Check if user is kasir
        if ($user->role !== 'kasir') {
            return redirect('/login')->withErrors(['username' => 'Aks<PERSON>lak! Anda tidak memiliki izin untuk mengakses halaman ini.']);
        }

        // Get date range from request or default to current month
        $startDate = $request->get('start_date', Carbon::now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->get('end_date', Carbon::now()->format('Y-m-d'));

        // Get recent transactions (last 10)
        $recentTransactions = CashierTransaction::where('user_id', $user->employee_id)
            ->orderBy('transaction_date', 'desc')
            ->limit(10)
            ->get();

        // Get summary data for the selected period
        $totalPengeluaran = CashierTransaction::where('user_id', $user->employee_id)
            ->where('type', 'pengeluaran')
            ->dateRange($startDate, $endDate)
            ->sum('amount');

        $totalPenerimaan = CashierTransaction::where('user_id', $user->employee_id)
            ->where('type', 'penerimaan')
            ->dateRange($startDate, $endDate)
            ->sum('amount');

        return view('kasir.dashboard', compact(
            'recentTransactions',
            'totalPengeluaran',
            'totalPenerimaan',
            'startDate',
            'endDate'
        ));
    }

    /**
     * Store a new transaction
     */
    public function store(Request $request)
    {
        // Check if user is kasir
        if (Auth::user()->role !== 'kasir') {
            return response()->json(['success' => false, 'message' => 'Akses ditolak'], 403);
        }
        $request->validate([
            'type' => 'required|in:pengeluaran,penerimaan',
            'description' => 'required|string|max:1000',
            'amount' => 'required|numeric|min:0',
            'transaction_date' => 'required|date',
            'attachment' => 'nullable|file|mimes:pdf,jpg,jpeg,png,doc,docx|max:5120' // 5MB
        ], [
            'type.required' => 'Jenis transaksi harus dipilih',
            'type.in' => 'Jenis transaksi tidak valid',
            'description.required' => 'Deskripsi transaksi harus diisi',
            'description.max' => 'Deskripsi maksimal 1000 karakter',
            'amount.required' => 'Jumlah harus diisi',
            'amount.numeric' => 'Jumlah harus berupa angka',
            'amount.min' => 'Jumlah tidak boleh negatif',
            'transaction_date.required' => 'Tanggal transaksi harus diisi',
            'transaction_date.date' => 'Format tanggal tidak valid',
            'attachment.file' => 'Lampiran harus berupa file',
            'attachment.mimes' => 'Format file tidak didukung. Gunakan PDF, JPG, JPEG, PNG, DOC, atau DOCX',
            'attachment.max' => 'Ukuran file maksimal 5MB'
        ]);

        $attachmentPath = null;
        if ($request->hasFile('attachment')) {
            $file = $request->file('attachment');
            $filename = time() . '_' . $file->getClientOriginalName();
            $attachmentPath = $file->storeAs('cashier_attachments', $filename, 'public');
        }

        $transaction = CashierTransaction::create([
            'user_id' => Auth::user()->employee_id,
            'type' => $request->type,
            'description' => $request->description,
            'amount' => $request->amount,
            'transaction_date' => $request->transaction_date,
            'attachment' => $attachmentPath
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Transaksi berhasil disimpan',
            'transaction' => $transaction->load('user')
        ]);
    }

    /**
     * Get transactions with pagination and filtering
     */
    public function getTransactions(Request $request)
    {
        $user = Auth::user();

        // Check if user is kasir
        if ($user->role !== 'kasir') {
            return response()->json(['success' => false, 'message' => 'Akses ditolak'], 403);
        }
        $perPage = 10;
        $page = $request->get('page', 1);

        $query = CashierTransaction::where('user_id', $user->employee_id);

        // Apply filters
        if ($request->has('type') && $request->type !== '') {
            $query->where('type', $request->type);
        }

        if ($request->has('start_date') && $request->has('end_date')) {
            $query->dateRange($request->start_date, $request->end_date);
        }

        if ($request->has('search') && $request->search !== '') {
            $query->where('description', 'like', '%' . $request->search . '%');
        }

        $transactions = $query->orderBy('transaction_date', 'desc')
            ->paginate($perPage, ['*'], 'page', $page);

        return response()->json([
            'success' => true,
            'data' => $transactions->items(),
            'pagination' => [
                'current_page' => $transactions->currentPage(),
                'last_page' => $transactions->lastPage(),
                'per_page' => $transactions->perPage(),
                'total' => $transactions->total(),
                'from' => $transactions->firstItem(),
                'to' => $transactions->lastItem()
            ]
        ]);
    }

    /**
     * Get summary data
     */
    public function getSummary(Request $request)
    {
        $user = Auth::user();

        // Check if user is kasir
        if ($user->role !== 'kasir') {
            return response()->json(['success' => false, 'message' => 'Akses ditolak'], 403);
        }
        $startDate = $request->get('start_date', Carbon::now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->get('end_date', Carbon::now()->format('Y-m-d'));

        $totalPengeluaran = CashierTransaction::where('user_id', $user->employee_id)
            ->where('type', 'pengeluaran')
            ->dateRange($startDate, $endDate)
            ->sum('amount');

        $totalPenerimaan = CashierTransaction::where('user_id', $user->employee_id)
            ->where('type', 'penerimaan')
            ->dateRange($startDate, $endDate)
            ->sum('amount');

        return response()->json([
            'success' => true,
            'data' => [
                'total_pengeluaran' => $totalPengeluaran,
                'total_penerimaan' => $totalPenerimaan,
                'formatted_pengeluaran' => 'Rp ' . number_format($totalPengeluaran, 0, ',', '.'),
                'formatted_penerimaan' => 'Rp ' . number_format($totalPenerimaan, 0, ',', '.')
            ]
        ]);
    }
}
