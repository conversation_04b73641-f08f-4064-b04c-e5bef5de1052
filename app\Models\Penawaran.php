<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Penawaran extends Model
{
    use HasFactory;

    protected $table = 'penawarans';

    protected $fillable = [
        'nomor',
        'tanggal_penawaran',
        'perihal',
        'customer',
        'customer_code',
        'lokasi',
        'notes',
        'showcode',
        'diskon',
        'attn',
        'status',
    ];

    protected $casts = [
        'tanggal_penawaran' => 'date',
    ];

    public function items()
    {
        return $this->hasMany(PenawaranItem::class);
    }

    /**
     * Get the invoice associated with this penawaran
     */
    public function invoice()
    {
        return $this->hasOne(Invoice::class);
    }
}
