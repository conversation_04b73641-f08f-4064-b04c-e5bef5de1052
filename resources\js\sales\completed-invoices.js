document.addEventListener('DOMContentLoaded', function() {
    // Set default date range (current month)
    const today = new Date();
    const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);

    document.getElementById('date-from').valueAsDate = firstDay;
    document.getElementById('date-to').valueAsDate = today;

    // Variables for pagination
    let currentPage = 1;
    let totalPages = 1;
    let itemsPerPage = 10;
    let sortField = 'tanggal_invoice';
    let sortDirection = 'desc';
    let searchTerm = '';

    // Store all loaded invoices for client-side filtering
    let allLoadedInvoices = [];
    let filteredInvoices = [];

    // Load completed invoices on page load
    loadCompletedInvoices();

    // Filter button click event - resets search and reloads from server
    document.getElementById('filter-button').addEventListener('click', function() {
        // Reset search term and search input
        searchTerm = '';
        document.getElementById('search-input').value = '';

        // Reset page to 1
        currentPage = 1;

        // Reload data from server with new date range
        loadCompletedInvoices();
    });

    // Search input event - client-side filtering
    document.getElementById('search-input').addEventListener('input', function() {
        searchTerm = this.value.toLowerCase().trim();
        currentPage = 1;
        filterAndDisplayInvoices();
    });

    // Sortable headers click event
    document.querySelectorAll('.sortable').forEach(header => {
        header.addEventListener('click', function() {
            const field = this.getAttribute('data-sort');

            // Toggle sort direction if clicking on the same field
            if (field === sortField) {
                sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
            } else {
                sortField = field;
                sortDirection = 'asc';
            }

            // Update UI to show sort direction
            document.querySelectorAll('.sortable').forEach(h => {
                h.classList.remove('asc', 'desc');
            });
            this.classList.add(sortDirection);

            // If we have loaded data, sort and display it client-side
            if (allLoadedInvoices.length > 0) {
                sortInvoicesClientSide();
                filterAndDisplayInvoices();
            } else {
                // Otherwise reload data from server
                loadCompletedInvoices();
            }
        });
    });

    // No print invoice button anymore

    // Document viewer event delegation (for dynamically added buttons)
    document.addEventListener('click', function(e) {
        if (e.target && e.target.closest('.btn-view-document')) {
            const button = e.target.closest('.btn-view-document');
            const documentPath = button.getAttribute('data-path');
            if (documentPath) {
                showDocumentInModal(documentPath);
            }
        }
    });

    // Function to load completed invoices
    function loadCompletedInvoices() {
        const dateFrom = document.getElementById('date-from').value;
        const dateTo = document.getElementById('date-to').value;

        // Show loading state
        const tableBody = document.getElementById('completed-invoices-table-body');
        tableBody.innerHTML = `
            <tr>
                <td colspan="9" class="text-center">
                    <div class="d-flex justify-content-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="sr-only">.</span>
                        </div>
                    </div>
                </td>
            </tr>
        `;

        // Build query parameters
        const params = new URLSearchParams({
            page: currentPage,
            per_page: itemsPerPage,
            sort_field: sortField,
            sort_direction: sortDirection,
            date_from: dateFrom,
            date_to: dateTo,
            search: searchTerm,
            completed: 'true' 
        });

        // Fetch data from API
        fetch(`/sales/invoiced-transactions?${params.toString()}`)
            .then(response => response.json())
            .then(data => {
                // Store all loaded invoices for client-side filtering
                allLoadedInvoices = data.data;
                filteredInvoices = [...allLoadedInvoices];

                displayInvoices(data.data);
                updatePagination(data);
                updateShowingText(data);
            })
            .catch(error => {
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="9" class="text-center">
                            Error loading data. Please try again.
                        </td>
                    </tr>
                `;
            });
    }

    // Function to display invoices in the table
    function displayInvoices(invoices) {
        const tableBody = document.getElementById('completed-invoices-table-body');
        tableBody.innerHTML = '';

        if (invoices.length === 0) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="9" class="text-center">
                        <div class="text-center p-3">
                            <img src="/assets/images/no-data.svg" alt="No Data" style="max-width: 150px;">
                            <p class="mt-2">Tidak ada invoice yang selesai dalam periode ini</p>
                        </div>
                    </td>
                </tr>
            `;
            return;
        }

        invoices.forEach((invoice, index) => {
            // Format date
            const invoiceDate = invoice.tanggal_invoice ? new Date(invoice.tanggal_invoice) : null;
            const formattedDate = invoiceDate ?
                invoiceDate.toLocaleDateString('id-ID', { day: 'numeric', month: 'long', year: 'numeric' }) :
                '-';

            // Format payment date
            const paymentDate = invoice.payment_date ? new Date(invoice.payment_date) : null;
            const formattedPaymentDate = paymentDate ?
                paymentDate.toLocaleDateString('id-ID', { day: 'numeric', month: 'long', year: 'numeric' }) :
                '-';

            // Format total amount
            const formattedTotal = new Intl.NumberFormat('id-ID', {
                style: 'currency',
                currency: 'IDR',
                minimumFractionDigits: 0
            }).format(invoice.total_amount || 0);

            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${(currentPage - 1) * itemsPerPage + index + 1}</td>
                <td>
                    <a href="#" class="invoice-detail-link" data-id="${invoice.id}">${invoice.no_invoice || '-'}</a>
                </td>
                <td>${invoice.unit_list || '-'}</td>
                <td>${formattedDate}</td>
                <td>${invoice.customer || '-'}</td>
                <td>${formattedTotal}</td>
                <td>${formattedPaymentDate}</td>
                <td>
                    ${invoice.document_path ?
                        `<button class="btn btn-sm btn-primary btn-view-document" data-path="${invoice.document_path}">
                            <i class="mdi mdi-file-document"></i> Lihat Lampiran
                        </button>` :
                        '<span class="badge bg-secondary">Tidak ada lampiran</span>'
                    }
                </td>
            `;

            tableBody.appendChild(row);
        });

        // Add event listeners to invoice detail links
        document.querySelectorAll('.invoice-detail-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const invoiceId = this.getAttribute('data-id');
                showInvoiceDetails(invoiceId);
            });
        });

        // No print invoice buttons anymore
    }

    // Function to show invoice details in modal
    function showInvoiceDetails(invoiceId) {
        const modal = new bootstrap.Modal(document.getElementById('invoice-detail-modal'));
        const modalContent = document.getElementById('invoice-detail-content');
        const printButton = document.getElementById('print-invoice-btn');

        // Show loading state
        modalContent.innerHTML = `
            <div class="d-flex justify-content-center p-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">.</span>
                </div>
            </div>
        `;

        // Set invoice ID to print button
        printButton.setAttribute('data-id', invoiceId);

        // Show modal
        modal.show();

        // Fetch invoice details
        fetch(`/sales/invoices/${invoiceId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayInvoiceDetails(data.invoice);
                } else {
                    modalContent.innerHTML = `
                        <div class="alert alert-danger">
                            Error loading invoice details: ${data.message}
                        </div>
                    `;
                }
            })
            .catch(error => {
                modalContent.innerHTML = `
                    <div class="alert alert-danger">
                        Error loading invoice details. Please try again.
                    </div>
                `;
            });
    }

    // Function to display invoice details in modal
    function displayInvoiceDetails(invoice) {
        const modalContent = document.getElementById('invoice-detail-content');

        // Format dates
        const invoiceDate = invoice.tanggal_invoice ? new Date(invoice.tanggal_invoice) : null;
        const formattedDate = invoiceDate ?
            invoiceDate.toLocaleDateString('id-ID', { day: 'numeric', month: 'long', year: 'numeric' }) :
            '-';

        const paymentDate = invoice.payment_date ? new Date(invoice.payment_date) : null;
        const formattedPaymentDate = paymentDate ?
            paymentDate.toLocaleDateString('id-ID', { day: 'numeric', month: 'long', year: 'numeric' }) :
            '-';

        // Format amounts
        const formattedSubtotal = new Intl.NumberFormat('id-ID', {
            style: 'currency',
            currency: 'IDR',
            minimumFractionDigits: 0
        }).format(invoice.subtotal || 0);

        const formattedTax = new Intl.NumberFormat('id-ID', {
            style: 'currency',
            currency: 'IDR',
            minimumFractionDigits: 0
        }).format(invoice.tax_amount || 0);

        const formattedTotal = new Intl.NumberFormat('id-ID', {
            style: 'currency',
            currency: 'IDR',
            minimumFractionDigits: 0
        }).format(invoice.total_amount || 0);

        // Determine if this is a penawaran invoice
        const isPenawaranInvoice = invoice.penawaran_id && invoice.penawaran;

        // Build HTML for invoice details
        modalContent.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <p><strong>Nomor Invoice:</strong> ${invoice.no_invoice || '-'}</p>
                    <p><strong>Tanggal Invoice:</strong> ${formattedDate}</p>
                    <p><strong>Customer:</strong> ${invoice.customer || '-'}</p>
                    <p><strong>Lokasi:</strong> ${invoice.location || invoice.lokasi || '-'}</p>
                    ${invoice.po_number ? `<p><strong>Nomor PO:</strong> ${invoice.po_number}</p>` : ''}
                </div>
                <div class="col-md-6">
                    <p><strong>Status Pembayaran:</strong> <span class="badge bg-success text-white">Lunas</span></p>
                    <p><strong>Tanggal Pembayaran:</strong> ${formattedPaymentDate}</p>
                    <p><strong>Catatan Pembayaran:</strong> ${invoice.payment_notes || '-'}</p>
                    ${isPenawaranInvoice ? `<p><strong>Jenis Invoice:</strong> <span class="badge bg-info">Penawaran</span></p>` : ''}
                </div>
            </div>

            <hr>

            ${isPenawaranInvoice ?
                `<h6>Detail Penawaran</h6>
                <div class="table-responsive">
                    <table class="table table-sm table-bordered">
                        <thead class="bg-light">
                            <tr>
                                <th>Nomor Penawaran</th>
                                <th>Perihal</th>
                                <th>Tanggal</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>${invoice.penawaran.nomor || '-'}</td>
                                <td>${invoice.penawaran.perihal || '-'}</td>
                                <td>${invoice.penawaran.tanggal_penawaran ? new Date(invoice.penawaran.tanggal_penawaran).toLocaleDateString('id-ID') : '-'}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>`
                :
                `<h6>Detail Transaksi</h6>
                <div class="table-responsive">
                    <table class="table table-sm table-bordered">
                        <thead class="bg-light">
                            <tr>
                                <th>Unit</th>
                                <th>Deskripsi</th>
                                <th>Tanggal</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${invoice.unitTransactions && invoice.unitTransactions.length > 0 ?
                                invoice.unitTransactions.map(transaction => `
                                    <tr>
                                        <td>${transaction.unit ? transaction.unit.unit_code : '-'}</td>
                                        <td>${transaction.description || '-'}</td>
                                        <td>${transaction.created_at ? new Date(transaction.created_at).toLocaleDateString('id-ID') : '-'}</td>
                                    </tr>
                                `).join('') :
                                '<tr><td colspan="3" class="text-center">Tidak ada data transaksi</td></tr>'
                            }
                        </tbody>
                    </table>
                </div>`
            }

            <div class="row mt-3">
                <div class="col-md-6">
                    ${invoice.document_path ?
                        `<p><strong>Dokumen Invoice:</strong> <button class="btn btn-sm btn-primary btn-view-document" data-path="${invoice.document_path}"><i class="mdi mdi-file-document"></i> Lihat Lampiran</button></p>` :
                        '<p><strong>Dokumen Invoice:</strong> <span class="badge bg-secondary">Tidak ada lampiran</span></p>'
                    }
                </div>
                <div class="col-md-6 text-right">
                    <p><strong>Subtotal:</strong> ${formattedSubtotal}</p>
                    <p><strong>PPN (${(invoice.ppn * 100).toFixed(0)}%):</strong> ${formattedTax}</p>
                    <p><strong>Total:</strong> ${formattedTotal}</p>
                </div>
            </div>
        `;
    }

    // Function to update pagination for server-side data
    function updatePagination(data) {
        const pagination = document.getElementById('pagination');
        pagination.innerHTML = '';

        totalPages = data.last_page;
        currentPage = data.current_page;

        // Previous button
        const prevLi = document.createElement('li');
        prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
        prevLi.innerHTML = `<a class="page-link" href="#" data-page="${currentPage - 1}">&laquo;</a>`;
        pagination.appendChild(prevLi);

        // Page numbers
        let startPage = Math.max(1, currentPage - 2);
        let endPage = Math.min(totalPages, startPage + 4);

        if (endPage - startPage < 4 && totalPages > 5) {
            startPage = Math.max(1, endPage - 4);
        }

        for (let i = startPage; i <= endPage; i++) {
            const pageLi = document.createElement('li');
            pageLi.className = `page-item ${i === currentPage ? 'active' : ''}`;
            pageLi.innerHTML = `<a class="page-link" href="#" data-page="${i}">${i}</a>`;
            pagination.appendChild(pageLi);
        }

        // Next button
        const nextLi = document.createElement('li');
        nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
        nextLi.innerHTML = `<a class="page-link" href="#" data-page="${currentPage + 1}">&raquo;</a>`;
        pagination.appendChild(nextLi);

        // Add event listeners to pagination links
        document.querySelectorAll('.page-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const page = parseInt(this.getAttribute('data-page'));
                if (page >= 1 && page <= totalPages) {
                    currentPage = page;

                    // If we have a search term, use client-side pagination
                    if (searchTerm) {
                        filterAndDisplayInvoices();
                    } else {
                        // Otherwise, load from server
                        loadCompletedInvoices();
                    }
                }
            });
        });
    }

    // Function to update showing text
    function updateShowingText(data) {
        const showingText = document.getElementById('showing-text');
        const from = data.total > 0 ? (data.current_page - 1) * data.per_page + 1 : 0;
        const to = Math.min(data.total, data.current_page * data.per_page);
        showingText.textContent = `Menampilkan ${from} - ${to} dari ${data.total} invoice`;
    }

    // Function to filter invoices client-side
    function filterAndDisplayInvoices() {
        if (searchTerm === '') {
            // If search term is empty, use all loaded invoices
            filteredInvoices = [...allLoadedInvoices];
        } else {
            // Filter invoices based on search term
            filteredInvoices = allLoadedInvoices.filter(invoice => {
                // Search in multiple fields
                return (
                    (invoice.no_invoice && invoice.no_invoice.toLowerCase().includes(searchTerm)) ||
                    (invoice.unit_list && invoice.unit_list.toLowerCase().includes(searchTerm)) ||
                    (invoice.customer && invoice.customer.toLowerCase().includes(searchTerm)) ||
                    (invoice.location && invoice.location.toLowerCase().includes(searchTerm)) ||
                    (invoice.lokasi && invoice.lokasi.toLowerCase().includes(searchTerm))
                );
            });
        }

        // Apply client-side pagination
        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        const paginatedInvoices = filteredInvoices.slice(startIndex, endIndex);

        // Display the filtered and paginated invoices
        displayInvoices(paginatedInvoices);

        // Update pagination and showing text
        updateClientPagination();
        updateClientShowingText();
    }

    // Function to sort invoices client-side
    function sortInvoicesClientSide() {
        filteredInvoices.sort((a, b) => {
            let valueA, valueB;

            // Handle different field types
            switch (sortField) {
                case 'no_invoice':
                    valueA = a.no_invoice || '';
                    valueB = b.no_invoice || '';
                    break;
                case 'unit_list':
                    valueA = a.unit_list || '';
                    valueB = b.unit_list || '';
                    break;
                case 'tanggal_invoice':
                    valueA = a.tanggal_invoice ? new Date(a.tanggal_invoice) : new Date(0);
                    valueB = b.tanggal_invoice ? new Date(b.tanggal_invoice) : new Date(0);
                    break;
                case 'customer':
                    valueA = a.customer || '';
                    valueB = b.customer || '';
                    break;
                case 'total_amount':
                    valueA = a.total_amount || 0;
                    valueB = b.total_amount || 0;
                    break;
                case 'payment_date':
                    valueA = a.payment_date ? new Date(a.payment_date) : new Date(0);
                    valueB = b.payment_date ? new Date(b.payment_date) : new Date(0);
                    break;
                default:
                    valueA = a[sortField] || '';
                    valueB = b[sortField] || '';
            }

            // Compare values based on sort direction
            if (sortDirection === 'asc') {
                if (valueA < valueB) return -1;
                if (valueA > valueB) return 1;
                return 0;
            } else {
                if (valueA > valueB) return -1;
                if (valueA < valueB) return 1;
                return 0;
            }
        });
    }

    // Function to update pagination for client-side filtering
    function updateClientPagination() {
        const pagination = document.getElementById('pagination');
        pagination.innerHTML = '';

        // Calculate total pages based on filtered data
        totalPages = Math.ceil(filteredInvoices.length / itemsPerPage);

        // Ensure current page is valid
        if (currentPage > totalPages) {
            currentPage = Math.max(1, totalPages);
        }

        // Previous button
        const prevLi = document.createElement('li');
        prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
        prevLi.innerHTML = `<a class="page-link" href="#" data-page="${currentPage - 1}">&laquo;</a>`;
        pagination.appendChild(prevLi);

        // Page numbers
        let startPage = Math.max(1, currentPage - 2);
        let endPage = Math.min(totalPages, startPage + 4);

        if (endPage - startPage < 4 && totalPages > 5) {
            startPage = Math.max(1, endPage - 4);
        }

        for (let i = startPage; i <= endPage; i++) {
            const pageLi = document.createElement('li');
            pageLi.className = `page-item ${i === currentPage ? 'active' : ''}`;
            pageLi.innerHTML = `<a class="page-link" href="#" data-page="${i}">${i}</a>`;
            pagination.appendChild(pageLi);
        }

        // Next button
        const nextLi = document.createElement('li');
        nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
        nextLi.innerHTML = `<a class="page-link" href="#" data-page="${currentPage + 1}">&raquo;</a>`;
        pagination.appendChild(nextLi);

        // Add event listeners to pagination links
        document.querySelectorAll('.page-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const page = parseInt(this.getAttribute('data-page'));
                if (page >= 1 && page <= totalPages) {
                    currentPage = page;
                    filterAndDisplayInvoices();
                }
            });
        });
    }

    // Function to update showing text for client-side filtering
    function updateClientShowingText() {
        const showingText = document.getElementById('showing-text');
        const total = filteredInvoices.length;
        const from = total > 0 ? (currentPage - 1) * itemsPerPage + 1 : 0;
        const to = Math.min(total, currentPage * itemsPerPage);
        showingText.textContent = `Menampilkan ${from} - ${to} dari ${total} invoice`;
    }

    // Function to show document in modal
    function showDocumentInModal(documentPath) {
        const modal = new bootstrap.Modal(document.getElementById('document-viewer-modal'));
        const documentViewer = document.getElementById('document-viewer');
        const downloadLink = document.getElementById('download-document');
        const modalTitle = document.getElementById('document-viewer-modal-label');

        // Update modal title with filename
        const fileName = documentPath.split('/').pop();
        modalTitle.textContent = `Dokumen Invoice: ${fileName}`;

        // Set the document path for download button
        downloadLink.href = `/assets/invoice_documents/${documentPath}`;

        // Show loading state
        documentViewer.innerHTML = `
            <div class="d-flex justify-content-center align-items-center h-100">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">.</span>
                </div>
            </div>
        `;

        // Show modal
        modal.show();

        // Check file extension to determine how to display it
        const fileExtension = documentPath.split('.').pop().toLowerCase();

        if (fileExtension === 'pdf') {
            // Embed PDF viewer
            documentViewer.innerHTML = `
                <embed src="/assets/invoice_documents/${documentPath}" type="application/pdf" width="100%" height="100%">
            `;
        } else if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExtension)) {
            // Display image
            documentViewer.innerHTML = `
                <div class="d-flex justify-content-center align-items-center h-100">
                    <img src="/assets/invoice_documents/${documentPath}" class="img-fluid" style="max-height: 100%;">
                </div>
            `;
        } else {
            // For other file types, show a message and download link
            documentViewer.innerHTML = `
                <div class="d-flex flex-column justify-content-center align-items-center h-100">
                    <div class="alert alert-info">
                        <i class="mdi mdi-file-document-outline" style="font-size: 48px;"></i>
                        <p class="mt-3">File tidak dapat ditampilkan secara langsung. Silakan download file untuk melihatnya.</p>
                    </div>
                </div>
            `;
        }
    }
});
