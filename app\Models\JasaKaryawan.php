<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class JasaKaryawan extends Model
{
    use HasFactory;

    protected $table = 'jasa_karyawan';
    protected $primaryKey = 'jasa_karyawan_id';

    protected $fillable = [
        'site_id',
        'employee_name',
        'amount',
        'date',
        'status',
        'description',
        'created_at',
        'updated_at'
    ];

    protected $casts = [
        'date' => 'date',
        'amount' => 'decimal:2',
    ];

    /**
     * Get the site that owns the jasa karyawan record
     */
    public function site()
    {
        return $this->belongsTo(Site::class, 'site_id', 'site_id');
    }
}
