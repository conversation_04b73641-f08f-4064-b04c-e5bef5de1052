<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('manual_invoice_parts', function (Blueprint $table) {
            // Drop the foreign key constraint on part_code
            $table->dropForeign(['part_code']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('manual_invoice_parts', function (Blueprint $table) {
            // Re-add the foreign key constraint
            $table->foreign('part_code')->references('part_code')->on('parts')->onUpdate('cascade');
        });
    }
};
