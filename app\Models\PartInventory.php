<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PartInventory extends Model
{
    use HasFactory;

    protected $table = 'part_inventories';
    protected $primaryKey = 'part_inventory_id';

    protected $fillable = ['part_code', 'site_part_name', 'site_id', 'price', 'priority','oum', 'min_stock', 'max_stock', 'stock_quantity', 'date_priority'];

    protected $casts = [
        'priority' => 'boolean',
    ];

    public function part()
    {
        return $this->belongsTo(Part::class, 'part_code', 'part_code');
    }

    public function site()
    {
        return $this->belongsTo(Site::class, 'site_id', 'site_id');
    }
    public function onHandQuantities()
    {
        return $this->hasMany(OnHandQuantity::class, 'part_inventory_id', 'part_inventory_id');
    }

    public function warehouseInStocks()
    {
        return $this->hasMany(WarehouseInStock::class, 'part_inventory_id', 'part_inventory_id');
    }

    public function warehouseOutStocks()
    {
        return $this->hasMany(WarehouseOutStock::class, 'part_inventory_id', 'part_inventory_id');
    }

    public function siteInStocks()
    {
        return $this->hasMany(SiteInStock::class, 'part_inventory_id', 'part_inventory_id');
    }

    public function siteOutStocks()
    {
        return $this->hasMany(SiteOutStock::class, 'part_inventory_id', 'part_inventory_id');
    }

    public function returnStocks()
    {
        return $this->hasMany(ReturnStock::class, 'part_inventory_id', 'part_inventory_id');
    }

    public function partTrackings()
    {
        return $this->hasMany(PartTracking::class, 'part_inventory_id', 'part_inventory_id');
    }

    public function scopePriorityOneForSite($query, $siteId = null)
    {
        return $query->where('priority', 1)
            ->when($siteId, fn($q) => $q->where('site_id', $siteId));
    }
}
