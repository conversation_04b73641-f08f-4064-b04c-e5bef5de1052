<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <title>@yield('title', 'DASHBOARD Site')</title>
    <link rel="shortcut icon" href="{{ asset('assets/images/logo-small.png') }}">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <!-- CSS Files -->
    @vite('resources/assets/css/bootstrap.min.css')
    @vite('resources/assets/css/icons.min.css')
    @vite('resources/assets/css/app.min.css')
    @vite('resources/css/app.css')
    <style>
        :root {
            --primary-color: #225297;
            --secondary-color: #58c0f6;
            --accent-color: #1e4785;
            --accent-hover-color: #3a6db5;
            --highlight-color: #e8f4ff;
            --danger-color: #eb3124;
            --success-color: #97f784;
            --info-color: #58c0f6;
            --warning-color: #feff8c;
            --text-color: #343a40;
            --text-muted: #6c757d;
            --border-color: rgba(0, 0, 0, 0.1);
            --card-bg-color: rgba(255, 255, 255, 0.95);
        }

        /* Global styles for sales pages - standardized font sizes */
        body.sales-body {
            font-size: 14px;
            margin: 0;
            padding: 0;
        }

        .sales-body .btn {
            font-size: 14px;
        }

        .sales-body .table {
            font-size: 14px;
        }

        .sales-body .form-control,
        .sales-body .form-select {
            font-size: 14px;
        }

        .sales-body .modal-body {
            font-size: 14px;
        }

        .sales-body .card-body {
            font-size: 14px;
        }

        .sales-body .dropdown-menu {
            font-size: 14px;
        }

        .sales-body .badge {
            font-size: 14px;
        }

        .sales-body .pagination {
            font-size: 14px;
        }

        .sales-body .sort-icon {
            font-size: 14px;
        }

        /* Sales-specific styles */
        .sales-body .card {
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        .sales-body .card-header {
            background-color: #225297;
            color: white;
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
        }

        .sales-body .btn-primary {
            background-color: #225297;
            border-color: #225297;
        }

        .sales-body .btn-danger {
            background-color: #eb3124;
            border-color: #eb3124;
        }

        .sales-body .btn-success {
            background-color: #97f784;
            border-color: #97f784;
            color: #343a40;
        }

        .sales-body .btn-warning {
            background-color: #feff8c;
            border-color: #feff8c;
            color: #343a40;
        }

        .sales-body .table thead th {
            background-color: #f8f9fa;
            color: #343a40;
        }

        .sales-body .pagination .page-item .page-link {
            color: #225297;
        }

        .sales-body .pagination .page-item.active .page-link {
            background-color: #225297;
            border-color: #225297;
            color: white;
        }
    </style>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jQuery-slimScroll/1.3.8/jquery.slimscroll.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/metisMenu/3.0.7/metisMenu.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
</head>

<body class="sales-body">
    <div id="wrapper">
        <div class="content-page p-0 m-0">
            <div class="content">
                <div class="container-fluid p-0 mr-0">
                    <!-- ======================================================================================================================= -->
                    <!-- ======================================================================================================================= -->
                    @yield('contentsales')
                    <!-- ======================================================================================================================= -->
                    <!-- ======================================================================================================================= -->
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    @vite("resources/assets/js/vendor.min.js")
    @vite("resources/assets/js/app.min.js")
    @vite("resources/js/utils/dateUtils.js")
    @vite("resources/js/sales/sales-common.js")
    @yield('resourcesales')
    @yield('scripts')
</body>

</html>