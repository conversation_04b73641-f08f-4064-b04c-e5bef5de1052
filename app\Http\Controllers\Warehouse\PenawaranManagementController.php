<?php

namespace App\Http\Controllers\Warehouse;

use App\Http\Controllers\Controller;
use App\Models\Penawaran;
use App\Models\PenawaranItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PenawaranManagementController extends Controller
{
    public function index()
    {
        $penawarans = Penawaran::with('items.partInventory.part')
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('warehouse.penawaran.index', compact('penawarans'));
    }

    public function show($id)
    {
        $penawaran = Penawaran::with('items.partInventory.part')->findOrFail($id);
        return view('warehouse.penawaran.show', compact('penawaran'));
    }

    // Warehouse can't update penawaran status, only part status

    public function updateItemStatus(Request $request, $id)
    {
        $request->validate([
            'item_id' => 'required|exists:penawaran_items,id',
            'status' => 'required|in:Ready,In Order,Not Ready',
        ]);

        try {
            // Log the request data for debugging
            Log::info('Updating item status', [
                'penawaran_id' => $id,
                'item_id' => $request->item_id,
                'status' => $request->status
            ]);

            $item = PenawaranItem::findOrFail($request->item_id);
            $item->status = $request->status;
            $item->save();

            return redirect()->back()->with('success', 'Status item berhasil diperbarui');
        } catch (\Exception $e) {
            Log::error('Error updating penawaran item status: ' . $e->getMessage());
            Log::error('Stack trace: ' . $e->getTraceAsString());
            return redirect()->back()->with('error', 'Gagal memperbarui status item: ' . $e->getMessage());
        }
    }

    public function updateAllItemsStatus(Request $request, $id)
    {
        $request->validate([
            'status' => 'required|in:Ready,In Order,Not Ready',
        ]);

        try {
            // Log the request data for debugging
            Log::info('Updating all items status', [
                'penawaran_id' => $id,
                'status' => $request->status
            ]);

            DB::beginTransaction();

            $penawaran = Penawaran::findOrFail($id);
            $itemCount = $penawaran->items()->count();
            $penawaran->items()->update(['status' => $request->status]);

            DB::commit();

            Log::info('Successfully updated all items', [
                'penawaran_id' => $id,
                'item_count' => $itemCount
            ]);

            return redirect()->back()->with('success', 'Status semua item berhasil diperbarui');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating all penawaran items status: ' . $e->getMessage());
            Log::error('Stack trace: ' . $e->getTraceAsString());
            return redirect()->back()->with('error', 'Gagal memperbarui status semua item: ' . $e->getMessage());
        }
    }
}
