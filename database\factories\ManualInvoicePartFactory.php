<?php

namespace Database\Factories;

use App\Models\ManualInvoicePart;
use Illuminate\Database\Eloquent\Factories\Factory;

class ManualInvoicePartFactory extends Factory
{
    protected $model = ManualInvoicePart::class;

    public function definition()
    {
        $quantity = $this->faker->numberBetween(1, 10);
        $price = $this->faker->numberBetween(50000, 500000);
        
        return [
            'invoice_id' => 1, // Will be overridden in tests
            'part_code' => $this->faker->regexify('[A-Z]{3}[0-9]{3}'),
            'part_name' => $this->faker->words(3, true),
            'quantity' => $quantity,
            'price' => $price,
            'total' => $quantity * $price,
            'eum' => 'EA',
        ];
    }
}
