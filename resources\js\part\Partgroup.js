import Swal from "sweetalert2";

// Global variables for pagination
let currentPage = 1;
const itemsPerPage = 20; // 5 items per page for parts table

// Get initial pagination data if available from the server
let paginationData = typeof window.initialPaginationData !== 'undefined' ? window.initialPaginationData : {
    current_page: 1,
    per_page: 20,
    last_page: 1,
    total: 0
};

document.addEventListener("DOMContentLoaded", function () {
    const siteFilter = document.getElementById("siteFilter");
    const typeFilter = document.getElementById("typeFilter");
    const searchInput = document.getElementById("searchInput");
    const partSearch = document.getElementById("partSearch");
    const suggestions = document.getElementById("suggestions");
    const addPartForm = document.getElementById("addPartForm");
    const selectedPartName = document.getElementById("selectedPartName");
    const selectedPartCode = document.getElementById("selectedPartCode");
    const siteCheckboxes = document.querySelectorAll(".site-checkbox");
    const partTableBody = document.getElementById("partTableBody");

    let currentSiteId = siteFilter ? siteFilter.value : '';
    let currentPartType = typeFilter ? typeFilter.value : '';
    let currentSearchTerm = searchInput ? searchInput.value : '';

    // Function to create pagination item
    function createPaginationItem(page, text, isActive = false) {
        const li = document.createElement('li');
        li.className = `page-item ${isActive ? 'active' : ''}`;

        const a = document.createElement('a');
        a.className = 'page-link';
        a.href = '#';
        a.textContent = text;
        a.dataset.page = page;

        li.appendChild(a);
        return li;
    }

    // Function to render pagination
    function renderPagination(data) {
        try {
            const paginationContainer = document.getElementById('parts-pagination');
            if (!paginationContainer) {
                console.error('Pagination container not found');
                return;
            }

            // Update global pagination data
            paginationData = data;

            // Update pagination info
            const paginationInfo = document.getElementById('pagination-info');
            if (paginationInfo) {
                const start = ((data.current_page - 1) * data.per_page) + 1;
                const end = Math.min(start + data.per_page - 1, data.total);
                paginationInfo.textContent = `Menampilkan ${start}-${end} dari ${data.total} item (Halaman ${data.current_page} dari ${data.last_page})`;
            }

            paginationContainer.innerHTML = '';

            // Always show pagination info even if there's only one page
            // But only show pagination controls if there are multiple pages
            if (data.last_page > 1) {
                const pagination = document.createElement('ul');
                pagination.className = 'pagination pagination-rounded ';

                // Previous button
                if (data.current_page > 1) {
                    pagination.appendChild(createPaginationItem(data.current_page - 1, '«'));
                }

                // Calculate which page numbers to show
                let startPage = Math.max(1, data.current_page - 2);
                let endPage = Math.min(data.last_page, startPage + 4);

                // Adjust if we're near the end
                if (endPage - startPage < 4) {
                    startPage = Math.max(1, endPage - 4);
                }

                // First page and ellipsis if needed
                if (startPage > 1) {
                    pagination.appendChild(createPaginationItem(1, 1, 1 === data.current_page));
                    if (startPage > 2) {
                        const ellipsis = document.createElement('li');
                        ellipsis.className = 'page-item disabled';
                        const span = document.createElement('span');
                        span.className = 'page-link';
                        span.textContent = '...';
                        ellipsis.appendChild(span);
                        pagination.appendChild(ellipsis);
                    }
                }

                // Page numbers
                for (let i = startPage; i <= endPage; i++) {
                    pagination.appendChild(createPaginationItem(i, i, i === data.current_page));
                }

                // Last page and ellipsis if needed
                if (endPage < data.last_page) {
                    if (endPage < data.last_page - 1) {
                        const ellipsis = document.createElement('li');
                        ellipsis.className = 'page-item disabled';
                        const span = document.createElement('span');
                        span.className = 'page-link';
                        span.textContent = '...';
                        ellipsis.appendChild(span);
                        pagination.appendChild(ellipsis);
                    }
                    pagination.appendChild(createPaginationItem(data.last_page, data.last_page, data.last_page === data.current_page));
                }

                // Next button
                if (data.current_page < data.last_page) {
                    pagination.appendChild(createPaginationItem(data.current_page + 1, '»'));
                }

                paginationContainer.appendChild(pagination);

                // Add event listeners to pagination links
                paginationContainer.querySelectorAll('.page-link').forEach(link => {
                    link.addEventListener('click', function(e) {
                        e.preventDefault();
                        const page = parseInt(this.dataset.page);
                        updateTableData(page);
                    });
                });
            }
        } catch (error) {
            console.error('Error rendering pagination:', error);
        }
    }

    // Function to fetch and update table data
    const updateTableData = (page = 1) => {
        // Update current page
        currentPage = page;

        // Build URL with pagination parameters
        const url = `/part-group/filter?page=${page}&site_id=${currentSiteId}&search=${currentSearchTerm}&part_type=${currentPartType}&per_page=${itemsPerPage}`;

        // Show loading indicator
        partTableBody.innerHTML = '<tr><td colspan="9" class="text-center">.</td></tr>';

        fetch(url, {
            headers: {
                "X-Requested-With": "XMLHttpRequest"
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            // Update table rows
            let tableRows = '';
            if (data.parts && data.parts.length > 0) {
                data.parts.forEach(partInventory => {
                    tableRows += `
                        <tr>
                            <td>${partInventory.part.part_code}</td>
                            <td>${partInventory.part.part_name}</td>
                            <td>${partInventory.price || partInventory.part.price}</td>
                            <td>${partInventory.part.bin_location}</td>
                            <td>${partInventory.site.site_name}</td>
                            <td>${partInventory.min_stock}</td>
                            <td>${partInventory.max_stock}</td>
                            <td>${partInventory.stock_quantity}</td>
                            <td>
                                <button class="btn btn-danger btn-sm delete-part-btn" data-part-inventory-id="${partInventory.part_inventory_id}">Delete</button>
                            </td>
                        </tr>
                    `;
                });
            } else {
                tableRows = '<tr><td colspan="9" class="text-center">No parts found.</td></tr>';
            }
            partTableBody.innerHTML = tableRows;

            // Render pagination with server data
            renderPagination({
                current_page: data.current_page,
                last_page: data.last_page,
                total: data.total,
                per_page: data.per_page
            });

            // Reattach delete event listeners
            attachDeleteEventListeners();
        })
        .catch(error => {
            console.error('Error fetching data:', error);
            partTableBody.innerHTML = '<tr><td colspan="9" class="text-center text-danger">Failed to load data. Please try again.</td></tr>';
            Swal.fire({
                icon: "error",
                title: "Terjadi Kesalahan!",
                text: "Gagal memuat data.",
            });
        });
    };

    // Pagination is now handled by the custom pagination function

    // Attach delete event listeners to the delete buttons
    function attachDeleteEventListeners() {
        document.querySelectorAll(".delete-part-btn").forEach((button) => {
            button.addEventListener("click", function (event) {
                event.preventDefault();
                const partInventoryId = this.dataset.partInventoryId;

                Swal.fire({
                    title: "Apakah Anda yakin?",
                    text: "Anda tidak akan dapat mengembalikan ini!",
                    icon: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#3085d6",
                    cancelButtonColor: "#d33",
                    confirmButtonText: "Ya, hapus!",
                    cancelButtonText: "Batal",
                }).then((result) => {
                    if (result.isConfirmed) {
                        fetch(`/part-group/${partInventoryId}`, {
                            method: "DELETE",
                            headers: {
                                "Content-Type": "application/json",
                                "X-CSRF-TOKEN": document.querySelector('meta[name="csrf-token"]').content,
                                "X-Requested-With": "XMLHttpRequest", // Laravel needs this
                            },
                        })
                        .then((response) => response.json())
                        .then((data) => {
                            if (data.success === false) {
                                Swal.fire({
                                    icon: "error",
                                    title: "Gagal!",
                                    text: data.message || "Gagal menghapus inventaris suku cadang.",
                                });
                            } else {
                                Swal.fire({
                                    icon: "success",
                                    title: "Berhasil!",
                                    text: data.message || "Inventaris suku cadang berhasil dihapus.",
                                });
                                // Reload the table data
                                updateTableData();
                            }
                        })
                        .catch((error) => {
                            console.error("Error deleting part inventory:", error);
                            Swal.fire({
                                icon: "error",
                                title: "Terjadi Kesalahan!",
                                text: "Gagal menghapus inventaris suku cadang.",
                            });
                        });
                    }
                });
            });
        });
    }

    // Site Filter
    if (siteFilter) {
        siteFilter.addEventListener("change", function () {
            currentSiteId = this.value;
            // Reset to page 1 when changing filter
            updateTableData(1);
        });
    }

    // Type Filter
    if (typeFilter) {
        typeFilter.addEventListener("change", function () {
            currentPartType = this.value;
            // Reset to page 1 when changing filter
            updateTableData(1);
        });
    }

    // Search Input
    if (searchInput) {
        searchInput.addEventListener("keyup", function () {
            currentSearchTerm = this.value;
            // Reset to page 1 when searching
            updateTableData(1);
        });
    }
    function debounce(func, delay) {
        let timeout;
        return function (...args) {
            const context = this;
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(context, args), delay);
        };
    }
    // Autocomplete
    if (partSearch) {
        partSearch.addEventListener(
            "input",
            debounce(function () {
                const searchTerm = this.value;
                if (searchTerm.length < 2) {
                    suggestions.style.display = "none";
                    return;
                }

                fetch(`/part-group/autocomplete?search=${searchTerm}`)
                    .then((response) => response.json())
                    .then((parts) => {
                        suggestions.innerHTML = "";
                        if (parts.length > 0) {
                            parts.forEach((part) => {
                                const suggestion =
                                    document.createElement("div");
                                suggestion.classList.add("suggestion-item");
                                suggestion.textContent = `${part.part_code} - ${part.part_name}`;
                                suggestion.dataset.partCode = part.part_code;
                                suggestion.dataset.partName = part.part_name;
                                suggestions.appendChild(suggestion);
                            });
                            suggestions.style.display = "block";
                        } else {
                            suggestions.style.display = "none";
                        }
                    })
                    .catch((error) =>
                        Swal.fire({
                            icon: "error",
                            title: "Terjadi Kesalahan!",
                            text: "Gagal mendapatkan saran otomatis.",
                        })
                    );
            }, 300)
        );
    }

    // Suggestion Click
    if (suggestions) {
        suggestions.addEventListener("click", function (event) {
            if (event.target.classList.contains("suggestion-item")) {
                const partCode = event.target.dataset.partCode;
                const partName = event.target.dataset.partName;

                selectedPartCode.value = partCode;
                selectedPartName.value = `${partCode} - ${partName}`;
                suggestions.style.display = "none";
                partSearch.value = ""; // Clear search input after selection
            }
        });
    }

    // Site Checkbox Change
    siteCheckboxes.forEach((checkbox) => {
        checkbox.addEventListener("change", function () {
            const siteId = this.value;
            const siteInputs = document.getElementById(`siteInputs_${siteId}`);
            if (siteInputs) {
                siteInputs.style.display = this.checked ? "block" : "none";
            }
        });
    });

    // Form Submission
    if (addPartForm) {
        addPartForm.addEventListener("submit", function (event) {
            event.preventDefault();

            const partCode = selectedPartCode.value;
            if (!partCode) {
                Swal.fire({
                    icon: "warning",
                    title: "Peringatan!",
                    text: "Silakan pilih suku cadang.",
                });
                return;
            }

            const sitesData = [];
            siteCheckboxes.forEach((checkbox) => {
                if (checkbox.checked) {
                    const siteId = checkbox.value;
                    const sitePartName = document.getElementById(`sitePartName_${siteId}`).value;
                    const minStock = document.getElementById(`minStock_${siteId}`).value;
                    const maxStock = document.getElementById(`maxStock_${siteId}`).value;
                    const price = document.getElementById(`price_${siteId}`).value;

                    sitesData.push({
                        site_id: siteId,
                        site_part_name: sitePartName,
                        min_stock: minStock,
                        price: price,
                        max_stock: maxStock,
                    });
                }
            });

            if (sitesData.length === 0) {
                Swal.fire({
                    icon: "warning",
                    title: "Peringatan!",
                    text: "Silakan pilih setidaknya satu lokasi.",
                });
                return;
            }

            const payload = {
                part_code: partCode,
                sites: sitesData,
            };

            fetch("/part-group", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "X-CSRF-TOKEN": document.querySelector('meta[name="csrf-token"]').content,
                    "X-Requested-With": "XMLHttpRequest",
                },
                body: JSON.stringify(payload),
            })
            .then((response) => {
                if (!response.ok) {
                    return response.json().then(error => {
                        throw new Error(error.message || "Gagal menyimpan data");
                    });
                }
                return response.json();
            })
            .then((data) => {
                Swal.fire({
                    icon: "success",
                    title: "Berhasil!",
                    text: data.message || "Data berhasil disimpan.",
                });
                // Reset form
                addPartForm.reset();
                selectedPartCode.value = "";
                selectedPartName.value = "";
                // Uncheck all site checkboxes and hide their inputs
                siteCheckboxes.forEach((checkbox) => {
                    checkbox.checked = false;
                    const siteInputs = document.getElementById(`siteInputs_${checkbox.value}`);
                    if (siteInputs) {
                        siteInputs.style.display = "none";
                    }
                });
                // Update table data
                updateTableData();
            })
            .catch((error) => {
                console.error("Error saving data:", error);
                Swal.fire({
                    icon: "error",
                    title: "Terjadi Kesalahan!",
                    text: error.message || "Gagal menyimpan data",
                });
            });
        });
    }

    // Initialize pagination with data from server
    if (paginationData && paginationData.current_page) {
        // Render initial pagination
        renderPagination(paginationData);
    } else {
        // If no initial data, fetch from server
        updateTableData(1);
    }

    attachDeleteEventListeners();
});
