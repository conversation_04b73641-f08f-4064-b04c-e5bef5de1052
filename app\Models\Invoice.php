<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Invoice extends Model
{
    protected $fillable = [
        'customer',
        'customer_code',
        'location',
        'no_invoice',
        'po_number',
        'sn',
        'trouble',
        'lokasi',
        'model_unit',
        'hmkm',
        'tanggal_invoice',
        'due_date',
        'ppn',
        'payment_status',
        'payment_date',
        'payment_notes',
        'notes',
        'transfer_to',
        'bank_account',
        'bank_branch',
        'signed_document_path',
        'document_path',
        'status',
        'penawaran_id',
        'site_id',
        'direct_subtotal'
    ];

    protected $casts = [
        'tanggal_invoice' => 'date',
        'due_date' => 'date',
        'payment_date' => 'date',
        'ppn' => 'float'
    ];

    protected $appends = ['invoice_status', 'subtotal', 'tax_amount', 'total_amount', 'unit_list'];

    /**
     * Get the invoice status based on payment status and due date
     */
    public function getInvoiceStatusAttribute()
    {
        try {
            // If payment status is 'Lunas', return 'Lunas'
            if ($this->payment_status === 'Lunas') {
                return 'Lunas';
            }

            // If due date is past and payment status is not 'Lunas',
            // mark as 'Jatuh Tempo'
            if ($this->due_date && $this->due_date < now()) {
                return 'Jatuh Tempo';
            }

            // If no due date is set, use the old logic (30 days from invoice date)
            if (!$this->due_date && $this->tanggal_invoice) {
                $invoiceDate = \Carbon\Carbon::parse($this->attributes['tanggal_invoice']);
                $dueDate = $invoiceDate->addDays(30);
                if ($dueDate < now()) {
                    return 'Jatuh Tempo';
                }
            }

            // Otherwise, return the current payment status
            return $this->payment_status ?? 'Belum Lunas';
        } catch (\Exception $e) {
            \Log::error('Error getting invoice status for invoice ' . $this->id . ': ' . $e->getMessage());
            return $this->payment_status ?? 'Belum Lunas';
        }
    }

    /**
     * Get the unit transactions associated with this invoice
     */
    public function unitTransactions()
    {
        return $this->belongsToMany(UnitTransaction::class, 'invoice_unit_transactions')
            ->withTimestamps();
    }

    /**
     * Calculate the subtotal amount for this invoice
     */
    public function getSubtotalAttribute()
    {
        // If this is a direct invoice with a direct_subtotal value, return that
        if ($this->direct_subtotal !== null && $this->direct_subtotal !== '0.00') {
            // If direct_subtotal is set, return it directly
            return $this->direct_subtotal;
        }

        $subtotal = 0;

        // If this is a penawaran invoice
        if ($this->penawaran_id) {
            try {
                // Load the penawaran with its items if not already loaded
                if (!$this->relationLoaded('penawaran')) {
                    $this->load('penawaran.items');
                } elseif ($this->penawaran && !$this->penawaran->relationLoaded('items')) {
                    $this->penawaran->load('items');
                }

                // Calculate subtotal from penawaran items
                if ($this->penawaran && $this->penawaran->items) {
                    foreach ($this->penawaran->items as $item) {
                        $subtotal += $item->price * $item->quantity;
                    }
                }
            } catch (\Exception $e) {
                // Log the error but don't break the application
                \Log::error('Error loading penawaran relationship for invoice ' . $this->id . ': ' . $e->getMessage());
                // Return 0 as fallback
                return 0;
            }
        } else {
            // For unit transaction invoices
            // Make sure unit transactions are loaded with their parts
            $transactions = $this->unitTransactions()->with('parts')->get();

            foreach ($transactions as $transaction) {
                foreach ($transaction->parts as $part) {
                    // Get price from part inventory (site-specific) instead of unit transaction part
                    $price = $part->price ?? $part->partInventory->price ;
                    $subtotal += $price * $part->quantity;
                }
            }
        }

        return $subtotal;
    }

    /**
     * Calculate the tax amount for this invoice
     */
    public function getTaxAmountAttribute()
    {
        try {
            $subtotal = $this->subtotal ?? 0;
            $ppn = $this->ppn ?? 0;
            return $subtotal * $ppn;
        } catch (\Exception $e) {
            \Log::error('Error calculating tax amount for invoice ' . $this->id . ': ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Calculate the total amount after tax for this invoice
     */
    public function getTotalAmountAttribute()
    {
        try {
            $subtotal = $this->subtotal ?? 0;
            $taxAmount = $this->tax_amount ?? 0;
            return $subtotal + $taxAmount;
        } catch (\Exception $e) {
            \Log::error('Error calculating total amount for invoice ' . $this->id . ': ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get a comma-separated list of unit codes for this invoice
     */
    public function getUnitListAttribute()
    {
        try {
            // Ensure unitTransactions relationship is loaded
            if (!$this->relationLoaded('unitTransactions')) {
                $this->load('unitTransactions');
            }

            // If this is a direct invoice (no unit transactions and no penawaran)
            if (!$this->unitTransactions->count() && !$this->penawaran_id && isset($this->attributes['unit'])) {
                return $this->attributes['unit'];
            }

            // If this is a penawaran invoice
            if ($this->penawaran_id) {
                try {
                    // Load the penawaran if not already loaded
                    if (!$this->relationLoaded('penawaran')) {
                        $this->load('penawaran');
                    }

                    // Return the penawaran number as the "unit list"
                    return $this->penawaran ? 'Penawaran: ' . $this->penawaran->nomor : 'Penawaran';
                } catch (\Exception $e) {
                    \Log::error('Error loading penawaran for unit list in invoice ' . $this->id . ': ' . $e->getMessage());
                    return 'Penawaran';
                }
            }

            // For unit transaction invoices
            return $this->unitTransactions->map(function($transaction) {
                return $transaction->unit ? $transaction->unit->unit_code : 'N/A';
            })->implode(', ');
        } catch (\Exception $e) {
            \Log::error('Error getting unit list for invoice ' . $this->id . ': ' . $e->getMessage());
            return 'N/A';
        }
    }

    /**
     * Get the penawaran associated with this invoice
     */
    public function penawaran()
    {
        return $this->belongsTo(Penawaran::class);
    }

    /**
     * Get the site associated with this invoice
     */
    public function site()
    {
        return $this->belongsTo(Site::class);
    }

    /**
     * Get the customer associated with this invoice
     */
    public function customerSales()
    {
        return $this->belongsTo(CustomerSales::class, 'customer_code', 'code');
    }

    /**
     * Get the manual invoice parts associated with this invoice
     */
    public function manualInvoiceParts()
    {
        return $this->hasMany(ManualInvoicePart::class);
    }
}
