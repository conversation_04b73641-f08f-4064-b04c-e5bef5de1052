<?php

namespace Database\Factories;

use App\Models\PartInventory;
use Illuminate\Database\Eloquent\Factories\Factory;

class PartInventoryFactory extends Factory
{
    protected $model = PartInventory::class;

    public function definition()
    {
        return [
            'part_code' => 'TEST001', // Will be overridden in tests
            'site_part_name' => $this->faker->words(3, true),
            'site_id' => 'TST',
            'price' => $this->faker->numberBetween(50000, 500000),
            'priority' => false,
            'oum' => 'EA',
            'min_stock' => $this->faker->numberBetween(1, 10),
            'max_stock' => $this->faker->numberBetween(20, 100),
            'stock_quantity' => $this->faker->numberBetween(5, 50),
        ];
    }
}
