<?php

namespace App\Http\Controllers\Sites;
use App\Http\Controllers\Controller;
use App\Models\PartInventory;
use App\Models\SiteOutStock;
use App\Models\SiteInStock;
use App\Helpers\ClassifiedPart;
use Illuminate\Http\Request;
use Carbon\Carbon;

class PartAnalysisController extends Controller
{

    /**
     * Display the part analysis page
     */
    public function index(Request $request)
    {
        // Get current site from session
        $siteId = session('site_id');
        
        // Set default values
        $startDate = $request->get('start_date', Carbon::now()->subMonths(3)->format('Y-m-d'));
        $endDate = $request->get('end_date', Carbon::now()->format('Y-m-d'));
        
        $analysisData = [];
        
        if ($request->ajax()) {
            $analysisData = $this->getAnalysisData($siteId, $startDate, $endDate);
            return response()->json($analysisData);
        }
        // For initial page load, get analysis data
        $analysisData = $this->getAnalysisData($siteId, $startDate, $endDate);
        return view('sites.part-analysis', compact('siteId', 'startDate', 'endDate', 'analysisData'));
    }
    private function getAnalysisData($siteId, $startDate, $endDate)
    {
        try {
            $start = Carbon::parse($startDate);
            $end = Carbon::parse($endDate);
        $parts = PartInventory::with('part')
            ->where('site_id', $siteId)
            ->whereHas('part')
            ->get();
        $analysisResults = [];
        foreach ($parts as $partInventory) {
            $part = $partInventory->part;
            if (!$part) {
                continue;
            }
            $stockOutHistory = $this->getStockOutHistory($partInventory->part_inventory_id, $start, $end);
            $stockInHistory = $this->getStockInHistory($partInventory->part_inventory_id, $start, $end);
            $totalInflow = $this->getTotalInflow($partInventory->part_inventory_id, $start, $end);
            $totalOutflow = $this->getTotalOutflow($partInventory->part_inventory_id, $start, $end);
            $currentStock = $partInventory->stock_quantity;

            // Status
            $status = $currentStock > 0 ? 'Ready' : 'Not Ready';
            $classification = ClassifiedPart::classifyWithInOut($stockInHistory, $stockOutHistory, $currentStock);
            $analysisResults[] = [
                'part_name' => $part->part_name,
                'part_code' => $part->part_code,
                'current_stock' => $currentStock,
                'in_stock' => $totalInflow,
                'out_stock' => $totalOutflow,
                'status' => $status,
                'analysis_description' => $classification,
                'stock_out_history' => $stockOutHistory,
                'data_points' => count($stockOutHistory)
            ];
        }
        
        return [
            'data' => $analysisResults,
            'grouping' => 'week', // Always weekly grouping
            'period_start' => $start->format('d/m/Y'),
            'period_end' => $end->format('d/m/Y'),
            'total_parts' => count($analysisResults)
        ];
        } catch (\Exception $e) {
            return [
                'data' => [],
                'grouping' => 'week', // Always weekly grouping
                'period_start' => '',
                'period_end' => '',
                'total_parts' => 0,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Get stock out history grouped by week only
     */
    private function getStockOutHistory($partInventoryId, $start, $end)
    {
        $query = SiteOutStock::where('part_inventory_id', $partInventoryId)
            ->whereBetween('date_out', [$start, $end]);

        // Always group by week
        $stockOuts = $query->selectRaw('YEAR(date_out) as year, WEEK(date_out) as week, SUM(quantity) as total_quantity')
            ->groupBy('year', 'week')
            ->orderBy('year')
            ->orderBy('week')
            ->get();

        // Create array with all weeks in the range
        $history = [];
        $current = $start->copy()->startOfWeek();

        while ($current <= $end) {
            $year = $current->year;
            $week = $current->week;

            $found = $stockOuts->first(function($item) use ($year, $week) {
                return $item->year == $year && $item->week == $week;
            });

            $history[] = $found ? (int)$found->total_quantity : 0;
            $current->addWeek();
        }

        return $history;
    }

    /**
     * Get stock in history grouped by week only
     */
    private function getStockInHistory($partInventoryId, $start, $end)
    {
        $query = SiteInStock::where('part_inventory_id', $partInventoryId)
            ->whereBetween('date_in', [$start, $end]);

        // Always group by week
        $stockIns = $query->selectRaw('YEAR(date_in) as year, WEEK(date_in) as week, SUM(quantity) as total_quantity')
            ->groupBy('year', 'week')
            ->orderBy('year')
            ->orderBy('week')
            ->get();

        // Create array with all weeks in the range
        $history = [];
        $current = $start->copy()->startOfWeek();

        while ($current <= $end) {
            $year = $current->year;
            $week = $current->week;

            $found = $stockIns->first(function($item) use ($year, $week) {
                return $item->year == $year && $item->week == $week;
            });

            $history[] = $found ? (int)$found->total_quantity : 0;
            $current->addWeek();
        }

        return $history;
    }

    /**
     * Get total inflow during the period
     */
    private function getTotalInflow($partInventoryId, $start, $end)
    {
        return SiteInStock::where('part_inventory_id', $partInventoryId)
            ->whereBetween('date_in', [$start, $end])
            ->sum('quantity');
    }
    
    /**
     * Get total outflow during the period
     */
    private function getTotalOutflow($partInventoryId, $start, $end)
    {
        return SiteOutStock::where('part_inventory_id', $partInventoryId)
            ->whereBetween('date_out', [$start, $end])
            ->sum('quantity');
    }
}