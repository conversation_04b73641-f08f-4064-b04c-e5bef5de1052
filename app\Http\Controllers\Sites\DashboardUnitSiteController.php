<?php

namespace App\Http\Controllers\Sites;

use App\Http\Controllers\Controller;
use App\Models\Unit;
use App\Models\DailyReport;
use App\Models\Backlog;
use App\Models\Job;
use App\Models\SiteOutStock;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class DashboardUnitSiteController extends Controller
{
    /**
     * Display the dashboard unit site page
     */
    public function index()
    {
        return view('sites.dashboard-unit-site');
    }

    /**
     * Search units for AJAX autocomplete
     */
    public function searchUnits(Request $request)
    {
        $query = $request->get('query', '');
        $siteId = session('site_id');

        $units = Unit::where('site_id', $siteId)
            ->where(function($q) use ($query) {
                $q->where('unit_code', 'LIKE', "%{$query}%")
                  ->orWhere('unit_model', 'LIKE', "%{$query}%")
                  ->orWhere('unit_type', 'LIKE', "%{$query}%");
            })
            ->whereHas('dailyReports') // Only units with daily reports
            ->select('id', 'unit_code', 'unit_model', 'unit_type', 'site_id')
            ->limit(10)
            ->get();

        return response()->json([
            'success' => true,
            'data' => $units
        ]);
    }

    /**
     * Get dashboard data for selected unit
     */
    public function getDashboardData(Request $request)
    {
        $unitId = $request->get('unit_id');
        
        if (!$unitId) {
            return response()->json([
                'success' => false,
                'message' => 'Unit ID is required'
            ], 400);
        }

        $unit = Unit::with(['site'])->find($unitId);
        
        if (!$unit) {
            return response()->json([
                'success' => false,
                'message' => 'Unit not found'
            ], 404);
        }

        // Check if unit belongs to current site
        $siteId = session('site_id');
        if ($unit->site_id !== $siteId) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access to unit'
            ], 403);
        }

        $data = [
            'unit' => $unit,
            'daily_reports' => $this->getDailyReports($unitId),
            'latest_report' => $this->getLatestReport($unitId),
            'open_backlogs' => $this->getOpenBacklogs($unit->unit_code),
            'part_usage_stats' => $this->getPartUsageStats($unitId),
            'monthly_usage_stats' => $this->getMonthlyUsageStats($unitId),
            'service_component_stats' => $this->getServiceComponentStats($unitId)
        ];

        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    }

    /**
     * Get daily reports for unit
     */
    private function getDailyReports($unitId)
    {
        return DailyReport::where('unit_id', $unitId)
            ->with(['jobs'])
            ->orderBy('date_in', 'desc')
            ->limit(20)
            ->get()
            ->map(function($report) {
                return [
                    'daily_report_id' => $report->daily_report_id,
                    'date_in' => $report->date_in->format('d/m/Y'),
                    'hm' => $report->hm,
                    'problem' => $report->problem,
                    'problem_description' => $report->problem_description,
                    'shift' => $report->shift,
                    'jobs' => $report->jobs->pluck('job_description')->toArray()
                ];
            });
    }

    /**
     * Get latest daily report
     */
    private function getLatestReport($unitId)
    {
        $report = DailyReport::where('unit_id', $unitId)
            ->with(['jobs'])
            ->orderBy('date_in', 'desc')
            ->first();

        if (!$report) {
            return null;
        }

        // Calculate DT Time if both hour_in and hour_out exist
        $dtTime = null;
        if ($report->hour_in && $report->hour_out) {
            try {
                $hourIn = Carbon::createFromFormat('H:i', $report->hour_in);
                $hourOut = Carbon::createFromFormat('H:i', $report->hour_out);
                
                if ($hourOut->lt($hourIn)) {
                    $hourOut->addDay();
                }
                
                $dtTime = $hourOut->diffInMinutes($hourIn);
            } catch (\Exception $e) {
                $dtTime = null;
            }
        }

        return [
            'daily_report_id' => $report->daily_report_id,
            'date_in' => $report->date_in->format('d/m/Y'),
            'hm' => $report->hm,
            'problem' => $report->problem,
            'problem_description' => $report->problem_description,
            'shift' => $report->shift,
            'hour_in' => $report->hour_in,
            'hour_out' => $report->hour_out,
            'dt_time' => $dtTime,
            'jobs' => $report->jobs->pluck('job_description')->toArray(),
            'plan_fix' => $report->plan_fix,
            'plan_rekomen' => $report->plan_rekomen
        ];
    }

    /**
     * Get open backlogs for unit
     */
    private function getOpenBacklogs($unitCode)
    {
        return Backlog::where('unit_code', $unitCode)
            ->where('status', 'OPEN')
            ->with(['backlogParts.part'])
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function($backlog) {
                return [
                    'id' => $backlog->id,
                    'problem_description' => $backlog->problem_description,
                    'backlog_job' => $backlog->backlog_job,
                    'hm_found' => $backlog->hm_found,
                    'plan_hm' => $backlog->plan_hm,
                    'created_at' => $backlog->created_at->format('d/m/Y'),
                    'plan_pull_date' => $backlog->plan_pull_date ? $backlog->plan_pull_date->format('d/m/Y H:i') : null,
                    'parts' => $backlog->backlogParts->map(function($bp) {
                        return [
                            'part_code' => $bp->part_code,
                            'part_name' => $bp->part->part_name ?? 'Unknown',
                            'quantity' => $bp->quantity
                        ];
                    })
                ];
            });
    }

    /**
     * Get part usage statistics
     */
    private function getPartUsageStats($unitId)
    {
        $stats = SiteOutStock::where('unit_id', $unitId)
            ->with(['partInventory.part'])
            ->select('part_inventory_id', DB::raw('SUM(quantity) as total_quantity'))
            ->groupBy('part_inventory_id')
            ->orderBy('total_quantity', 'desc')
            ->limit(10)
            ->get()
            ->map(function($stat) {
                return [
                    'part_code' => $stat->partInventory->part->part_code ?? 'Unknown',
                    'part_name' => $stat->partInventory->part->part_name ?? 'Unknown',
                    'total_quantity' => $stat->total_quantity
                ];
            });

        return $stats;
    }

    /**
     * Get monthly usage statistics
     */
    private function getMonthlyUsageStats($unitId)
    {
        $stats = DailyReport::where('unit_id', $unitId)
            ->select(
                DB::raw('YEAR(date_in) as year'),
                DB::raw('MONTH(date_in) as month'),
                DB::raw('COUNT(*) as report_count')
            )
            ->groupBy('year', 'month')
            ->orderBy('year', 'desc')
            ->orderBy('month', 'desc')
            ->limit(12)
            ->get()
            ->map(function($stat) {
                return [
                    'period' => Carbon::create($stat->year, $stat->month)->format('M Y'),
                    'count' => $stat->report_count
                ];
            });

        return $stats->reverse()->values();
    }

    /**
     * Get service component statistics
     */
    private function getServiceComponentStats($unitId)
    {
        $stats = DB::table('daily_reports as dr')
            ->join('daily_report_jobs as drj', 'dr.daily_report_id', '=', 'drj.daily_report_id')
            ->join('daily_report_job_descriptions as job', 'drj.job_description_id', '=', 'job.job_description_id')
            ->where('dr.unit_id', $unitId)
            ->select('job.job_description', DB::raw('COUNT(*) as usage_count'))
            ->groupBy('job.job_description')
            ->orderBy('usage_count', 'desc')
            ->limit(10)
            ->get()
            ->map(function($stat) {
                return [
                    'component' => $stat->job_description,
                    'count' => $stat->usage_count
                ];
            });

        return $stats;
    }

    /**
     * Get detailed daily report
     */
    public function getDailyReportDetail(Request $request)
    {
        $reportId = $request->get('report_id');
        
        $report = DailyReport::with(['unit', 'jobs'])
            ->where('daily_report_id', $reportId)
            ->first();

        if (!$report) {
            return response()->json([
                'success' => false,
                'message' => 'Daily report not found'
            ], 404);
        }

        // Check if report belongs to current site
        if ($report->unit->site_id !== session('site_id')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access to report'
            ], 403);
        }

        // Calculate DT Time
        $dtTime = null;
        if ($report->hour_in && $report->hour_out) {
            try {
                $hourIn = Carbon::createFromFormat('H:i', $report->hour_in);
                $hourOut = Carbon::createFromFormat('H:i', $report->hour_out);
                
                if ($hourOut->lt($hourIn)) {
                    $hourOut->addDay();
                }
                
                $dtTime = $hourOut->diffInMinutes($hourIn);
            } catch (\Exception $e) {
                $dtTime = null;
            }
        }

        $data = [
            'daily_report_id' => $report->daily_report_id,
            'unit_code' => $report->unit->unit_code,
            'date_in' => $report->date_in->format('d/m/Y'),
            'hm' => $report->hm,
            'problem' => $report->problem,
            'problem_component' => $report->problem_component,
            'problem_description' => $report->problem_description,
            'shift' => $report->shift,
            'hour_in' => $report->hour_in,
            'hour_out' => $report->hour_out,
            'dt_time' => $dtTime,
            'plan_fix' => $report->plan_fix,
            'plan_rekomen' => $report->plan_rekomen,
            'lifetime_component' => $report->lifetime_component,
            'jobs' => $report->jobs->map(function($job) {
                return [
                    'job_description_id' => $job->job_description_id,
                    'job_description' => $job->job_description,
                    'highlight' => $job->highlight
                ];
            })
        ];

        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    }
}
