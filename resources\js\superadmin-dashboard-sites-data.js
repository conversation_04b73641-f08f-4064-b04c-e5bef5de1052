document.addEventListener("DOMContentLoaded", function () {
    loadSitesData();
});
function loadSitesData() {
    // Show skeleton loader
    const contentWrapper = document.querySelector(".content-wrapper");
    const skeletonLoader = document.getElementById("skeleton-loader");

    if (contentWrapper && skeletonLoader) {
        contentWrapper.style.display = "none";
        skeletonLoader.style.display = "block";
    }

    // Get the date range and filters
    const startDateInput = document.getElementById("start-date");
    const endDateInput = document.getElementById("end-date");
    const divisionFilter = document.getElementById("division-filter");
    const siteFilter = document.getElementById("site-filter");

    const startDate = startDateInput ? startDateInput.value : "";
    const endDate = endDateInput ? endDateInput.value : "";
    const selectedDivision = divisionFilter ? divisionFilter.value : "";
    const selectedSite = siteFilter ? siteFilter.value : "";

    // Build query parameters
    const params = new URLSearchParams();
    if (startDate) params.append("start_date", startDate);
    if (endDate) params.append("end_date", endDate);
    if (selectedDivision) params.append("division", selectedDivision);
    if (selectedSite) params.append("site", selectedSite);
    fetch(`/superadmin/sites-data?${params.toString()}`)
        .then((response) => {
            if (!response.ok) {
                return response
                    .json()
                    .then((errorData) => {
                        throw new Error(
                            errorData.error || "Network response was not ok"
                        );
                    })
                    .catch((e) => {
                        if (e instanceof SyntaxError) {
                            // If the response is not valid JSON
                            throw new Error(
                                `Server error: ${response.status} ${response.statusText}`
                            );
                        }
                        throw e;
                    });
            }
            return response.json();
        })
        .then((data) => {
            // Check if the response contains an error
            if (data.error) {
                throw new Error(data.error);
            }
            const sitesDataElement = document.getElementById("sitesData");
            if (sitesDataElement) {
                sitesDataElement.setAttribute(
                    "data-sites-data",
                    JSON.stringify(data)
                );

                // Dispatch a custom event to notify other scripts that the data is loaded
                const event = new CustomEvent("sitesDataLoaded", {
                    detail: data,
                });
                document.dispatchEvent(event);
            }

            // Hide skeleton loader, show content
            if (contentWrapper && skeletonLoader) {
                skeletonLoader.style.display = "none";
                contentWrapper.style.display = "block";
            }
        })
        .catch((error) => {
            if (contentWrapper) {
                contentWrapper.innerHTML = `
                    <div class="container-fluid py-4">
                        <div class="alert alert-danger">
                            <i class="mdi mdi-alert-circle-outline me-2"></i>
                            Gagal memuat data: ${error.message}
                            <div class="mt-2">
                                <button class="btn btn-sm btn-outline-danger" onclick="loadSitesData()">
                                    <i class="mdi mdi-refresh me-1"></i> Coba Lagi
                                </button>
                            </div>
                        </div>
                    </div>
                `;
                contentWrapper.style.display = "block";
            }

            // Hide skeleton loader
            if (skeletonLoader) {
                skeletonLoader.style.display = "none";
            }
        });
}

// Listen for date range change events
document.addEventListener("dateRangeChanged", function () {
    // Reload sites data when date range changes
    loadSitesData();
});

// Listen for filter change events
// document.addEventListener("DOMContentLoaded", function () {
//     // Add event listeners to filter elements
//     const divisionFilter = document.getElementById("division-filter");
//     const siteFilter = document.getElementById("site-filter");
//     const searchBtn = document.getElementById("searchBtns");

//     if (searchBtn) {
//         // The search button is handled by the form submission
//         // No need to add event listener here
//     }
// });

// Show skeleton loader when page starts loading
window.addEventListener("load", function () {
    const skeletonLoader = document.getElementById("skeleton-loader");
    const contentWrapper = document.querySelector(".content-wrapper");

    // Hide skeleton loader and show content when page is fully loaded
    skeletonLoader.style.display = "none";
    contentWrapper.style.display = "block";
});

document.addEventListener("DOMContentLoaded", function () {
    const startDateInput = document.getElementById("start-date");
    const endDateInput = document.getElementById("end-date");
    const dateRangeForm = document.getElementById("dateRangeForm");
    const contentWrapper = document.querySelector(".content-wrapper");
    const skeletonLoader = document.getElementById("skeleton-loader");

    // Set default date range if not already set
    if (!startDateInput.value || !endDateInput.value) {
        const today = new Date();
        const firstDayOfMonth = new Date(
            today.getFullYear(),
            today.getMonth(),
            1
        );

        // Format dates as YYYY-MM-DD
        startDateInput.value = formatDate(firstDayOfMonth);
        endDateInput.value = formatDate(today);
    }

    // Helper function to format date as YYYY-MM-DD
    function formatDate(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        return `${year}-${month}-${day}`;
    }

    // Initialize filter form
    const mainFilterForm = document.getElementById("mainFilterForm");
    const divisionFilter = document.getElementById("division-filter");
    const siteFilter = document.getElementById("site-filter");
    

    // Add event listener to search button
    // document
    // .getElementById("searchBtns")
    // .addEventListener("click", function (e) {
    //     e.preventDefault();

    //     const startDateValue = startDateInput.value;
    //     const endDateValue = endDateInput.value;

    //     // Hapus input tersembunyi sebelumnya
    //     mainFilterForm.querySelectorAll('input[name="start_date"], input[name="end_date"]').forEach(el => el.remove());

    //     // Tambahkan input tersembunyi baru
    //     if (startDateValue) {
    //         const hiddenStart = document.createElement("input");
    //         hiddenStart.type = "hidden";
    //         hiddenStart.name = "start_date";
    //         hiddenStart.value = startDateValue;
    //         mainFilterForm.appendChild(hiddenStart);
    //     }

    //     if (endDateValue) {
    //         const hiddenEnd = document.createElement("input");
    //         hiddenEnd.type = "hidden";
    //         hiddenEnd.name = "end_date";
    //         hiddenEnd.value = endDateValue;
    //         mainFilterForm.appendChild(hiddenEnd);
    //     }

    //     document.dispatchEvent(
    //         new CustomEvent("dateRangeChanged", {
    //             detail: {
    //                 startDate: startDateValue,
    //                 endDate: endDateValue,
    //                 division: divisionFilter.value,
    //                 site: siteFilter.value,
    //             },
    //         })
    //     );

    //     contentWrapper.style.display = "none";
    //     skeletonLoader.style.display = "block";

    //     loadSitesData(); // <-- AJAX call instead of form submission
    // });

});
