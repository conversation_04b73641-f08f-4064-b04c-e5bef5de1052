<?php

namespace Database\Factories;

use App\Models\UnitTransaction;
use Illuminate\Database\Eloquent\Factories\Factory;

class UnitTransactionFactory extends Factory
{
    protected $model = UnitTransaction::class;

    public function definition()
    {
        return [
            'unit_id' => 1, // Assuming unit exists
            'site_id' => 'TST',
            'status' => 'Active',
            'mr_date' => $this->faker->date(),
            'wo_number' => $this->faker->regexify('WO-[0-9]{6}'),
            'do_number' => $this->faker->regexify('DO-[0-9]{6}'),
            'do_date' => $this->faker->date(),
            'po_date' => $this->faker->date(),
            'tanggalstart' => $this->faker->date(),
            'tanggalend' => $this->faker->dateTimeBetween('+1 day', '+1 week'),
            'noireq' => $this->faker->regexify('REQ-[0-9]{6}'),
            'noBA' => $this->faker->regexify('BA-[0-9]{6}'),
            'pekerjaan' => $this->faker->sentence,
            'HMKM' => $this->faker->numberBetween(1000, 10000),
            'SHIFT' => $this->faker->randomElement(['1', '2', '3']),
            'LOKASI' => $this->faker->address,
            'po_number' => $this->faker->regexify('PO-[0-9]{6}'),
            'issue_nomor' => $this->faker->regexify('ISS-[0-9]{6}'),
            'remarks' => $this->faker->sentence,
            'sales_notes' => $this->faker->sentence,
            'noSPB' => $this->faker->regexify('SPB-[0-9]{6}'),
            'contact' => $this->faker->name,
            'phone' => $this->faker->phoneNumber,
            'customer' => $this->faker->company,
            'sitework' => $this->faker->city,
            'attachment_path' => null,
            'actual_out_date' => null,
        ];
    }
}
