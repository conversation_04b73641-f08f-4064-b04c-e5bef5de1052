<?php

namespace App\Models;
use Illuminate\Database\Eloquent\Model;
use App\Models\Part;
use App\Models\PartInventory;
use App\Models\SiteOutStock;
use App\Models\UnitTransaction;
use Illuminate\Database\Eloquent\Builder;

class UnitTransactionPart extends Model
{
    protected $fillable = [
        'unit_transaction_id',
        'part_inventory_id',
        'quantity',
        'part_name',
        'price',
        'eum'
    ];

    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    protected static function booted()
    {
        static::addGlobalScope('unique_part_per_transaction', function (Builder $builder) {
            $builder->distinct('unit_transaction_id', 'part_inventory_id');
        });
    }

    protected $casts = [
        'price' => 'float',
        'quantity' => 'float'
    ];

    public function unitTransaction()
    {
        return $this->belongsTo(UnitTransaction::class);
    }

    public function partInventory()
    {
        return $this->belongsTo(PartInventory::class, 'part_inventory_id', 'part_inventory_id');
    }

    public function siteOutStock()
    {
        return $this->hasOne(SiteOutStock::class, 'unit_transaction_parts_id');
    }

    public function part()
    {
        return $this->hasOneThrough(
            Part::class,
            PartInventory::class,
            'part_inventory_id', // Foreign key on PartInventory table
            'part_code', // Foreign key on Part table
            'part_inventory_id', // Local key on UnitTransactionPart table
            'part_code' // Local key on PartInventory table
        );
    }
}